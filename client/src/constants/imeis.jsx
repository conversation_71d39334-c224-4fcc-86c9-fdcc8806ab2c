/**
 * IMEI formatter component
 * - Formats IMEI according to standard AA BBBBBB CCCCCC D format
 * - Maintains natural text selection (double/triple click works as expected)
 * - No validation or additional metadata
 */
export const RobustIMEI = ({ imei }) => {
  // Parse IMEI into parts using regex
  const formatIMEI = (imeiString) => {
  if (!imeiString || typeof imeiString !== 'string') {
    return [''];
  }

  // Clean the input - remove non-digits
  const cleanImei = imeiString.replace(/\D/g, '');

  // Use regex to split the IMEI into parts according to format: AA BBBBBB CCCCCC D
  // ^(\d{2})(\d{6})?(\d{6})?(\d{1})?$
  // Group 1: First 2 digits (AA) - Reporting Body Identifier
  // Group 2: Next 6 digits (BBBBBB) - Part of TAC
  // Group 3: Next 6 digits (CCCCCC) - Serial Number
  // Group 4: Last digit (D) - Check Digit

  const imeiRegex = /^(\d{2})(\d{6})?(\d{6})?(\d{1})?$/;
  const match = cleanImei.match(imeiRegex);

  if (!match) {
    // If regex doesn't match (incomplete IMEI), fall back to basic chunking
    // This handles partial IMEIs gracefully
    return [cleanImei];
  }

  // Filter out undefined groups and return the parts
  return match.slice(1).filter(part => part && part.length > 0);
  };

  const imeiParts = formatIMEI(imei);

  return (
    <span className="font-mono text-sm">
      {imeiParts.map((part, index) => (
        <span key={index} className={index > 0 ? "ml-1" : ""}>
          {part}
        </span>
      ))}
    </span>
  );
};

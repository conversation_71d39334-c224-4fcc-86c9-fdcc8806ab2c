import React from 'react';

/**
 * Compact ICCID display
 */
export const RobustICCID = ({ iccid, showDetails = true }) => {
  // Parse ICCID
  const parsedData = parseICCID(iccid);

  if (!parsedData.valid) {
    return (
      <div className="inline-flex items-center">
        <div className="font-mono text-sm">
          <span className="text-red-600">{iccid || "Invalid ICCID"}</span>
        </div>
        <span className="ml-2 text-xs text-red-600">{parsedData.error}</span>
      </div>
    );
  }

  // ICCID is valid
  return (
    <div className="inline-flex items-center">
      {/* ICCID with special spacing */}
      <div>
        {parsedData.formattedParts.map((part, index) => (
          <span
            key={index}
            className={`${index > 0 ? "ml-1" : ""}`}
          >
            {part}
          </span>
        ))}
      </div>
      {showDetails && (
        <>
          {/* Separator */}
          <span className="mx-1 text-gray-300">|</span>

          {/* Carrier name with dot indicator */}
          <div className="flex items-center">
            <span className="text-xs font-medium">{parsedData.carrierName}</span>
          </div>

          {/* Country */}
          <span className="mx-1 text-gray-300">|</span>
          <span className="text-xs text-BRAND_UNKNOWN-600">{parsedData.country || "Unknown country"}</span>
        </>
      )}
    </div>
  );
};

/**
 * Parse ICCID and extract all needed information
 */
function parseICCID(iccid) {
  // Handle null/undefined/empty
  if (!iccid) {
    return {
      valid: false,
      error: "ICCID is missing"
    };
  }

  // Check if the input is an error message
  if (typeof iccid === 'string' && iccid.toLowerCase().includes('error')) {
    return (
      <div className="inline-flex items-center">
        <div className="text-sm bg-red-50 px-2 py-1 rounded">
          <span className="text-red-700">Device reported error: '{iccid}'</span>
        </div>
      </div>
    );
  }

  // Clean any existing spaces
  const cleanICCID = iccid.replace(/\s+/g, '');

  // Basic validation
  if (/[^0-9]/.test(cleanICCID)) {
    return {
      valid: false,
      error: "Non-numeric characters"
    };
  }

  if (cleanICCID.length < 18 || cleanICCID.length > 22) {
    return {
      valid: false,
      error: `Invalid length: ${cleanICCID.length} digits (should be 18-22)`
    };
  }

  // Extract first parts to determine format
  const industryID = cleanICCID.substring(0, 2);

  // Verify valid industry ID for telecom
  if (industryID !== "89") {
    return {
      valid: false,
      error: `Invalid industry identifier: ${industryID} (should be 89 for telecom)`
    };
  }

  // Detect country and format
  let countryCode, providerCode, format, formattedParts;
  let country = "Unknown";

  // Check for Canadian format (302 country code)
  if (cleanICCID.substring(2, 5) === "302") {
    countryCode = "302";
    providerCode = cleanICCID.substring(5, 8); // Canadian provider codes are typically 2-3 digits
    country = "CA";
    format = "canadian";
    formattedParts = formatCanadianICCID(cleanICCID);
  }
  // Check for US standard format (01 country code)
  else if (cleanICCID.substring(2, 4) === "01") {
    countryCode = "01";
    providerCode = cleanICCID.substring(4, 7);
    country = "US";
    format = "standard";
    formattedParts = formatWithStandardSpacing(cleanICCID);
  }
  // Check for Sprint/T-Mobile alternative format (31 code)
  else if (cleanICCID.substring(2, 4) === "31") {
    countryCode = "31";
    providerCode = cleanICCID.substring(4, 7);
    country = "US";
    format = "sprint_alt";
    formattedParts = formatWithAlternateSpacing(cleanICCID);
  }
  // Check for Verizon alternative format (14 code)
  else if (cleanICCID.substring(2, 4) === "14") {
    countryCode = "14";
    providerCode = cleanICCID.substring(4, 7);
    country = "US";
    format = "verizon_alt";
    formattedParts = formatWithAlternateSpacing(cleanICCID);
  }
  // Unknown format but try standard anyway
  else {
    countryCode = cleanICCID.substring(2, 4);
    providerCode = cleanICCID.substring(4, 7);
    format = "unknown";
    formattedParts = formatWithStandardSpacing(cleanICCID);
  }

  // Brand color constants
  const BRAND_TMOBILE = "#E20074"; // Magenta (safe)
  const BRAND_ATT = "#FF6900";     // AT&T orange
  const BRAND_VERIZON = "#000000"; // Verizon black
  const BRAND_TELUS = "#4B286D";   // TELUS purple
  const BRAND_UNKNOWN = "#666666";

  // Carrier information
  let carrierName = "Unknown carrier";
  let carrierColor = BRAND_UNKNOWN;

  // US Carriers
  if (country === "US") {
    // AT&T family
    if ((countryCode === "01" && providerCode === "310") ||
        (countryCode === "01" && providerCode === "410") ||
        (countryCode === "01" && providerCode === "030")) {
      carrierName = "AT&T";
      carrierColor = BRAND_ATT;
    }
    else if (countryCode === "01" && providerCode === "150") {
      carrierName = "AT&T IoT";
      carrierColor = BRAND_ATT;
    }
    // Verizon family
    else if ((countryCode === "01" && providerCode === "004") ||
             (countryCode === "14" && providerCode.startsWith("8"))) {
      carrierName = "Verizon";
      carrierColor = BRAND_VERIZON;
    }
    else if (countryCode === "01" && providerCode === "012") {
      carrierName = "Verizon IoT";
      carrierColor = BRAND_VERIZON;
    }
    // T-Mobile family
    else if (countryCode === "01" && providerCode === "260") {
      carrierName = "T-Mobile";
      carrierColor = BRAND_TMOBILE;
    }
    else if (countryCode === "01" && providerCode === "240") {
      carrierName = "T-Mobile IoT";
      carrierColor = BRAND_TMOBILE;
    }
    // Sprint (now T-Mobile)
    else if (countryCode === "31") {
      carrierName = "T-Mobile (Sprint)";
      carrierColor = BRAND_TMOBILE;
    }
  }
  // Canadian Carriers
  else if (country === "CA") {
    if (providerCode === "220") {
      carrierName = "Telus";
      carrierColor = BRAND_TELUS;
    }
    // We can add other Canadian carriers here if needed in the future
  }

  // Return complete parsed information
  return {
    valid: true,
    industryID,
    countryCode,
    providerCode,
    carrierName,
    carrierColor,
    country,
    format,
    formattedParts
  };
}

/**
 * Format an ICCID with standard spacing (2-2-3-3-4-4-1+)
 */
function formatWithStandardSpacing(iccid) {
  const clean = iccid.replace(/\s+/g, '');

  return [
    clean.substring(0, 2),         // Industry (89)
    clean.substring(2, 4),         // Country (01)
    clean.substring(4, 7),         // Provider (310)
    clean.substring(7, 10),        // First 3 of unique ID
    clean.substring(10, 14),       // Next 4 of unique ID
    clean.substring(14, 18),       // Next 4 of unique ID
    clean.substring(18)            // Check digit(s)
  ];
}

/**
 * Format an ICCID with alternate spacing for special formats
 */
function formatWithAlternateSpacing(iccid) {
  const clean = iccid.replace(/\s+/g, '');

  // Adjust this based on which alternate format we're using
  return [
    clean.substring(0, 2),         // Industry (89)
    clean.substring(2, 4),         // Country (14/31)
    clean.substring(4, 7),         // Provider
    clean.substring(7, 11),        // First 4 of unique ID
    clean.substring(11, 15),       // Next 4 of unique ID
    clean.substring(15, 19),       // Next 4 of unique ID
    clean.substring(19)            // Check digit
  ];
}

/**
 * Format a Canadian ICCID with proper spacing for 302 country code
 */
function formatCanadianICCID(iccid) {
  const clean = iccid.replace(/\s+/g, '');

  return [
    clean.substring(0, 2),         // Industry (89)
    clean.substring(2, 5),         // Country (302)
    clean.substring(5, 8),         // Provider (720, etc.)
    clean.substring(8, 12),        // First 4 of unique ID
    clean.substring(12, 16),       // Next 4 of unique ID
    clean.substring(16, 20),       // Next 4 of unique ID
    clean.substring(20)            // Check digit(s)
  ];
}

import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON>ertTriangle,
  CheckCircle,
  XCircle,
  Router,
  TrendingUp,
  TrendingDown,
  BarChart3,
  MapPin,
  Zap,
  Activity,
  RefreshCw,
  ChevronDown,
} from 'lucide-react';
import { useFetch } from "@/hooks";

// Import our awesome DateSelector component
const DateSelector = ({
  selectedDate,
  onDateChange,
  defaultDate = "last-business-day",
  startDate = "2025-06-30",
  endDate = "today"
}) => {
  // Smart date parsing function
  const parseSmartDate = (dateInput) => {
    if (!dateInput) return null;

    // If it's already a YYYY-MM-DD string, return it
    if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      return dateInput;
    }

    const today = new Date();
    const formatDate = (date) => {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };

    const getLastBusinessDay = () => {
      const date = new Date(today);
      const dayOfWeek = date.getDay();

      if (dayOfWeek === 0) { // Sunday
        date.setDate(date.getDate() - 2); // Friday
      } else if (dayOfWeek === 1) { // Monday
        date.setDate(date.getDate() - 3); // Friday
      } else {
        date.setDate(date.getDate() - 1); // Yesterday
      }
      return date;
    };

    const getNextBusinessDay = () => {
      const date = new Date(today);
      const dayOfWeek = date.getDay();

      if (dayOfWeek === 5) { // Friday
        date.setDate(date.getDate() + 3); // Monday
      } else if (dayOfWeek === 6) { // Saturday
        date.setDate(date.getDate() + 2); // Monday
      } else {
        date.setDate(date.getDate() + 1); // Tomorrow
      }
      return date;
    };

    switch (dateInput) {
      case 'today':
        return formatDate(today);
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        return formatDate(yesterday);
      case 'last-business-day':
        return formatDate(getLastBusinessDay());
      case 'tomorrow':
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        return formatDate(tomorrow);
      case 'next-business-day':
        return formatDate(getNextBusinessDay());
      default:
        console.warn('Unknown date keyword:', dateInput);
        return null;
    }
  };

  // Parse the smart dates
  const parsedStartDate = parseSmartDate(startDate);
  const parsedEndDate = parseSmartDate(endDate);
  const parsedDefaultDate = parseSmartDate(defaultDate);

  // Internal state to track the selected date
  const [internalDate, setInternalDate] = useState(() => {
    if (selectedDate) {
      return selectedDate;
    }
    if (parsedDefaultDate) {
      return parsedDefaultDate;
    }
    // Fall back to today
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  });

  // Update internal state when prop changes
  useEffect(() => {
    if (selectedDate) {
      setInternalDate(selectedDate);
    }
  }, [selectedDate]);

  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(() => {
    const parts = internalDate.split('-');
    return new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, 1);
  });

  const formatDate = (dateString) => {
    const [year, month, day] = dateString.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[parseInt(month) - 1]} ${parseInt(day)}, ${year}`;
  };

  // Check if a date is within the allowed range
  const isDateInRange = (dateString) => {
    if (!parsedStartDate && !parsedEndDate) return true;

    if (parsedStartDate && dateString < parsedStartDate) return false;
    if (parsedEndDate && dateString > parsedEndDate) return false;

    return true;
  };

  // Check if month navigation should be disabled
  const getMonthNavigationState = () => {
    if (!parsedStartDate && !parsedEndDate) {
      return { canGoPrev: true, canGoNext: true };
    }

    const currentYear = currentMonth.getFullYear();
    const currentMonthIndex = currentMonth.getMonth();

    let canGoPrev = true;
    let canGoNext = true;

    if (parsedStartDate) {
      const [startYear, startMonth] = parsedStartDate.split('-').map(Number);
      const startMonthDate = new Date(startYear, startMonth - 1, 1);
      const prevMonthDate = new Date(currentYear, currentMonthIndex - 1, 1);
      canGoPrev = prevMonthDate >= startMonthDate;
    }

    if (parsedEndDate) {
      const [endYear, endMonth] = parsedEndDate.split('-').map(Number);
      const endMonthDate = new Date(endYear, endMonth - 1, 1);
      const nextMonthDate = new Date(currentYear, currentMonthIndex + 1, 1);
      canGoNext = nextMonthDate <= endMonthDate;
    }

    return { canGoPrev, canGoNext };
  };

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      days.push({
        year,
        month,
        day,
        dateString,
        isInRange: isDateInRange(dateString)
      });
    }

    return days;
  };

  const isToday = (dayObj) => {
    const today = new Date();
    const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    return dayObj.dateString === todayString;
  };

  const isSameDay = (dayObj, dateString) => {
    return dayObj.dateString === dateString;
  };

  const handleDateSelect = (dayObj) => {
    // Don't allow selection of dates outside the range
    if (!dayObj.isInRange) {
      return;
    }

    console.log('🎯 Date selected:', dayObj.dateString);

    // Update internal state immediately
    setInternalDate(dayObj.dateString);

    // Call parent callback
    if (onDateChange) {
      onDateChange(dayObj.dateString);
    }

    setIsOpen(false);
  };

  const navigateMonth = (direction) => {
    setCurrentMonth((prev) => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + direction);
      return newMonth;
    });
  };

  const days = getDaysInMonth(currentMonth);
  const monthYear = currentMonth.toLocaleDateString("en-US", {
    month: "long",
    year: "numeric",
  });

  const { canGoPrev, canGoNext } = getMonthNavigationState();

  return (
    <div className="relative">
      {/* Dropdown Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-between px-3 py-2 text-xs bg-stone-100 text-gray-700 transition-colors min-w-32 ${
          isOpen ? "rounded-t-xl" : "rounded-full hover:bg-stone-200"
        }`}
      >
        <span>{formatDate(internalDate)}</span>
        <ChevronDown
          className={`w-3 h-3 ml-2 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 bg-stone-100 rounded-b-md rounded-r-md shadow-lg z-50 p-4 min-w-64">
          {/* Month Navigation */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigateMonth(-1)}
              disabled={!canGoPrev}
              className={`p-1 rounded transition-colors ${
                canGoPrev
                  ? "hover:bg-gray-100 text-gray-700"
                  : "text-gray-300 cursor-not-allowed"
              }`}
            >
              <ChevronDown className="w-4 h-4 rotate-90" />
            </button>
            <h3 className="text-sm font-medium text-gray-900">{monthYear}</h3>
            <button
              onClick={() => navigateMonth(1)}
              disabled={!canGoNext}
              className={`p-1 rounded transition-colors ${
                canGoNext
                  ? "hover:bg-gray-100 text-gray-700"
                  : "text-gray-300 cursor-not-allowed"
              }`}
            >
              <ChevronDown className="w-4 h-4 -rotate-90" />
            </button>
          </div>

          {/* Days of Week Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
              <div
                key={day}
                className="text-xs text-gray-500 text-center py-2 font-medium"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {days.map((dayObj, index) => (
              <div key={index} className="aspect-square">
                {dayObj && (
                  <button
                    onClick={() => handleDateSelect(dayObj)}
                    disabled={!dayObj.isInRange}
                    className={`w-full h-full text-xs rounded transition-colors ${
                      !dayObj.isInRange
                        ? "text-gray-300 cursor-not-allowed"
                        : isSameDay(dayObj, internalDate)
                        ? "bg-blue-500 text-white hover:bg-blue-600"
                        : isToday(dayObj)
                        ? "bg-gray-200 text-gray-900 hover:bg-gray-100"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    {dayObj.day}
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Show range info if constraints are active */}
          {(parsedStartDate || parsedEndDate) && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="text-xs text-gray-500 text-center">
                {parsedStartDate && parsedEndDate
                  ? `${formatDate(parsedStartDate)} - ${formatDate(parsedEndDate)}`
                  : parsedStartDate
                  ? `From ${formatDate(parsedStartDate)}`
                  : `Until ${formatDate(parsedEndDate)}`
                }
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const MetricCard = ({ title, value, subtitle, trend, icon: Icon, color = "blue" }) => {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-500",
    green: "bg-green-50 text-green-500",
    red: "bg-red-50 text-red-500",
    orange: "bg-orange-50 text-orange-500",
    purple: "bg-purple-50 text-purple-500"
  };

  return (
    <div className={`rounded-md p-4 ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-md ${colorClasses[color]}`}>
            <Icon className="w-10 h-10" strokeWidth={1} />
          </div>
          <div>
            <p className="text-xs font-medium text-gray-500">{title}</p>
            <p className="text-xl font-bold text-gray-900">{value}</p>
            {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
          </div>
        </div>
        {trend && (
          <div className={`flex items-center gap-1 ${trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
            {trend > 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
            <span className="text-xs font-medium">{Math.abs(trend)}%</span>
          </div>
        )}
      </div>
    </div>
  );
};

const RegionBreakdown = ({ regions }) => {
  return (
    <div className="bg-stone-50 rounded-md p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <MapPin className="w-5 h-5" />
        Regional performance
      </h3>
      <div className="space-y-4">
        {Object.entries(regions).map(([region, data]) => (
          <div key={region} className="flex items-center justify-between p-3 bg-white rounded-md">
            <div className="flex items-center gap-3">
              <div className="text-sm font-mono text-gray-500">{region}</div>
              <div className="flex items-center gap-4 text-sm">
                <span className="text-gray-500">Total: {data.total}</span>
                <span className="text-green-500">Success: {data.successful}</span>
                <span className="text-red-500">Failed: {data.failed}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-16 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${parseFloat(data.successRate)}%` }}
                />
              </div>
              <span className="text-sm font-medium text-gray-900">{data.successRate}%</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const FailureAnalysis = ({ failures }) => {
  return (
    <div className="bg-stone-50 rounded-md p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <AlertTriangle className="w-5 h-5" />
        Top failure reasons
      </h3>
      <div className="space-y-1">
        {failures.topFailureReasons.map(([reason, count], index) => (
          <div key={index} className="flex items-start gap-3 p-3 bg-white rounded-md">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 break-words leading-relaxed">{reason}</p>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <div className="w-12 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-red-500 h-2 rounded-full"
                  style={{ width: `${Math.max((count / failures.totalFailures) * 100, 12)}%` }}
                />
              </div>
              <span className="text-sm font-bold text-gray-900 w-6 text-right">{count}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const DeviceList = ({ devices }) => {
  const sortedDevices = Object.entries(devices).sort((a, b) =>
    parseFloat(a[1].successRate) - parseFloat(b[1].successRate)
  );

  return (
    <div className="bg-stone-50 rounded-md p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <Router className="w-5 h-5" />
        Device performance
      </h3>
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {sortedDevices.map(([deviceId, data]) => (
          <div key={deviceId} className="flex items-center justify-between p-3 bg-white rounded-md">
            <div className="flex items-center gap-3">
              <div className="text-sm font-mono text-gray-500">{data.serialNumber}</div>
              <div className="flex items-center gap-4 text-sm">
                <span className="text-gray-500">Total: {data.total}</span>
                <span className="text-green-500">Success: {data.successful}</span>
                <span className="text-red-500">Failed: {data.failed}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-16 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    parseFloat(data.successRate) > 80 ? 'bg-green-500' :
                    parseFloat(data.successRate) > 50 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{
                    width: parseFloat(data.successRate) === 0 ? '0%' : `${Math.max(parseFloat(data.successRate), 12)}%`
                  }}
                />
              </div>
              <span className="text-sm font-medium text-gray-900">{data.successRate}%</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const Timeline = ({ timeline }) => {
  const dates = Object.keys(timeline).sort();

  return (
    <div className="bg-stone-50 rounded-md p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <BarChart3 className="w-5 h-5" />
        Daily activity timeline
      </h3>
      <div className="space-y-3">
        {dates.map(date => {
          const data = timeline[date];
          const successWidth = (data.successful / data.total) * 100;
          const failedWidth = (data.failed / data.total) * 100;
          const successIsMajority = successWidth >= 50;

          return (
            <div key={date} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">{date}</span>
                <div className="flex items-center gap-4 text-xs">
                  <span className="text-gray-500">Total: {data.total}</span>
                  <span className="text-green-500">Success: {data.successful}</span>
                  <span className="text-red-500">Failed: {data.failed}</span>
                  <span className="font-medium">{data.successRate}%</span>
                </div>
              </div>
              <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
                {successIsMajority ? (
                  <>
                    <div
                      className="bg-green-500 transition-all duration-300"
                      style={{ width: `${successWidth}%` }}
                    />
                    <div
                      className="bg-gray-300 transition-all duration-300"
                      style={{ width: `${failedWidth}%` }}
                    />
                  </>
                ) : (
                  <>
                    <div
                      className="bg-gray-300 transition-all duration-300"
                      style={{ width: `${successWidth}%` }}
                    />
                    <div
                      className="bg-red-500 transition-all duration-300"
                      style={{ width: `${failedWidth}%` }}
                    />
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export const SimActivationReport = () => {
  // Get smart default date (yesterday or last Friday if today is Sun-Mon)
  const getDefaultDate = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();

    if (dayOfWeek === 0 || dayOfWeek === 1) {
      const daysToSubtract = dayOfWeek === 0 ? 2 : 3;
      const lastFriday = new Date(today);
      lastFriday.setDate(today.getDate() - daysToSubtract);
      return lastFriday.toISOString().split('T')[0];
    } else {
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      return yesterday.toISOString().split('T')[0];
    }
  };

  const [selectedDate, setSelectedDate] = useState(getDefaultDate());

  const endpoint = useMemo(() => {
    const params = new URLSearchParams();
    params.append('startDate', selectedDate);

    // For single day queries, use next day as endDate to ensure full day coverage
    const nextDay = new Date(selectedDate);
    nextDay.setDate(nextDay.getDate() + 1);
    const nextDayString = nextDay.toISOString().split('T')[0];

    params.append('endDate', nextDayString);
    return `/qc/reports/sim-activations?${params.toString()}`;
  }, [selectedDate]);

  const {
    data: activationData,
    isLoading,
    isError,
    isFetching,
    refetch
  } = useFetch({
    endpoint: endpoint,
  });

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate);
    console.log('Date changed to:', newDate);
  };

  const handleRefresh = () => {
    refetch();
  };

  // Check if selected date is today
  const isSelectedDateToday = () => {
    const today = new Date();
    const selected = new Date(selectedDate);
    return today.toDateString() === selected.toDateString();
  };

  // Initial loading state (no previous data)
  if (isLoading && !activationData) {
    return (
      <div className="min-h-screen bg-white p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-gray-500">Loading activation data...</div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError || !activationData?.success) {
    return (
      <div className="min-h-screen bg-white p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-lg text-red-500 mb-2">Error loading activation data</div>
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Extract data from the response
  const { summary, byRegion, bySlot, failureAnalysis, timeline, byDevice } = activationData.data;

  return (
    <div className="min-h-screen bg-white pt-1">
      <div className="max-w-7xl mx-auto space-y-4">

        {/* Header with DateSelector - always visible */}
        <div className="border-1 border-gray-50">
          <div className="flex items-center gap-3">
            <DateSelector
              selectedDate={selectedDate}
              onDateChange={handleDateChange}
            />
            {/* Only show refresh button if selected date is today */}
            {isSelectedDateToday() && (
              <button
                onClick={handleRefresh}
                className="px-3 py-1 text-sm bg-none text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Content area with smooth loading states */}
        <div className={`transition-all duration-500 space-y-4 ${isFetching ? 'opacity-60 pointer-events-none' : 'opacity-100'}`}>

          {/* Loading overlay when fetching */}
          {isFetching && (
            <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-75">
              <div className="flex items-center gap-3 text-gray-500">
                <RefreshCw className="w-5 h-5 animate-spin" />
                <span className="text-sm">Loading new data...</span>
              </div>
            </div>
          )}

          {/* Key Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Total activations"
              value={summary.total.toLocaleString()}
              subtitle="All activation attempts"
              icon={Activity}
              color="blue"
            />
            <MetricCard
              title="Success rate"
              value={`${summary.successRate}%`}
              subtitle={`${summary.successful} successful`}
              icon={CheckCircle}
              color="green"
            />
            <MetricCard
              title="Failure rate"
              value={`${summary.failureRate}%`}
              subtitle={`${summary.failed} failed`}
              icon={XCircle}
              color="red"
            />
            <MetricCard
              title="Unique devices"
              value={summary.uniqueDevices}
              subtitle="Different devices"
              icon={Router}
              color="purple"
            />
          </div>

          {/* Charts and Breakdowns */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FailureAnalysis failures={failureAnalysis} />
            <Timeline timeline={timeline} />
          </div>

          {/* Device List */}
          <DeviceList devices={byDevice || {}} />

          {/* Regional Performance */}
          <RegionBreakdown regions={byRegion} />

          {/* Slot Comparison */}
          <div className="bg-stone-50 rounded-md p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Slot performance comparison
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(bySlot).map(([slot, data]) => (
                <div key={slot} className="p-4 bg-white rounded-md">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">Slot {slot}</h4>
                    <span className="text-lg font-bold text-gray-900">{data.successRate}%</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Total:</span>
                      <span>{data.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Successful:</span>
                      <span className="text-green-500">{data.successful}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Failed:</span>
                      <span className="text-red-500">{data.failed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">In Progress:</span>
                      <span className="text-orange-500">{data.inProgress}</span>
                    </div>
                  </div>
                  <div className="mt-3 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 transition-all duration-300"
                      style={{ width: `${parseFloat(data.successRate)}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimActivationReport;

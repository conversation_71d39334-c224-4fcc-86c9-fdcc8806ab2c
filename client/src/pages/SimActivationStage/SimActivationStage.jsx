import React, { useState, useEffect } from "react";
import {
  <PERSON>Wrapper,
  DataTable,
  DateTime,
  PageTitle,
  Avatar1,
  SerialNumber,
  <PERSON>ceH<PERSON>oryButton,
  Loading<PERSON>pinner as <PERSON>ner,
  StatusBadge,
  FriendlySerialNumber,
  JsonViewer,
  Dropdown1,
  LocationChangeButton,
  StageMove,
  DeleteDevice,
  Tooltip1,
} from "@/components";
import { useDebounce, useFetch, useMutate } from "@/hooks";
import { RobustICCID } from "@/constants/iccids";
import { RobustIMEI } from "@/constants/imeis";
import {
  AlertCircle,
  X as XIcon,
  ChevronDown,
  ChevronRight,
  Smartphone,
  Clock,
  Binary,
  Phone,
  PhoneOff,
  RefreshCw
} from "lucide-react";
import Controls from "./Controls";
import { useStore } from "@/store";

export default function SimActivationStage() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(100); // Add pageSize state
  const [queryString, setQueryString] = useState("");
  const [sortOrder, setSortOrder] = useState("");
  const [serialNumber, setSerialNumber] = useState("");
  const [carrierRegion, setCarrierRegion] = useState(null);
  const [autoTest, setAutoTest] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState("");
  const [location, setLocation] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const { mutateAsync } = useMutate();

  // Effect to show error whenever errorMessage string is set
  useEffect(() => {
    if (errorMessage) {
      setShowErrorMessage(true);
    } else {
      setShowErrorMessage(false);
    }
  }, [errorMessage]);

  const debouncedQueryString = useDebounce(queryString);
  const {
    data: simActivationData,
    isLoading,
    refetch,
  } = useFetch({
    endpoint: `/qc/stages/sim/devices?page=${page}&pageSize=${pageSize}&sortOrder=${sortOrder}${debouncedQueryString}`,
    refetchInterval: 5000,
    suppressErrors: true,
  });

  const { devices = [], total: totalDevices } = simActivationData || {};

  const handlePageChange = ({ page }) => {
    setPage(page);
  };

  const handlePageSizeChange = ({ pageSize: newPageSize, page: newPage }) => {
    setPageSize(newPageSize);
    setPage(newPage); // This will be 1 from the DataTable component
  };

  const handleSortOrderChange = ({ sortingStack }) => {
    const newSortOrder = sortingStack
      .map((sort) => `${sort.field}:${sort.direction}`)
      .join(",");
    setSortOrder(newSortOrder);
  };

  // Validation functions
  const validateSerialNumber = (serial) => {
    const pattern1 = /^20\d{8}$/;
    const pattern2 = /^\d{12}$/;
    return pattern1.test(serial) || pattern2.test(serial);
  };

  const validateCarrierRegion = (value) => {
    // For Verizon regions, value will be a ZIP code string
    // For other carriers, value will be null (which is valid)
    return value === null || /^\d{5}$/.test(value);
  };

  // Smart location validation - empty is valid, but if provided must be correct format
  const validateLocation = (loc) => {
    // Empty/whitespace-only is valid (skip location update)
    if (!loc || loc.trim() === '') return true;
    // Otherwise must follow pattern
    return /^[A-Z]\d{2} [A-Z]\d$/.test(loc);
  };

  // Event handlers for the controls
  const handleSerialNumberChange = (e) => {
    const value = e.target.value.replace(/[^\d]/g, "");
    setSerialNumber(value);
    setErrorMessage("");
  };

  const handleLocationChange = (e) => {
    const value = e.target.value.toUpperCase();
    setLocation(value);
    setErrorMessage("");
  };

  // Location is not required
  const canEnroll = (serialToCheck = serialNumber) => {
    return (
      validateSerialNumber(serialToCheck) &&
      validateCarrierRegion(carrierRegion) &&
      selectedPlan !== ""
      // Location is optional now
    );
  };

  const getUserId = () => {
    // Try store first (uses _id from API responses)
    const userData = useStore.getState().user.data;
    if (userData?._id) {
      console.error("Got user ID from data store");
      return userData._id;
    }

    // Fall back to JWT token (uses id)
    const token = localStorage.getItem("jwtToken");
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split(".")[1]));
        console.error("Got user ID from local storage");
        return payload.id; // JWT uses 'id', not '_id'
      } catch (e) {
        console.error("Could not decode JWT:", e);
      }
    }

    // Dev fallback
    const { VITE_NODE_ENV, VITE_DEV_USER_ID } = import.meta.env;
    if (VITE_NODE_ENV === 'development' && VITE_DEV_USER_ID) {
      return VITE_DEV_USER_ID;
    }

    return null;
  };

  const enrollDevice = async (serial) => {
    setErrorMessage("");
    setShowErrorMessage(false);
    setIsEnrolling(true);

    const userId = getUserId();
    if (!userId) {
      setErrorMessage("User not logged in or user ID is missing.");
      setIsEnrolling(false);
      return;
    }

    try {
      // Validation
      if (!selectedPlan) {
        throw new Error("Please select a SIM plan");
      }
      if (!validateCarrierRegion(carrierRegion)) {
        throw new Error("Please select a carrier/region");
      }
      if (!validateLocation(location)) {
        throw new Error("Location must follow pattern: A00 A0 (e.g., B12 C3) or leave empty");
      }
      if (!validateSerialNumber(serial)) {
        throw new Error(
          "Serial number must be 10 digits starting with '20' or 12 digits"
        );
      }

      let deviceId;

      // Only update location if provided and not empty
      if (location && location.trim()) {
        const moveLocationResult = await mutateAsync({
          endpoint: `/qc/devices/${serial.trim()}/locations/${location.trim()}`,
          method: "PUT",
          body: {
            user: userId,
          },
        });

        if (moveLocationResult.error) {
          throw new Error(moveLocationResult.error);
        }

        deviceId = moveLocationResult.device;
      }

      // Move device to SIM stage (this call should handle cases where deviceId might be undefined)
      const moveStageResult = await mutateAsync({
        endpoint: `/qc/stages/sim/devices`,
        method: "PUT",
        body: {
          serial: serial.trim(),
        },
      });

      if (moveStageResult.error) {
        throw new Error(moveStageResult.error);
      }

      // Use deviceId from stage move if we didn't get it from location update
      if (!deviceId && moveStageResult.device) {
        deviceId = moveStageResult.device;
      }

      // Create device configuration
      const createConfigResult = await mutateAsync({
        endpoint: "/qc/device-configs",
        method: "POST",
        body: {
          device: deviceId,
          user: userId,
          plan: selectedPlan,
          autoTest: autoTest,
          ...(carrierRegion && { region: carrierRegion }),
        },
      });

      if (createConfigResult.error) {
        throw new Error(createConfigResult.error);
      }

      // Success! Clear both serial number and location for next device
      console.log("Device enrolled successfully:", {
        serial: serial.trim(),
        region: carrierRegion,
        location: location.trim() || "(no location update)",
        autoTest,
        plan: selectedPlan,
      });

      setSerialNumber("");
      setLocation("");
      setErrorMessage("");

      // Always focus back to serial number field for consistency
      setTimeout(() => {
        document.getElementById("serial-number")?.focus();
      }, 100);

      refetch();
    } catch (error) {
      console.error("Error enrolling device:", error);
      setErrorMessage(error.message || "Failed to enroll device");
    } finally {
      setIsEnrolling(false);
    }
  };

  const handleRetryActivation = async (deviceId, plan, region) => {
    try {
      const activationResult = await mutateAsync({
        endpoint: `/qc/sims/activations`,
        method: "POST",
        body: {
          device: deviceId,
          plan,
          region,
          user: getUserId(),
        },
      });

      refetch(); // Refresh the table
    } catch (error) {
      console.error("Error retrying activation:", error);
      const errorMessage =
        error?.response?.message ||
        error?.message ||
        "Failed to retry activation";
      setErrorMessage(errorMessage);
    }
  };

  const getActivationStatus = (row) => {
    const sim1Activation = row?.simActivations?.["1"];
    const sim2Activation = row?.simActivations?.["2"];

    // Check if any SIM is currently activating
    if (sim1Activation?.status === "in_progress" || sim2Activation?.status === "in_progress") {
      return "activating";
    }

    if (sim1Activation?.status === "pending" || sim2Activation?.status === "pending") {
      return "pending";
    }

    // Fall back to the device's overall status
    return row.status || "pending";
  };

  const StackedSimInfo = ({ row }) => {
    return (
      <div className="space-y-2">
        <div className="flex items-center">
          <div className="ml-1">
            {row.sims && row.sims[0] ? (
              <RobustICCID iccid={row.sims[0]} showDetails={true} />
            ) : (
              <span className="text-gray-400">No SIM</span>
            )}
          </div>
        </div>
        <div className="flex items-center">
          <div className="ml-1">
            {row.sims && row.sims[1] ? (
              <RobustICCID iccid={row.sims[1]} showDetails={true} />
            ) : (
              <span className="text-gray-400">No SIM</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  const formatPlan = (plan) => {
    const planMap = {
      standard: "Standard",
      static: "Standard",
      public: "Public sector",
      private: "Private sector",
    };
    return planMap[plan?.toLowerCase()] || plan;
  };

  const CarrierResponse = ({ responses, simId, index, row }) => {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [expandedSim, setExpandedSim] = useState(() => {
      // Default to expanding the first failed SIM
      if (!row?.simActivations?.["1"]?.success) return 1;
      if (!row?.simActivations?.["2"]?.success) return 2;
      return null;
    });

    const response = responses?.[index];
    const simActivation = row?.simActivations?.[simId];
    const isSuccess = simActivation?.success;
    const sims = row?.sims || [];

    // Show spinner if device is pending and either:
    // 1. SIM has activation record but not successful, OR
    // 2. No activation record yet (still waiting to start)
    if (row?.status === "pending") {
      if (simActivation && !isSuccess) {
        return <Spinner className="h-3.5 w-3.5 text-blue-500" />; // Activating
      } else if (!simActivation) {
        return (
          <div className="flex items-center justify-center flex-shrink-0 p-1">
            <Clock className="h-3.5 w-3.5 text-blue-500" />
          </div>
        ); // Waiting
      }
    }

    return (
      <>
        <button
          type="button"
          className="flex items-center text-sm rounded-md px-2 py-1 bg-none hover:bg-gray-50"
          onClick={() => setIsDialogOpen(true)}
        >
          {isSuccess ? (
            <Phone className="h-3.5 w-3.5 mr-1 text-green-500" />
          ) : (
            <PhoneOff className="h-3.5 w-3.5 mr-1 text-red-500" />
          )}
        </button>

        {isDialogOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsDialogOpen(false)}
          >
            <div
              className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-light text-blue-500">
                    SIM status
                    <span className="font-normal">
                      <span className="mx-2 text-gray-300">/</span>
                      Device <FriendlySerialNumber serial={row?.serial} />
                    </span>
                  </h3>
                  <button
                    onClick={() => setIsDialogOpen(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-4 overflow-y-auto max-h-[70vh]">
                <SIMStatusPanel
                  simSlot={1}
                  simData={row?.simActivations?.["1"]}
                  iccid={sims[0]}
                  isExpanded={expandedSim === 1}
                  onToggle={() => setExpandedSim(expandedSim === 1 ? null : 1)}
                  deviceStatus={row?.status} // Add device status
                />

                <SIMStatusPanel
                  simSlot={2}
                  simData={row?.simActivations?.["2"]}
                  iccid={sims[1]}
                  isExpanded={expandedSim === 2}
                  onToggle={() => setExpandedSim(expandedSim === 2 ? null : 2)}
                  deviceStatus={row?.status} // Add device status
                />
              </div>
            </div>
          </div>
        )}
      </>
    );
  };

  const StackedCarrierResponse = ({ row }) => {
    return (
      <div className="space-y-1">
        <div className="flex items-center">
          <div className="font-medium text-xs text-gray-500 w-2"></div>
          <div>
            <CarrierResponse
              responses={row.simCarrierResponses}
              simId={1}
              index={0}
              row={row}
            />
          </div>
        </div>
        <div className="flex items-center">
          <div className="font-medium text-xs text-gray-500 w-2"></div>
          <div>
            <CarrierResponse
              responses={row.simCarrierResponses}
              simId={2}
              index={1}
              row={row}
            />
          </div>
        </div>
      </div>
    );
  };

  const SIMStatusPanel = ({
    simSlot,
    simData,
    iccid,
    isExpanded,
    onToggle,
  }) => {
    const isSuccess = simData?.success;

    return (
      <div className="mb-4 border rounded-lg">
        {/* Header */}
        <div
          className="p-4 cursor-pointer hover:bg-gray-50 flex items-center justify-between"
          onClick={onToggle}
        >
          <div className="flex items-center space-x-3">
            {isSuccess ? (
              <Phone className="h-5 w-5 text-green-500" />
            ) : (
              <PhoneOff className="h-5 w-5 text-red-500" />
            )}
            <div className="flex-1">
              <div className="font-medium">
                {simSlot === 1 ? "Primary" : "Failover"}
                {isExpanded || (
                  <span className="text-gray-500">
                    <span className="text-gray-300">{" | "}</span>
                    <RobustICCID iccid={iccid} />
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span
              className={`px-2 py-1 rounded text-xs font-medium ${
                isSuccess
                  ? "bg-green-100 text-green-800"
                  : "bg-red-100 text-red-800"
              }`}
            >
              {isSuccess ? "Active" : "Inactive"}
            </span>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </div>
        </div>

        {/* Expanded content */}
        {isExpanded && simData && (
          <div className="px-4 pb-4 border-t bg-gray-50">
            <div className="space-y-3 pt-3">
              {/* Request details */}
              <div>
                <h5 className="font-medium text-sm mb-2 flex items-center">
                  <div className="w-5 flex justify-center mr-3">
                    <Smartphone className="h-4 w-4" />
                  </div>
                  Activation request
                </h5>
                <div className="text-sm space-y-1 ml-8">
                  <div
                    className={
                      simData.request?.region
                        ? "grid grid-cols-2 gap-x-8 gap-y-1"
                        : "space-y-1"
                    }
                  >
                    <div>
                      <span className="text-gray-500">ICCID:</span>{" "}
                      <RobustICCID
                        iccid={simData.request?.iccid}
                        showDetails={true}
                      />
                    </div>
                    <div>
                      <span className="text-gray-500">Plan:</span>{" "}
                      {formatPlan(simData.request?.plan)}
                    </div>
                    <div>
                      <span className="text-gray-500">IMEI:</span>{" "}
                      <RobustIMEI imei={simData.request?.imei} />
                    </div>
                    {simData.request?.region && (
                      <div>
                        <span className="text-gray-500">Region:</span>{" "}
                        {simData.request.region}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Response details */}
              {(simData.response || simData.reason) && (
                <div>
                  <h5 className="font-medium text-sm mb-2 flex items-center">
                    <div className="w-5 flex justify-center mr-3">
                      <Binary className="h-4 w-4" />
                    </div>
                    Carrier gateway response
                  </h5>
                  <div className="text-sm ml-8">
                    {simData.reason && (
                      <div className="mb-2">
                        <div className="text-red-600 text-xs mt-1 p-2 bg-red-50 rounded">
                          {simData.reason}
                        </div>
                      </div>
                    )}
                    {simData.response && (
                      <div>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                          <JsonViewer data={simData.response} />
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Timing */}
              <div>
                <h5 className="font-medium text-sm mb-2 flex items-center">
                  <div className="w-5 flex justify-center mr-3">
                    <Clock className="h-4 w-4" />
                  </div>
                  Timing
                </h5>
                <div className="text-sm ml-8">
                  <div>
                    <span className="text-gray-500">Activation started:</span>{" "}
                    {new Date(simData.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const tableColumns = [
    {
      field: "username",
      title: "User",
      headerClasses: "w-10 pl-3",
      contentClasses: "w-10 pl-1",
      cellRenderer: ({ row }) => (
        <Avatar1 text={row.username} colors={row.userColors} />
      ),
    },
    {
      field: "location",
      title: "Location",
      headerClasses: "w-10",
      contentClasses: "w-10",
    },
    {
      field: "serial",
      title: "Device",
      headerClasses: "w-24 mr-5",
      contentClasses: "w-24 mr-5",
      cellRenderer: ({ row }) => <SerialNumber device={row} />,
    },
    {
      title: "Info",
      headerClasses: "p-0",
      contentClasses: "p-0",
      cellRenderer: ({ row }) => (
        <DeviceHistoryButton
          device={row}
          btnOnly={true}
          onNotationUpdated={refetch}
        />
      ),
    },
    /*
    {
      title: "4G",
      headerClasses: "pl-4",
      contentClasses: "pl-2",
      cellRenderer: ({ row }) => (
        <SignalStrength
          rssi={row?.modemStatus?.rssi}
          quality={row?.modemStatus?.quality}
          isOnline={row?.online === "online"}
          lastChecked={row?.modemStatus?.lastChecked}
        />
      ),
    },
    {
      field: "online",
      title: (
        <>
          <span className="xl:hidden">On</span>
          <span className="hidden xl:inline">Online</span>
        </>
      ),
      contentClasses: "min-w-2 max-w-6 text-middle xl:pl-2",
      headerClasses: "min-w-2 max-w-6 text-middle",
      cellRenderer: ({ row }) => (
        <OnlineStatus status={row.online} uptime={row.uptime} />
      ),
    },
    */
    {
      title: "Plan",
      cellRenderer: ({ row }) => (
        <span>
          {row.plan === "standard"
            ? "Standard"
            : row.plan === "public"
            ? "Public"
            : row.plan === "private"
            ? "Private"
            : `[${row.plan}]`}
        </span>
      ),
    },
    {
      title: "Region",
      cellRenderer: ({ row }) => <span>{row.region}</span>,
    },
    {
      title: "SIMS",
      cellRenderer: ({ row }) => (
        <div className="flex justify-between items-start">
          <StackedSimInfo row={row} />
          <StackedCarrierResponse row={row} />
        </div>
      ),
    },
    {
      title: "Status",
      cellRenderer: ({ row }) => (
        <div className="flex items-center">
          <StatusBadge status={getActivationStatus(row)} />
        </div>
      ),
    },
    {
      field: "enrolledAt",
      title: "QC",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} format="short" />;
      },
      contentClasses: "text-xs",
    },
    {
      field: "createdAt",
      title: "Time",
      cellRenderer: ({ value }) => {
        return <DateTime value={value} format="short"/>;
      },
      contentClasses: "text-xs",
    },
    {
      title: " ",
      contentClasses: "flex justify-end w-full pr-2",
      cellRenderer: ({ row }) => {

        // The front end has to be a little too smart, for this,
        // using all these implementation details instead of a clear
        // message from the backend.
        const shouldShowActivateButton = (
          row.status === 'inactive' || row.status === 'failed'
        );

        return (
          <div className="flex flex-row gap-2">
            {shouldShowActivateButton && (
              <Tooltip1 content="Activate SIM card(s)">
                <button
                  type="button"
                  className={`shrink-0 w-5 h-5 mr-1`}
                  onClick={() => {
                    console.log("Retry activation clicked for:", {
                      device: row.device,
                      plan: row.plan,
                      region: row.region
                    });
                    handleRetryActivation(row.device, row.plan, row.region);
                  }}
                >
                  <RefreshCw className="shrink-0 w-5 h-5 stroke-1.5 text-gray-800" />
                </button>
              </Tooltip1>
            )}
            <StageMove
              from="sim"
              target="software"
              device={row}
              refetch={refetch}
              btnOnly
            />
            <div className="flex flex-row gap-2">
              <Dropdown1>
                <LocationChangeButton
                  device={row.serial}
                  onLocationUpdated={refetch}
                />
                <div separator />
                <StageMove target="archive" device={row} refetch={refetch} />
                <DeleteDevice device={row} refetch={refetch} />
              </Dropdown1>
            </div>
          </div>
        );
      },
    }
  ];

  return (
    <MainWrapper>
      <div className="flex justify-between items-center mb-1">
        <PageTitle>SIM activation</PageTitle>

        {/* Extracted Controls Component */}
        <Controls
          // Settings section props
          autoTest={autoTest}
          setAutoTest={setAutoTest}
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedPlan}
          carrierRegion={carrierRegion}
          setCarrierRegion={setCarrierRegion}
          // Device enrollment section props
          location={location}
          setLocation={setLocation}
          serialNumber={serialNumber}
          setSerialNumber={setSerialNumber}
          isEnrolling={isEnrolling}
          // Validation functions
          validateLocation={validateLocation}
          validateSerialNumber={validateSerialNumber}
          validateCarrierRegion={validateCarrierRegion}
          canEnroll={canEnroll}
          // Event handlers
          handleLocationChange={handleLocationChange}
          handleSerialNumberChange={handleSerialNumberChange}
          enrollDevice={enrollDevice}
        />
      </div>

      {/* Error Message */}
      {showErrorMessage && errorMessage && (
        <div className="relative w-full flex items-center bg-red-50 px-4 py-2 rounded-md mb-4 text-red-500 text-sm border border-red-200">
          <AlertCircle className="h-4 w-4 flex-shrink-0 mr-2" />
          <span className="flex-grow">{errorMessage}</span>
          <button
            onClick={() => setShowErrorMessage(false)}
            className="ml-auto p-1 -my-1 rounded-full text-red-400 hover:bg-red-100 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
            aria-label="Dismiss error"
          >
            <XIcon className="h-4 w-4 pr-2" />
          </button>
        </div>
      )}

      <DataTable
        columns={tableColumns}
        rows={devices}
        isLoading={isLoading}
        showPagination
        pageSize={pageSize}
        totalRows={totalDevices}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onSortOrderChange={handleSortOrderChange}
      />
    </MainWrapper>
  );
}

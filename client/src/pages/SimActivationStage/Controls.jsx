import React, { useRef } from "react";
import {
  ToggleSwitch,
  Dropdown2,
  CollapsibleSection,
  IconWithOverlay,
  Tooltip1,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as Spinner,
} from "@/components";
import {
  SlidersHorizontal,
  HardDrive,
  Plus,
} from "lucide-react";

const Controls = ({
  // Settings section props
  autoTest,
  setAutoTest,
  selectedPlan,
  setSelectedPlan,
  carrierRegion,
  setCarrierRegion,

  // Device enrollment section props
  location,
  setLocation,
  serialNumber,
  setSerialNumber,
  isEnrolling,

  // Validation functions
  validateLocation,
  validateSerialNumber,
  validateCarrierRegion,
  canEnroll,

  // Event handlers
  handleLocationChange,
  handleSerialNumberChange,
  enrollDevice,
}) => {
  const locationRef = useRef(null);
  const serialNumberRef = useRef(null);

  // Plan selection handler
  const handlePlanChange = (value) => {
    setSelectedPlan(value);
  };

  // Carrier/Region selection handler - focus serial number field directly
  const handleCarrierRegionChange = (value) => {
    setCarrierRegion(value);

    // Focus on serial number field directly for warehouse workflow
    setTimeout(() => {
      serialNumberRef.current?.focus();
    }, 0);
  };

  const handleLocationChangeWithAutoFocus = (e) => {
    handleLocationChange(e);
    const value = e.target.value.toUpperCase();

    // Check if the location is now valid and complete (A00 A1 format)
    if (validateLocation(value) && value.length === 6 && /^[A-Z]\d{2} [A-Z]\d$/.test(value)) {
      // Focus on serial number field when valid location is complete
      setTimeout(() => {
        serialNumberRef.current?.focus();
      }, 0);
    }
  };

  // Serial number handler with auto-submit - always focus back to serial
  const handleSerialNumberChangeWithSubmit = (e) => {
    handleSerialNumberChange(e);
    const value = e.target.value.replace(/[^\d]/g, '');

    // Auto-submit when form becomes valid
    const wouldBeValid = (
      validateSerialNumber(value) &&
      (carrierRegion === "other" || validateCarrierRegion(carrierRegion)) &&
      validateLocation(location) &&
      selectedPlan !== ""
    );

    if (wouldBeValid) {
      enrollDevice(value);
    }
  };

  return (
    <div className="flex items-start">
      {/* Settings Section */}
      <div className="flex items-center gap-2">
        <div className="flex gap-2">
          <CollapsibleSection icon={SlidersHorizontal} defaultExpanded={true}>
            <Tooltip1
              content="Start Stage 1 tests automatically when both SIMs are activated"
            >
              <ToggleSwitch
                checked={autoTest}
                onChange={setAutoTest}
              >
                <div
                  className={`text-xs leading-none flex flex-wrap ${
                    autoTest  ? "" : "opacity-50"
                  }`}
                >
                  Autotest
                </div>
              </ToggleSwitch>
            </Tooltip1>

            <div className="flex items-center space-x-2">
              <Dropdown2
                options={[
                  { value: 'standard', label: 'Standard plan', shortLabel: 'Standard plan', disabled: false },
                  { value: 'public', label: 'Public sector', shortLabel: 'Public plan', disabled: true },
                  { value: 'private', label: 'Private sector', shortLabel: 'Private plan', disabled: true },
                ]}
                placeholder=""
                label="SIM plan"
                value={selectedPlan}
                onChange={handlePlanChange}
                width="w-[7.5rem]"
              />
            </div>

            {/* Single Carrier/Region Dropdown */}
            <div className="flex items-center space-x-2">
              <Dropdown2
                options={[
                  { value: '90222', label: 'Verizon - L.A.', disabled: false },
                  { value: '02124', label: 'Verizon - Boston', disabled: false },
                  { value: '60629', label: 'Verizon - Chicago', disabled: false },
                  { value: '75201', label: 'Verizon - Dallas', disabled: false },
                  { value: 'other', shortLabel: 'Non-Verizon carrier', label: "Other", disabled: false },
                ]}
                placeholder="Carrier/Region"
                label=""
                value={carrierRegion}
                onChange={handleCarrierRegionChange}
                width="w-[9rem]"
              />
            </div>
          </CollapsibleSection>
        </div>
      </div>

      {/* Device Enrollment Section */}
      <div className="flex gap-2">
        {!isEnrolling && (
          <CollapsibleSection
            icon={() => <IconWithOverlay icon={HardDrive} overlayIcon={Plus} />}
            defaultExpanded={true}
          >
            <div className="relative">
              <input
                type="text"
                id="location"
                ref={locationRef}
                value={location}
                onChange={handleLocationChangeWithAutoFocus}
                className={`flex h-6 w-20 rounded-full px-3 py-0 text-xs transition-colors border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white disabled:cursor-not-allowed disabled:opacity-50 ${
                  location && !validateLocation(location)
                    ? "bg-red-100 focus:ring-red-500"
                    : "bg-stone-100"
                }`}
                placeholder="Location"
                maxLength={6}
              />
            </div>

            <div className="relative">
              <input
                type="text"
                id="serial-number"
                ref={serialNumberRef}
                value={serialNumber}
                onChange={handleSerialNumberChangeWithSubmit}
                className={`flex h-6 w-24 rounded-full px-3 py-0 text-xs transition-colors border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white disabled:cursor-not-allowed disabled:opacity-50 ${
                  isEnrolling
                    ? "bg-blue-50"
                    : serialNumber && !validateSerialNumber(serialNumber)
                    ? "bg-red-100 focus:ring-red-500"
                    : "bg-stone-100"
                }`}
                placeholder="Device"
                maxLength={12}
                disabled={isEnrolling}
              />
            </div>
          </CollapsibleSection>
        )}
        {isEnrolling && (
          <Spinner className={`absolute right-2 top-1/2 -translate-y-1/2 h-2 w-2`} />
        )}
      </div>
    </div>
  );
};

export default Controls;

const SimCardIcon = ({
  size = 24,
  color = "currentColor",
  strokeWidth = 2,
  className = "",
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth * 0.8}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`lucide lucide-sim-card ${className} rotate-90`}
      {...props}
    >
      {/* Main SIM card outline - rotated 90° with 45° notched corner */}
      <path d="M6 16v-8l3-3h10a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2z" />

      {/* Contact lines pattern */}
      <line x1="12" y1="9" x2="18" y2="9" strokeWidth={strokeWidth * 0.7} />
      <line x1="12" y1="12" x2="18" y2="12" strokeWidth={strokeWidth * 0.7} />
      <line x1="12" y1="15" x2="18" y2="15" strokeWidth={strokeWidth * 0.7} />
    </svg>
  );
};

export default SimCardIcon;

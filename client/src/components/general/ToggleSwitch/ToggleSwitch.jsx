import React, { useState } from 'react';

const ToggleSwitch = ({ children, checked = false, onChange }) => {
  const [enabled, setEnabled] = useState(checked);

  const handleToggle = () => {
    const newState = !enabled;
    setEnabled(newState);
    if (onChange) onChange(newState);
  };

  return (
    <div className="flex items-center">
      <div className="flex items-center cursor-pointer" onClick={handleToggle}>
        <button
          type="button"
          className={`relative inline-flex h-5 w-8 items-center rounded-full shrink-0 scale-100 ${
            enabled ? 'bg-blue-500' : 'bg-stone-200'
          } transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          role="switch"
          aria-checked={enabled}
        >
          <span className="sr-only">{children}</span>
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition duration-200 ease-in-out ${
              enabled ? 'translate-x-3.5' : 'translate-x-0.5'
            }`}
          />
        </button>
        <span className="text-sm font-medium text-stone-700 ml-1.5 cursor-pointer select-none">
          {children}
        </span>
      </div>
    </div>
  );
};

export default ToggleSwitch;

import { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';

// Enhanced Dropdown component with disabled options and auto-selection
const Dropdown2 = ({
  options = [],
  defaultValue = null,
  label = '',
  placeholder,
  width = 'w-64',
  onChange = null,
  disabled = false,
  showSelectedBelow = false,
  buttonClassName = '',
  menuClassName = '',
  itemClassName = '',
  dropdownPosition = 'bottom',
  maxHeight = 'max-h-60',
  isSearchable = false,
  valueKey = 'value',     // For object options
  labelKey = 'label',     // For object options
  shortLabelKey = 'shortLabel',
  displaySelectedWithLabel = false,
  value,                  // For controlled components
  disabledKey = 'disabled', // Key to check if option is disabled (for object options)
}) => {
  // Determine if options are simple strings or objects
  const isObjectOptions = options.length > 0 && typeof options[0] === 'object';

  // Helper function to check if an option is disabled
  const isOptionDisabled = (option) => {
    if (isObjectOptions) {
      return option[disabledKey] === true;
    }
    // For string options, you could extend this to support a separate disabled array
    return false;
  };

  // Filter out disabled options to get available options
  const availableOptions = options.filter(option => !isOptionDisabled(option));

  // Check if this is a controlled component
  const isControlled = value !== undefined;

  // Initialize the selected item
  const getInitialValue = () => {
    const initialVal = isControlled ? value : defaultValue;
    if (initialVal) {
      const foundOption = isObjectOptions
        ? options.find(opt => opt[valueKey] === initialVal)
        : options.includes(initialVal) ? initialVal : null;
      return foundOption;
    }

    // Auto-select if only one available option
    if (availableOptions.length === 1) {
      return availableOptions[0];
    }

    return null;
  };

  const [isOpen, setIsOpen] = useState(false);
  const [internalSelectedItem, setInternalSelectedItem] = useState(getInitialValue());

  // For controlled components, use the value prop; for uncontrolled, use internal state
  const selectedItem = isControlled
    ? (value ? (isObjectOptions ? options.find(opt => opt[valueKey] === value) : value) : null)
    : internalSelectedItem;
  const [searchTerm, setSearchTerm] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Effect to handle auto-selection when available options change
  useEffect(() => {
    const currentAvailableOptions = options.filter(option => !isOptionDisabled(option));

    // Auto-select if only one available option and nothing is currently selected
    if (currentAvailableOptions.length === 1 && !selectedItem) {
      const autoSelected = currentAvailableOptions[0];

      // Only update internal state for uncontrolled components
      if (!isControlled) {
        setInternalSelectedItem(autoSelected);
      }

      if (onChange) {
        onChange(isObjectOptions ? autoSelected[valueKey] : autoSelected);
      }
    }
  }, [options, selectedItem, onChange, isObjectOptions, valueKey, disabledKey, isControlled]);

  // Filter options based on search term if searchable
  const filteredOptions = isSearchable && searchTerm
    ? (isObjectOptions
        ? options.filter(opt =>
            String(opt[labelKey])
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          )
        : options.filter(opt =>
            String(opt)
              .toLowerCase()
              .includes(searchTerm.toLowerCase())
          )
      )
    : options;

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      // Clear search when toggling
      if (!isOpen) {
        setSearchTerm('');
      }
    }
  };

  const selectOption = (option) => {
    // Don't allow selection of disabled options
    if (isOptionDisabled(option)) {
      return;
    }

    // Only update internal state for uncontrolled components
    if (!isControlled) {
      setInternalSelectedItem(option);
    }

    setIsOpen(false);
    setSearchTerm('');
    // Call onChange callback if provided
    if (onChange) {
      onChange(isObjectOptions ? option[valueKey] : option);
    }
  };

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && isSearchable && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen, isSearchable]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get displayed value
  const getDisplayValue = () => {
    if (!selectedItem) return placeholder;

    if (isObjectOptions) {
      // If shortLabelKey is provided and exists in the option, use it
      if (shortLabelKey && selectedItem[shortLabelKey]) {
        return selectedItem[shortLabelKey];
      }
      // Otherwise fall back to the regular label
      return selectedItem[labelKey];
    }

    return selectedItem;
  };

  // Position classes based on dropdownPosition prop
  const positionClasses = {
    'bottom': 'top-full',
    'top': 'bottom-full ',
    'left': 'right-full top-0',
    'right': 'left-full top-0'
  };

  return (
    <div className="flex flex-col items-center justify-center whitespace-nowrap">
      <div className={`relative ${width}`} ref={dropdownRef}>
        {/* Container for both button and menu - no outline when open */}
        <div className="relative">
          {/* Dropdown button */}
          <button
            onClick={toggleDropdown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            disabled={disabled}
            className={`w-full flex items-center h-6 pl-3 pr-2.5 py-1 text-left text-xs z-20
              ${dropdownPosition === 'top' && isOpen ? "shadow-lg" : ""}
              bg-stone-100
              ${isOpen ? `${dropdownPosition === 'bottom' ? 'rounded-t-lg rounded-b-none' : 'rounded-b-lg rounded-t-none'}` : 'rounded-full'}
              ${!isOpen && isFocused ? 'ring-2 ring-blue-500' : ''}
              focus:outline-none
              ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
              ${buttonClassName}`}
          >
            {/* Only show label if no item is selected or displaySelectedWithLabel is true */}
            {(label && (!selectedItem || displaySelectedWithLabel)) && (
              <span className="font-medium text-gray-700 mr-1">{label}</span>
            )}
            <span className="text-gray-800 truncate flex-grow">{getDisplayValue()}</span>
            <ChevronDown
              className={`w-3 h-3 text-gray-500 transform ${(isOpen ^ dropdownPosition === 'top') ? 'rotate-180' : 'rotate-0'} transition-transform duration-200 ml-1.5`}
            />
          </button>

          {/* Dropdown menu - only shown when isOpen */}
          {isOpen && (
            <div
              className={`absolute w-full bg-stone-100 ${dropdownPosition === 'bottom' ? 'shadow-lg rounded-b-lg' : 'rounded-t-lg'}
                ${positionClasses[dropdownPosition]}
                mt-0 border-t-1 boder-gray-200 z-20
                ${menuClassName}`}
            >
              {/* Search input */}
              {isSearchable && (
                <div className="border-none border-gray-200">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search..."
                    className="w-full h-6 px-2 py-1 text-xs bg-white rounded-full focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              )}

              {/* Options list */}
              <ul className={`${dropdownPosition === 'bottom' ? 'pb-2' : 'pt-2'} overflow-auto ${maxHeight} overflow-hidden`}>
                {filteredOptions.length > 0 ? (
                  filteredOptions.map((option, index) => {
                    const disabled = isOptionDisabled(option);
                    const isSelected = (isObjectOptions && selectedItem && selectedItem[valueKey] === option[valueKey]) ||
                                    (!isObjectOptions && selectedItem === option);

                    return (
                      <li
                        key={index}
                        onClick={() => selectOption(option)}
                        className={`px-3 py-1 text-xs transition-colors duration-150 whitespace-nowrap ${
                          disabled
                            ? 'text-gray-400 cursor-not-allowed opacity-50'
                            : `text-gray-700 cursor-pointer ${
                                isSelected ? 'bg-blue-100' : ''
                              } hover:bg-blue-200`
                        } ${itemClassName}`}
                      >
                        {isObjectOptions ? option[labelKey] : option}
                      </li>
                    );
                  })
                ) : (
                  <li className="px-4 py-2 text-gray-500 italic whitespace-nowrap">No options found</li>
                )}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Selected value display */}
      {showSelectedBelow && selectedItem && (
        <div className="mt-4 text-center text-gray-700 whitespace-nowrap">
          <p>Selected: <span className="font-medium">{isObjectOptions ? selectedItem[labelKey] : selectedItem}</span></p>
        </div>
      )}
    </div>
  );
};

export default Dropdown2;

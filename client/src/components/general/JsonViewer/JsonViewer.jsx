  import React, { useState } from "react";

  const JsonViewer = ({ data }) => {
    try {
      const normalizedData = JSON.parse(JSON.stringify(data));
      return (
        <div className="font-mono text-sm text-gray-900">
          <PropertyNode data={normalizedData} name="" level={0} />
        </div>
      );
    } catch (error) {
      return (
        <div className="font-mono text-sm text-red-600 p-2 bg-red-50 rounded">
          Invalid JSON: {error.message}
        </div>
      );
    }
  };

  const PropertyNode = ({
    data,
    name,
    level,
    isArrayIndex = false,
    showComma = false,
  }) => {
    const [isExpanded, setIsExpanded] = useState(true);

    const getValueType = (value) => {
      if (value === null) return "null";
      if (typeof value === "string") return "string";
      if (typeof value === "number") return "number";
      if (typeof value === "boolean") return "boolean";
      if (Array.isArray(value)) return "array";
      if (typeof value === "object") return "object";
      return "unknown";
    };

    const getValueColor = (type) => {
      const colors = {
        string: "text-green-700",
        number: "text-amber-600",
        boolean: "text-purple-700",
        null: "text-purple-700",
      };
      return colors[type] || "text-gray-700";
    };

    const formatValue = (value, type) => {
      if (type === "string") {
        return `"${value}"`;
      }
      if (type === "null") return "null";
      if (type === "boolean") return value ? "true" : "false";
      return String(value);
    };

    const renderValue = (value) => {
      const type = getValueType(value);

      if (type === "object" && value !== null) {
        const entries = Object.entries(value);

        // If empty object, treat as scalar
        if (entries.length === 0) {
          return (
            <div style={{ paddingLeft: "13px" }}>
              {name !== null && name !== undefined && name !== "" && (
                <span>
                  <span
                    className={isArrayIndex ? "text-gray-400" : "text-blue-600"}
                  >
                    {name}
                  </span>
                  <span className="text-black">: </span>
                </span>
              )}
              <span className="text-black text-xs">{"{}"}</span>
              {showComma && <span className="text-black">,</span>}
            </div>
          );
        }

        return (
          <div>
            <div
              onClick={() => setIsExpanded(!isExpanded)}
              className="cursor-pointer hover:bg-gray-100 -mx-2 px-2 py-[0.05rem] rounded flex items-start w-full"
            >
              <span className="text-gray-400 text-xs mr-[0.5em]">
                {isExpanded ? "-" : "+"}
              </span>
              {name !== null && name !== undefined && name !== "" && (
                <span>
                  <span
                    className={isArrayIndex ? "text-gray-400" : "text-blue-600"}
                  >
                    {name}
                  </span>
                  <span className="text-black">: </span>
                </span>
              )}
              <span className="text-black">
                {"{"}
              </span>
              {!isExpanded && (
                <span className="text-gray-400 text-xs  ">{entries.length}</span>
              )}
              {!isExpanded && (
                <span className="text-black">
                  {"}"}
                  {showComma && ","}
                </span>
              )}
            </div>

            {isExpanded && (
              <div>
                {entries.map(([childKey, childValue], index) => (
                  <PropertyNode
                    key={childKey}
                    data={childValue}
                    name={childKey}
                    level={level + 1}
                    showComma={index < entries.length - 1}
                  />
                ))}
                <div
                  style={{ paddingLeft: "1em" }}
                  className="text-black font-mono text-xs"
                >
                  {"}"}
                  {showComma && <span>,</span>}
                </div>
              </div>
            )}
          </div>
        );
      }

      if (type === "array") {
        // If empty array, treat as scalar
        if (value.length === 0) {
          return (
            <div style={{ paddingLeft: "15px" }}>
              {name !== null && name !== undefined && name !== "" && (
                <span>
                  <span
                    className={isArrayIndex ? "text-gray-400" : "text-blue-600"}
                  >
                    {name}
                  </span>
                  <span className="text-black">: </span>
                </span>
              )}
              <span className="text-black text-xs">{"[]"}</span>
              {showComma && <span className="text-black">,</span>}
            </div>
          );
        }

        return (
          <div>
            <div
              onClick={() => setIsExpanded(!isExpanded)}
              className="cursor-pointer hover:bg-gray-100 -mx-2 px-2 py-[0.05rem] rounded flex items-start w-full"
            >
              <span className="text-gray-400 text-xs mr-1">
                {isExpanded ? "-" : "+"}
              </span>
              {name !== null && name !== undefined && name !== "" && (
                <span>
                  <span
                    className={isArrayIndex ? "text-gray-400" : "text-blue-600"}
                  >
                    {name}
                  </span>
                  <span className="text-black">: </span>
                </span>
              )}
              <span className="text-black">
                {"["}
              </span>
              {!isExpanded && (
                <span className="text-gray-400 text-xs">{value.length}</span>
              )}
              {!isExpanded && (
                <span className="text-black">
                  {"]"}
                  {showComma && ","}
                </span>
              )}
            </div>

            {isExpanded && (
              <div>
                {value.map((item, index) => (
                  <PropertyNode
                    key={index}
                    data={item}
                    name={index}
                    level={level + 1}
                    isArrayIndex={true}
                    showComma={index < value.length - 1}
                  />
                ))}
                <div
                  style={{ paddingLeft: "1em" }}
                  className="text-black font-mono text-xs"
                >
                  {"]"}
                  {showComma && <span>,</span>}
                </div>
              </div>
            )}
          </div>
        );
      }

      return (
        <div style={{ paddingLeft: "13px" }}>
          {name !== null && name !== undefined && name !== "" && (
            <span>
              <span className={isArrayIndex ? "text-gray-400" : "text-blue-600"}>
                {name}
              </span>
              <span className="text-black">: </span>
            </span>
          )}
          <span className={`text-xs ${getValueColor(type)}`}>
            {formatValue(value, type)}
          </span>
          {showComma && <span className="text-black">,</span>}
        </div>
      );
    };

    return (
      <div className="py-[0.05rem]">
        <div
          className="flex items-start py-[0.05rem] font-mono text-xs leading-tight"
          style={{ paddingLeft: "0.67rem" }}
        >
          {renderValue(data)}
        </div>
      </div>
    );
  };

  export default JsonViewer;

import { PageTitle } from "@/components";

export function Breadcrumb({ items }) {
  return (
    <nav className="flex items-center space-x-0">
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            "/"
          )}
          {item.onClick ? (
            <button onClick={item.onClick}>
              <PageTitle className="text-gray-600 hover:text-blue-500">
                {item.label}
              </PageTitle>
            </button>
          ) : (
            <PageTitle className="text-gray-600">{item.label}</PageTitle>
          )}
        </div>
      ))}
    </nav>
  );
}

import React, { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";

const DateSelector = ({
  selectedDate,
  onDateChange,
  defaultDate = "last-business-day",
  startDate = "2025-06-30",
  endDate = "today"
}) => {
  // Smart date parsing function
  const parseSmartDate = (dateInput) => {
    if (!dateInput) return null;

    // If it's already a YYYY-MM-DD string, return it
    if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      return dateInput;
    }

    const today = new Date();
    const formatDate = (date) => {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };

    const getLastBusinessDay = () => {
      const date = new Date(today);
      const dayOfWeek = date.getDay();

      if (dayOfWeek === 0) { // Sunday
        date.setDate(date.getDate() - 2); // Friday
      } else if (dayOfWeek === 1) { // Monday
        date.setDate(date.getDate() - 3); // Friday
      } else {
        date.setDate(date.getDate() - 1); // Yesterday
      }
      return date;
    };

    const getNextBusinessDay = () => {
      const date = new Date(today);
      const dayOfWeek = date.getDay();

      if (dayOfWeek === 5) { // Friday
        date.setDate(date.getDate() + 3); // Monday
      } else if (dayOfWeek === 6) { // Saturday
        date.setDate(date.getDate() + 2); // Monday
      } else {
        date.setDate(date.getDate() + 1); // Tomorrow
      }
      return date;
    };

    switch (dateInput) {
      case 'today':
        return formatDate(today);
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        return formatDate(yesterday);
      case 'last-business-day':
        return formatDate(getLastBusinessDay());
      case 'tomorrow':
        const tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        return formatDate(tomorrow);
      case 'next-business-day':
        return formatDate(getNextBusinessDay());
      default:
        console.warn('Unknown date keyword:', dateInput);
        return null;
    }
  };

  // Parse the smart dates
  const parsedStartDate = parseSmartDate(startDate);
  const parsedEndDate = parseSmartDate(endDate);
  const parsedDefaultDate = parseSmartDate(defaultDate);

  // Internal state to track the selected date
  const [internalDate, setInternalDate] = useState(() => {
    if (selectedDate) {
      return selectedDate;
    }
    if (parsedDefaultDate) {
      return parsedDefaultDate;
    }
    // Fall back to today
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  });

  // Update internal state when prop changes
  useEffect(() => {
    if (selectedDate) {
      setInternalDate(selectedDate);
    }
  }, [selectedDate]);

  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(() => {
    const parts = internalDate.split('-');
    return new Date(parseInt(parts[0]), parseInt(parts[1]) - 1, 1);
  });
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const formatDate = (dateString) => {
    const [year, month, day] = dateString.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[parseInt(month) - 1]} ${parseInt(day)}, ${year}`;
  };

  // Check if a date is within the allowed range
  const isDateInRange = (dateString) => {
    if (!parsedStartDate && !parsedEndDate) return true;

    if (parsedStartDate && dateString < parsedStartDate) return false;
    if (parsedEndDate && dateString > parsedEndDate) return false;

    return true;
  };

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      days.push({
        year,
        month,
        day,
        dateString,
        isInRange: isDateInRange(dateString)
      });
    }

    return days;
  };

  const isToday = (dayObj) => {
    const today = new Date();
    const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    return dayObj.dateString === todayString;
  };

  const isSameDay = (dayObj, dateString) => {
    return dayObj.dateString === dateString;
  };

  const handleDateSelect = (dayObj) => {
    // Don't allow selection of dates outside the range
    if (!dayObj.isInRange) {
      return;
    }

    console.log('Date selected:', dayObj.dateString);

    // Update internal state immediately
    setInternalDate(dayObj.dateString);

    // Call parent callback
    if (onDateChange) {
      onDateChange(dayObj.dateString);
    }

    setIsOpen(false);
  };

  // Check if month navigation should be disabled
  const getMonthNavigationState = () => {
    if (!parsedStartDate && !parsedEndDate) {
      return { canGoPrev: true, canGoNext: true };
    }

    const currentYear = currentMonth.getFullYear();
    const currentMonthIndex = currentMonth.getMonth();

    let canGoPrev = true;
    let canGoNext = true;

    if (parsedStartDate) {
      const [startYear, startMonth] = parsedStartDate.split('-').map(Number);
      const startMonthDate = new Date(startYear, startMonth - 1, 1);
      const prevMonthDate = new Date(currentYear, currentMonthIndex - 1, 1);
      canGoPrev = prevMonthDate >= startMonthDate;
    }

    if (parsedEndDate) {
      const [endYear, endMonth] = parsedEndDate.split('-').map(Number);
      const endMonthDate = new Date(endYear, endMonth - 1, 1);
      const nextMonthDate = new Date(currentYear, currentMonthIndex + 1, 1);
      canGoNext = nextMonthDate <= endMonthDate;
    }

    return { canGoPrev, canGoNext };
  };

  const navigateMonth = (direction) => {
    setCurrentMonth((prev) => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + direction);
      return newMonth;
    });
  };

  const { canGoPrev, canGoNext } = getMonthNavigationState();

  const days = getDaysInMonth(currentMonth);
  const monthYear = currentMonth.toLocaleDateString("en-US", {
    month: "long",
    year: "numeric",
  });

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-between px-3 py-2 text-xs bg-stone-100 text-gray-700 transition-colors min-w-32 ${
          isOpen ? "rounded-t-xl" : "rounded-full hover:bg-stone-200"
        }`}
      >
        <span>{formatDate(internalDate)}</span>
        <ChevronDown
          className={`w-3 h-3 ml-2 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 bg-stone-100 rounded-b-md rounded-r-md shadow-lg z-50 p-4 min-w-64">
          {/* Month Navigation */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigateMonth(-1)}
              disabled={!canGoPrev}
              className={`p-1 rounded transition-colors ${
                canGoPrev
                  ? "hover:bg-gray-100 text-gray-700"
                  : "text-gray-300 cursor-not-allowed"
              }`}
            >
              <ChevronDown className="w-4 h-4 rotate-90" />
            </button>
            <h3 className="text-sm font-medium text-gray-900">{monthYear}</h3>
            <button
              onClick={() => navigateMonth(1)}
              disabled={!canGoNext}
              className={`p-1 rounded transition-colors ${
                canGoNext
                  ? "hover:bg-gray-100 text-gray-700"
                  : "text-gray-300 cursor-not-allowed"
              }`}
            >
              <ChevronDown className="w-4 h-4 -rotate-90" />
            </button>
          </div>

          {/* Days of Week Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => (
              <div
                key={day}
                className="text-xs text-gray-500 text-center py-2 font-medium"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {days.map((dayObj, index) => (
              <div key={index} className="aspect-square">
                {dayObj && (
                  <button
                    onClick={() => handleDateSelect(dayObj)}
                    disabled={!dayObj.isInRange}
                    className={`w-full h-full text-xs rounded transition-colors ${
                      !dayObj.isInRange
                        ? "text-gray-300 cursor-not-allowed"
                        : isSameDay(dayObj, internalDate)
                        ? "bg-blue-500 text-white hover:bg-blue-600"
                        : isToday(dayObj)
                        ? "bg-gray-200 text-gray-900 hover:bg-gray-100"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    {dayObj.day}
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DateSelector;

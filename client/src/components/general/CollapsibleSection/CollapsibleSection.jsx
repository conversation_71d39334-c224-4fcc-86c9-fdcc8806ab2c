import React, { useState } from 'react';

const CollapsibleSection = ({
  icon: Icon,
  children,
  defaultExpanded = false,
  className = ""
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className={`flex px-2 items-center gap-2 p-1 min-h-[2rem] min-w-8 ${className}`}>
      <div className="flex-shrink-0 cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
        <Icon className="flex-shrink-0  w-4 h-4 text-gray-600" />
      </div>

      {isExpanded && (
        <div className="flex items-center gap-2">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsibleSection;

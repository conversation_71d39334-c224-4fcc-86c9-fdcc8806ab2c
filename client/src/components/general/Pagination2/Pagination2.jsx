import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

const Pagination2 = ({
  currentPage = 1,
  totalPages = 10,
  onPageChange = () => {},
  showEdges = true,
  siblingCount = 1
}) => {
  const generatePageNumbers = () => {
    const pages = [];
    const leftSibling = Math.max(currentPage - siblingCount, 1);
    const rightSibling = Math.min(currentPage + siblingCount, totalPages);

    const showLeftDots = leftSibling > 2;
    const showRightDots = rightSibling < totalPages - 1;

    if (showEdges && showLeftDots) {
      pages.push(1);
      if (leftSibling > 3) pages.push('left-dots');
    }

    for (let i = leftSibling; i <= rightSibling; i++) {
      pages.push(i);
    }

    if (showEdges && showRightDots) {
      if (rightSibling < totalPages - 2) pages.push('right-dots');
      pages.push(totalPages);
    }

    return pages;
  };

  const pages = generatePageNumbers();

  const handlePageClick = (page) => {
    if (page !== currentPage && typeof page === 'number') {
      onPageChange(page);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <nav className="inline-flex items-center bg-stone-100 rounded-full p-0">
      {/* Previous Button */}
      <button
        onClick={handlePrevious}
        disabled={currentPage === 1}
        className="flex items-center justify-center w-9 h-6 text-gray-600 hover:text-gray-900 hover:bg-blue-200 disabled:text-gray-400 disabled:cursor-not-allowed rounded-l-full transition-all duration-150 ease-in-out"
      >
        <ChevronLeft className="text-stone-500 w-3 h-3" />
      </button>

      {/* Page Numbers */}
      <div className="flex items-center">
        {pages.map((page, index) => {
          if (page === 'left-dots' || page === 'right-dots') {
            return (
              <div
                key={`dots-${index}`}
                className="flex items-center justify-center w-6 h-6 text-gray-400"
              >
                <MoreHorizontal className="text-stone-500 w-2 h-2" />
              </div>
            );
          }

          const isActive = page === currentPage;

          return (
            <button
              key={page}
              onClick={() => handlePageClick(page)}
              className={`
                flex items-center justify-center w-9 h-6 text-xs font-medium transition-all duration-150 ease-in-out
                ${isActive
                  ? 'bg-blue-100 text-black'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-blue-200'
                }
              `}
            >
              {page}
            </button>
          );
        })}
      </div>

      {/* Next Button */}
      <button
        onClick={handleNext}
        disabled={currentPage === totalPages}
        className="flex items-center justify-center w-9 h-6 text-gray-600 hover:text-gray-900 hover:bg-blue-200 disabled:text-gray-400 disabled:cursor-not-allowed rounded-r-full transition-all duration-150 ease-in-out"
      >
        <ChevronRight className="text-stone-500 w-3 h-3" />
      </button>
    </nav>
  );
};

export default Pagination2;

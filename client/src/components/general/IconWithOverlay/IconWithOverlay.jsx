const IconWithOverlay = ({ icon: Icon, overlayIcon: OverlayIcon, onClick, className = "" }) => {
  return (
    <div className={`flex-shrink-0 relative cursor-pointer ${className}`} onClick={onClick}>
      <Icon className="flex-shrink-0 w-4 h-4 text-gray-600" />
      {OverlayIcon && (
        <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-white border-[0.08rem] border-gray-600 rounded-full flex items-center justify-center">
          <OverlayIcon className="w-1.5 h-1.5 text-gray-600" strokeWidth={4} />
        </div>
      )}
    </div>
  );
};

export default IconWithOverlay;

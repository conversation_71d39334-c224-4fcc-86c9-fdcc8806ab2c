# Simple Command Queue Implementation

## Overview

This is a simplified command processing system for the epikbox proxy service. Commands are queued in Redis for online boxes or stored in MongoDB for offline boxes. When boxes come online, scheduled commands are automatically processed.

## Architecture

### Core Components

1. **Command Service** (`pkg/command/service.go`)
   - Handles command execution requests
   - Manages command status tracking
   - Box online/offline notifications

2. **Command Processor** (`pkg/command/processor.go`)
   - Processes commands from Redis queues
   - Makes HTTP calls to boxes
   - Updates command status

3. **Redis Client** (`pkg/redis/redis.go`)
   - Global Redis connection
   - Command queuing with priority
   - Box online status tracking

4. **Webhook Handler** (`pkg/command/webhook.go`)
   - HTTP endpoint for box online notifications
   - Simple webhook processing

5. **Types** (`pkg/command/types.go`)
   - Command data structures
   - Status constants

## Key Features

### ✅ **Simple Architecture**
- **Redis**: Active command queuing for online boxes
- **MongoDB**: Persistent storage for offline box commands
- **No complex workers or managers**

### ✅ **Basic Command Processing**
- **Priority-based queuing** (1-5 priority levels)
- **Simple command execution**
- **Status tracking**

### ✅ **Box Status Management**
- **Check online status when command submitted**
- **Wait 1 hour for box to come online**
- **Webhook notification for box online status**

### ✅ **Clean Integration**
- **Uses existing MongoDB connection**
- **Uses existing HTTP client**
- **Optional activation via environment variable**

## Configuration

### Environment Variables

```bash
# Enable command system
ENABLE_COMMANDS=true

# Redis configuration
REDIS_ADDRESS=localhost:6379
REDIS_PASSWORD=your_password
```

### Default Configuration

The system uses existing MongoDB connection and simple defaults:
- **Redis TTL**: 1 hour for commands
- **Box online check**: 30 second intervals
- **Offline wait time**: 1 hour maximum

## API Usage

### Command Service Methods

```go
// Execute a command
service := command.NewService()
req := &command.CommandRequest{
    BoxID:    "box-001",
    Method:   "GET",
    Path:     "/status",
    Priority: 3,
}
resp, err := service.ExecuteCommand(ctx, req)

// Check command status
cmd, err := service.GetCommandStatus(ctx, "command-id")

// Notify box online
err := service.NotifyBoxOnline(ctx, "box-001")
```

### Webhook Endpoint
- `POST /webhook/box-online` - Box online notification

## How It Works

### Command Flow

1. **Command Submission**:
   - Check if box is online in Redis
   - If online: Queue command in Redis with priority
   - If offline: Store in MongoDB as "scheduled"

2. **Online Box Processing**:
   - Processor polls Redis queues every 5 seconds
   - Executes highest priority commands first
   - Updates status in both Redis and MongoDB

3. **Offline Box Handling**:
   - Wait up to 1 hour for box to come online
   - Check every 30 seconds
   - When box comes online, move scheduled commands to Redis

4. **Box Online Notification**:
   - Webhook endpoint receives box online notification
   - Marks box as online in Redis (5 minute TTL)
   - Processes any scheduled commands for that box

### Data Storage

**Redis**:
- `box:online:{boxId}` - Box online status (5 min TTL)
- `queue:commands:{boxId}` - Priority queue of commands
- `command:{commandId}` - Command data (1 hour TTL)

**MongoDB**:
- `commands` collection - Persistent command storage
- Used for scheduled commands and history

## Integration

The command system is integrated into the existing proxy service:

1. **Optional Activation** - Set `ENABLE_COMMANDS=true`
2. **Uses Existing Infrastructure**:
   - MongoDB connection from `pkg/db`
   - HTTP client from `pkg/boxes`
   - No separate HTTP server needed

3. **Simple Integration**:
   - Command processor starts with main server
   - No complex configuration required
   - Follows existing project conventions

## Testing

```bash
# Build the project
go build ./cmd/edge

# Test command functionality (requires Redis)
ENABLE_COMMANDS=true REDIS_ADDRESS=localhost:6379 ./edge
```

## Production Deployment

1. **Enable Redis**: Ensure Redis is available
2. **Set Environment**: `ENABLE_COMMANDS=true`
3. **Configure Redis**: Set `REDIS_ADDRESS` and `REDIS_PASSWORD`
4. **Deploy**: The system will start automatically

The implementation is simple, follows your project conventions, and provides the core functionality you requested without unnecessary complexity.

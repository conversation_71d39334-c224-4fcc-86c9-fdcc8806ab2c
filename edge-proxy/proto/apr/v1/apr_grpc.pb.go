// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: proto/apr/v1/apr.proto

package aprv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	APRService_ToggleAPR_FullMethodName       = "/apr.v1.APRService/ToggleAPR"
	APRService_APRStatus_FullMethodName       = "/apr.v1.APRService/APRStatus"
	APRService_APRTestResults_FullMethodName  = "/apr.v1.APRService/APRTestResults"
	APRService_InitiateAprTest_FullMethodName = "/apr.v1.APRService/InitiateAprTest"
)

// APRServiceClient is the client API for APRService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// APR Service - Dedicated service for APR operations
type APRServiceClient interface {
	// Toggle APR on/off for a specific port
	ToggleAPR(ctx context.Context, in *APRToggleRequest, opts ...grpc.CallOption) (*APRToggleResponse, error)
	// Get current APR status for a port
	APRStatus(ctx context.Context, in *APRStatusRequest, opts ...grpc.CallOption) (*APRStatusResponse, error)
	// Get APR test results for a port
	APRTestResults(ctx context.Context, in *APRTestResultRequest, opts ...grpc.CallOption) (*APRTestResultResponse, error)
	// Initiate APR test (legacy method)
	InitiateAprTest(ctx context.Context, in *InitiateAprRequest, opts ...grpc.CallOption) (*InitiateAprResponse, error)
}

type aPRServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAPRServiceClient(cc grpc.ClientConnInterface) APRServiceClient {
	return &aPRServiceClient{cc}
}

func (c *aPRServiceClient) ToggleAPR(ctx context.Context, in *APRToggleRequest, opts ...grpc.CallOption) (*APRToggleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(APRToggleResponse)
	err := c.cc.Invoke(ctx, APRService_ToggleAPR_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPRServiceClient) APRStatus(ctx context.Context, in *APRStatusRequest, opts ...grpc.CallOption) (*APRStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(APRStatusResponse)
	err := c.cc.Invoke(ctx, APRService_APRStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPRServiceClient) APRTestResults(ctx context.Context, in *APRTestResultRequest, opts ...grpc.CallOption) (*APRTestResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(APRTestResultResponse)
	err := c.cc.Invoke(ctx, APRService_APRTestResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aPRServiceClient) InitiateAprTest(ctx context.Context, in *InitiateAprRequest, opts ...grpc.CallOption) (*InitiateAprResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InitiateAprResponse)
	err := c.cc.Invoke(ctx, APRService_InitiateAprTest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// APRServiceServer is the server API for APRService service.
// All implementations must embed UnimplementedAPRServiceServer
// for forward compatibility.
//
// APR Service - Dedicated service for APR operations
type APRServiceServer interface {
	// Toggle APR on/off for a specific port
	ToggleAPR(context.Context, *APRToggleRequest) (*APRToggleResponse, error)
	// Get current APR status for a port
	APRStatus(context.Context, *APRStatusRequest) (*APRStatusResponse, error)
	// Get APR test results for a port
	APRTestResults(context.Context, *APRTestResultRequest) (*APRTestResultResponse, error)
	// Initiate APR test (legacy method)
	InitiateAprTest(context.Context, *InitiateAprRequest) (*InitiateAprResponse, error)
	mustEmbedUnimplementedAPRServiceServer()
}

// UnimplementedAPRServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAPRServiceServer struct{}

func (UnimplementedAPRServiceServer) ToggleAPR(context.Context, *APRToggleRequest) (*APRToggleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ToggleAPR not implemented")
}
func (UnimplementedAPRServiceServer) APRStatus(context.Context, *APRStatusRequest) (*APRStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APRStatus not implemented")
}
func (UnimplementedAPRServiceServer) APRTestResults(context.Context, *APRTestResultRequest) (*APRTestResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APRTestResults not implemented")
}
func (UnimplementedAPRServiceServer) InitiateAprTest(context.Context, *InitiateAprRequest) (*InitiateAprResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateAprTest not implemented")
}
func (UnimplementedAPRServiceServer) mustEmbedUnimplementedAPRServiceServer() {}
func (UnimplementedAPRServiceServer) testEmbeddedByValue()                    {}

// UnsafeAPRServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to APRServiceServer will
// result in compilation errors.
type UnsafeAPRServiceServer interface {
	mustEmbedUnimplementedAPRServiceServer()
}

func RegisterAPRServiceServer(s grpc.ServiceRegistrar, srv APRServiceServer) {
	// If the following call pancis, it indicates UnimplementedAPRServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&APRService_ServiceDesc, srv)
}

func _APRService_ToggleAPR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(APRToggleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APRServiceServer).ToggleAPR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APRService_ToggleAPR_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APRServiceServer).ToggleAPR(ctx, req.(*APRToggleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APRService_APRStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(APRStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APRServiceServer).APRStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APRService_APRStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APRServiceServer).APRStatus(ctx, req.(*APRStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APRService_APRTestResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(APRTestResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APRServiceServer).APRTestResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APRService_APRTestResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APRServiceServer).APRTestResults(ctx, req.(*APRTestResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _APRService_InitiateAprTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateAprRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(APRServiceServer).InitiateAprTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: APRService_InitiateAprTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(APRServiceServer).InitiateAprTest(ctx, req.(*InitiateAprRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// APRService_ServiceDesc is the grpc.ServiceDesc for APRService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var APRService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "apr.v1.APRService",
	HandlerType: (*APRServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ToggleAPR",
			Handler:    _APRService_ToggleAPR_Handler,
		},
		{
			MethodName: "APRStatus",
			Handler:    _APRService_APRStatus_Handler,
		},
		{
			MethodName: "APRTestResults",
			Handler:    _APRService_APRTestResults_Handler,
		},
		{
			MethodName: "InitiateAprTest",
			Handler:    _APRService_InitiateAprTest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/apr/v1/apr.proto",
}

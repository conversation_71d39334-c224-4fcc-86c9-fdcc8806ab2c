syntax = "proto3";

package apr.v1;

option go_package = "github.com/EPIKio/myepikV2/edge/proto/apr/v1;aprv1";

// APR (Alarm Panel Relay) Protocol Definitions
// This file contains only APR-related message types and service definitions

// APR Toggle Request - Enable or disable APR on a specific port
message APRToggleRequest {
  string serial_number = 1;
  string port = 2;
  bool enable = 3;
}

// APR Toggle Response - Result of APR toggle operation
message APRToggleResponse {
  bool status = 1;
  string response = 2;
}

// APR Test Initiation Request - Start a 10-minute APR test session
message InitiateAprRequest {
  string serial_number = 1;
  string port = 2;
  string companion_port = 3; // Optional companion port to disable during test
}

// APR Test Initiation Response - Result of test start operation
message InitiateAprResponse {
  bool status = 1;
  string response = 2;
}

// APR Test Result Request - Get test results for a specific port
message APRTestResultRequest {
  string serial_number = 1;
  string port = 2;
}

// APR Test Result Response - Test results and status
message APRTestResultResponse {
  bool result = 1;
  string response = 2;
  string timestamp = 3;
}

// APR Status Request - Get current APR status for a port
message APRStatusRequest {
  string serial_number = 1;
  string port = 2;
}

// APR Status Response - Current APR status
message APRStatusResponse {
  bool status = 1;
  string response = 2;
}

// APR Test Start Request - Start APR test with companion port management
message APRTestStartRequest {
  string serial_number = 1;
  string port = 2;
  string companion_port = 3; // Optional companion port to disable during test
}

// APR Test Start Response - Result of test start with session info
message APRTestStartResponse {
  string test_id = 1;
  string status = 2;
  int64 start_time = 3;
  string message = 4;
}

// APR Service - Dedicated service for APR operations
service APRService {
  // Toggle APR on/off for a specific port
  rpc ToggleAPR(APRToggleRequest) returns (APRToggleResponse);

  // Get current APR status for a port
  rpc APRStatus(APRStatusRequest) returns (APRStatusResponse);

  // Get APR test results for a port
  rpc APRTestResults(APRTestResultRequest) returns (APRTestResultResponse);

  // Initiate APR test (legacy method)
  rpc InitiateAprTest(InitiateAprRequest) returns (InitiateAprResponse);
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: proto/apr/v1/apr.proto

package aprv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// APR Toggle Request - Enable or disable APR on a specific port
type APRToggleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	Enable        bool                   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRToggleRequest) Reset() {
	*x = APRToggleRequest{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRToggleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRToggleRequest) ProtoMessage() {}

func (x *APRToggleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRToggleRequest.ProtoReflect.Descriptor instead.
func (*APRToggleRequest) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{0}
}

func (x *APRToggleRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *APRToggleRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *APRToggleRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

// APR Toggle Response - Result of APR toggle operation
type APRToggleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Response      string                 `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRToggleResponse) Reset() {
	*x = APRToggleResponse{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRToggleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRToggleResponse) ProtoMessage() {}

func (x *APRToggleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRToggleResponse.ProtoReflect.Descriptor instead.
func (*APRToggleResponse) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{1}
}

func (x *APRToggleResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *APRToggleResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

// APR Test Initiation Request - Start a 10-minute APR test session
type InitiateAprRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	CompanionPort string                 `protobuf:"bytes,3,opt,name=companion_port,json=companionPort,proto3" json:"companion_port,omitempty"` // Optional companion port to disable during test
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitiateAprRequest) Reset() {
	*x = InitiateAprRequest{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitiateAprRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAprRequest) ProtoMessage() {}

func (x *InitiateAprRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAprRequest.ProtoReflect.Descriptor instead.
func (*InitiateAprRequest) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{2}
}

func (x *InitiateAprRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *InitiateAprRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *InitiateAprRequest) GetCompanionPort() string {
	if x != nil {
		return x.CompanionPort
	}
	return ""
}

// APR Test Initiation Response - Result of test start operation
type InitiateAprResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Response      string                 `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitiateAprResponse) Reset() {
	*x = InitiateAprResponse{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitiateAprResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAprResponse) ProtoMessage() {}

func (x *InitiateAprResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAprResponse.ProtoReflect.Descriptor instead.
func (*InitiateAprResponse) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{3}
}

func (x *InitiateAprResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *InitiateAprResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

// APR Test Result Request - Get test results for a specific port
type APRTestResultRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRTestResultRequest) Reset() {
	*x = APRTestResultRequest{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRTestResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRTestResultRequest) ProtoMessage() {}

func (x *APRTestResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRTestResultRequest.ProtoReflect.Descriptor instead.
func (*APRTestResultRequest) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{4}
}

func (x *APRTestResultRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *APRTestResultRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

// APR Test Result Response - Test results and status
type APRTestResultResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        bool                   `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Response      string                 `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
	Timestamp     string                 `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRTestResultResponse) Reset() {
	*x = APRTestResultResponse{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRTestResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRTestResultResponse) ProtoMessage() {}

func (x *APRTestResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRTestResultResponse.ProtoReflect.Descriptor instead.
func (*APRTestResultResponse) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{5}
}

func (x *APRTestResultResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *APRTestResultResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *APRTestResultResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

// APR Status Request - Get current APR status for a port
type APRStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRStatusRequest) Reset() {
	*x = APRStatusRequest{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRStatusRequest) ProtoMessage() {}

func (x *APRStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRStatusRequest.ProtoReflect.Descriptor instead.
func (*APRStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{6}
}

func (x *APRStatusRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *APRStatusRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

// APR Status Response - Current APR status
type APRStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Response      string                 `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRStatusResponse) Reset() {
	*x = APRStatusResponse{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRStatusResponse) ProtoMessage() {}

func (x *APRStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRStatusResponse.ProtoReflect.Descriptor instead.
func (*APRStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{7}
}

func (x *APRStatusResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *APRStatusResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

// APR Test Start Request - Start APR test with companion port management
type APRTestStartRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	CompanionPort string                 `protobuf:"bytes,3,opt,name=companion_port,json=companionPort,proto3" json:"companion_port,omitempty"` // Optional companion port to disable during test
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRTestStartRequest) Reset() {
	*x = APRTestStartRequest{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRTestStartRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRTestStartRequest) ProtoMessage() {}

func (x *APRTestStartRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRTestStartRequest.ProtoReflect.Descriptor instead.
func (*APRTestStartRequest) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{8}
}

func (x *APRTestStartRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *APRTestStartRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *APRTestStartRequest) GetCompanionPort() string {
	if x != nil {
		return x.CompanionPort
	}
	return ""
}

// APR Test Start Response - Result of test start with session info
type APRTestStartResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestId        string                 `protobuf:"bytes,1,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	StartTime     int64                  `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APRTestStartResponse) Reset() {
	*x = APRTestStartResponse{}
	mi := &file_proto_apr_v1_apr_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APRTestStartResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APRTestStartResponse) ProtoMessage() {}

func (x *APRTestStartResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_apr_v1_apr_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APRTestStartResponse.ProtoReflect.Descriptor instead.
func (*APRTestStartResponse) Descriptor() ([]byte, []int) {
	return file_proto_apr_v1_apr_proto_rawDescGZIP(), []int{9}
}

func (x *APRTestStartResponse) GetTestId() string {
	if x != nil {
		return x.TestId
	}
	return ""
}

func (x *APRTestStartResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *APRTestStartResponse) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *APRTestStartResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_proto_apr_v1_apr_proto protoreflect.FileDescriptor

const file_proto_apr_v1_apr_proto_rawDesc = "" +
	"\n" +
	"\x16proto/apr/v1/apr.proto\x12\x06apr.v1\"c\n" +
	"\x10APRToggleRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\tR\x04port\x12\x16\n" +
	"\x06enable\x18\x03 \x01(\bR\x06enable\"G\n" +
	"\x11APRToggleResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x1a\n" +
	"\bresponse\x18\x02 \x01(\tR\bresponse\"t\n" +
	"\x12InitiateAprRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\tR\x04port\x12%\n" +
	"\x0ecompanion_port\x18\x03 \x01(\tR\rcompanionPort\"I\n" +
	"\x13InitiateAprResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x1a\n" +
	"\bresponse\x18\x02 \x01(\tR\bresponse\"O\n" +
	"\x14APRTestResultRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\tR\x04port\"i\n" +
	"\x15APRTestResultResponse\x12\x16\n" +
	"\x06result\x18\x01 \x01(\bR\x06result\x12\x1a\n" +
	"\bresponse\x18\x02 \x01(\tR\bresponse\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\tR\ttimestamp\"K\n" +
	"\x10APRStatusRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\tR\x04port\"G\n" +
	"\x11APRStatusResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x1a\n" +
	"\bresponse\x18\x02 \x01(\tR\bresponse\"u\n" +
	"\x13APRTestStartRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\tR\x04port\x12%\n" +
	"\x0ecompanion_port\x18\x03 \x01(\tR\rcompanionPort\"\x80\x01\n" +
	"\x14APRTestStartResponse\x12\x17\n" +
	"\atest_id\x18\x01 \x01(\tR\x06testId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\x03R\tstartTime\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage2\xab\x02\n" +
	"\n" +
	"APRService\x12@\n" +
	"\tToggleAPR\x12\x18.apr.v1.APRToggleRequest\x1a\x19.apr.v1.APRToggleResponse\x12@\n" +
	"\tAPRStatus\x12\x18.apr.v1.APRStatusRequest\x1a\x19.apr.v1.APRStatusResponse\x12M\n" +
	"\x0eAPRTestResults\x12\x1c.apr.v1.APRTestResultRequest\x1a\x1d.apr.v1.APRTestResultResponse\x12J\n" +
	"\x0fInitiateAprTest\x12\x1a.apr.v1.InitiateAprRequest\x1a\x1b.apr.v1.InitiateAprResponseB4Z2github.com/EPIKio/myepikV2/edge/proto/apr/v1;aprv1b\x06proto3"

var (
	file_proto_apr_v1_apr_proto_rawDescOnce sync.Once
	file_proto_apr_v1_apr_proto_rawDescData []byte
)

func file_proto_apr_v1_apr_proto_rawDescGZIP() []byte {
	file_proto_apr_v1_apr_proto_rawDescOnce.Do(func() {
		file_proto_apr_v1_apr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_apr_v1_apr_proto_rawDesc), len(file_proto_apr_v1_apr_proto_rawDesc)))
	})
	return file_proto_apr_v1_apr_proto_rawDescData
}

var file_proto_apr_v1_apr_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_apr_v1_apr_proto_goTypes = []any{
	(*APRToggleRequest)(nil),      // 0: apr.v1.APRToggleRequest
	(*APRToggleResponse)(nil),     // 1: apr.v1.APRToggleResponse
	(*InitiateAprRequest)(nil),    // 2: apr.v1.InitiateAprRequest
	(*InitiateAprResponse)(nil),   // 3: apr.v1.InitiateAprResponse
	(*APRTestResultRequest)(nil),  // 4: apr.v1.APRTestResultRequest
	(*APRTestResultResponse)(nil), // 5: apr.v1.APRTestResultResponse
	(*APRStatusRequest)(nil),      // 6: apr.v1.APRStatusRequest
	(*APRStatusResponse)(nil),     // 7: apr.v1.APRStatusResponse
	(*APRTestStartRequest)(nil),   // 8: apr.v1.APRTestStartRequest
	(*APRTestStartResponse)(nil),  // 9: apr.v1.APRTestStartResponse
}
var file_proto_apr_v1_apr_proto_depIdxs = []int32{
	0, // 0: apr.v1.APRService.ToggleAPR:input_type -> apr.v1.APRToggleRequest
	6, // 1: apr.v1.APRService.APRStatus:input_type -> apr.v1.APRStatusRequest
	4, // 2: apr.v1.APRService.APRTestResults:input_type -> apr.v1.APRTestResultRequest
	2, // 3: apr.v1.APRService.InitiateAprTest:input_type -> apr.v1.InitiateAprRequest
	1, // 4: apr.v1.APRService.ToggleAPR:output_type -> apr.v1.APRToggleResponse
	7, // 5: apr.v1.APRService.APRStatus:output_type -> apr.v1.APRStatusResponse
	5, // 6: apr.v1.APRService.APRTestResults:output_type -> apr.v1.APRTestResultResponse
	3, // 7: apr.v1.APRService.InitiateAprTest:output_type -> apr.v1.InitiateAprResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_apr_v1_apr_proto_init() }
func file_proto_apr_v1_apr_proto_init() {
	if File_proto_apr_v1_apr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_apr_v1_apr_proto_rawDesc), len(file_proto_apr_v1_apr_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_apr_v1_apr_proto_goTypes,
		DependencyIndexes: file_proto_apr_v1_apr_proto_depIdxs,
		MessageInfos:      file_proto_apr_v1_apr_proto_msgTypes,
	}.Build()
	File_proto_apr_v1_apr_proto = out.File
	file_proto_apr_v1_apr_proto_goTypes = nil
	file_proto_apr_v1_apr_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: proto/edge/v1/notifications.proto

package edgev1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ----------Request Constructs-----------
// DeviceRequest represents a request to proxy to an edge device.
type DeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Payload       string                 `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceRequest) Reset() {
	*x = DeviceRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceRequest) ProtoMessage() {}

func (x *DeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceRequest.ProtoReflect.Descriptor instead.
func (*DeviceRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *DeviceRequest) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

type EnableDisablePortRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Enable        bool                   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnableDisablePortRequest) Reset() {
	*x = EnableDisablePortRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableDisablePortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDisablePortRequest) ProtoMessage() {}

func (x *EnableDisablePortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDisablePortRequest.ProtoReflect.Descriptor instead.
func (*EnableDisablePortRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{1}
}

func (x *EnableDisablePortRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *EnableDisablePortRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *EnableDisablePortRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type EnableDisablePortResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnableDisablePortResponse) Reset() {
	*x = EnableDisablePortResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableDisablePortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDisablePortResponse) ProtoMessage() {}

func (x *EnableDisablePortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDisablePortResponse.ProtoReflect.Descriptor instead.
func (*EnableDisablePortResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{2}
}

func (x *EnableDisablePortResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// EpiRequest represents a request to proxy to an Epi device.
type EpiRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MacAddress    string                 `protobuf:"bytes,1,opt,name=mac_address,json=macAddress,proto3" json:"mac_address,omitempty"`
	SerialNumber  string                 `protobuf:"bytes,2,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Port          string                 `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EpiRequest) Reset() {
	*x = EpiRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EpiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpiRequest) ProtoMessage() {}

func (x *EpiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpiRequest.ProtoReflect.Descriptor instead.
func (*EpiRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{3}
}

func (x *EpiRequest) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

func (x *EpiRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *EpiRequest) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

// Response from the edge device
type PowerSourceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PowerSource   string                 `protobuf:"bytes,1,opt,name=power_source,json=powerSource,proto3" json:"power_source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PowerSourceResponse) Reset() {
	*x = PowerSourceResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PowerSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerSourceResponse) ProtoMessage() {}

func (x *PowerSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerSourceResponse.ProtoReflect.Descriptor instead.
func (*PowerSourceResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{4}
}

func (x *PowerSourceResponse) GetPowerSource() string {
	if x != nil {
		return x.PowerSource
	}
	return ""
}

type ActiveInterfaceResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ActiveInterface string                 `protobuf:"bytes,1,opt,name=active_interface,json=activeInterface,proto3" json:"active_interface,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ActiveInterfaceResponse) Reset() {
	*x = ActiveInterfaceResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveInterfaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveInterfaceResponse) ProtoMessage() {}

func (x *ActiveInterfaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveInterfaceResponse.ProtoReflect.Descriptor instead.
func (*ActiveInterfaceResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{5}
}

func (x *ActiveInterfaceResponse) GetActiveInterface() string {
	if x != nil {
		return x.ActiveInterface
	}
	return ""
}

type LanIpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LanIp         string                 `protobuf:"bytes,1,opt,name=lan_ip,json=lanIp,proto3" json:"lan_ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LanIpResponse) Reset() {
	*x = LanIpResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LanIpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanIpResponse) ProtoMessage() {}

func (x *LanIpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanIpResponse.ProtoReflect.Descriptor instead.
func (*LanIpResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{6}
}

func (x *LanIpResponse) GetLanIp() string {
	if x != nil {
		return x.LanIp
	}
	return ""
}

type PublicIpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PublicIp      string                 `protobuf:"bytes,1,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicIpResponse) Reset() {
	*x = PublicIpResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicIpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicIpResponse) ProtoMessage() {}

func (x *PublicIpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicIpResponse.ProtoReflect.Descriptor instead.
func (*PublicIpResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{7}
}

func (x *PublicIpResponse) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

type SignalStrengthResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SignalStrength string                 `protobuf:"bytes,1,opt,name=signal_strength,json=signalStrength,proto3" json:"signal_strength,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SignalStrengthResponse) Reset() {
	*x = SignalStrengthResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignalStrengthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignalStrengthResponse) ProtoMessage() {}

func (x *SignalStrengthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignalStrengthResponse.ProtoReflect.Descriptor instead.
func (*SignalStrengthResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{8}
}

func (x *SignalStrengthResponse) GetSignalStrength() string {
	if x != nil {
		return x.SignalStrength
	}
	return ""
}

type SimStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimStatus     string                 `protobuf:"bytes,1,opt,name=sim_status,json=simStatus,proto3" json:"sim_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimStatusResponse) Reset() {
	*x = SimStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimStatusResponse) ProtoMessage() {}

func (x *SimStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimStatusResponse.ProtoReflect.Descriptor instead.
func (*SimStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{9}
}

func (x *SimStatusResponse) GetSimStatus() string {
	if x != nil {
		return x.SimStatus
	}
	return ""
}

type ModemInfo struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	ManufacturerPlaceholder string                 `protobuf:"bytes,1,opt,name=manufacturer_placeholder,json=manufacturerPlaceholder,proto3" json:"manufacturer_placeholder,omitempty"`
	Manufacturer            string                 `protobuf:"bytes,2,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	ModelPlaceholder        string                 `protobuf:"bytes,3,opt,name=model_placeholder,json=modelPlaceholder,proto3" json:"model_placeholder,omitempty"`
	Model                   string                 `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	SimPlaceholder          string                 `protobuf:"bytes,5,opt,name=sim_placeholder,json=simPlaceholder,proto3" json:"sim_placeholder,omitempty"`
	Sim                     string                 `protobuf:"bytes,6,opt,name=sim,proto3" json:"sim,omitempty"`
	ImeiPlaceholder         string                 `protobuf:"bytes,7,opt,name=imei_placeholder,json=imeiPlaceholder,proto3" json:"imei_placeholder,omitempty"`
	Imei                    string                 `protobuf:"bytes,8,opt,name=imei,proto3" json:"imei,omitempty"`
	CarrierPlaceholder      string                 `protobuf:"bytes,9,opt,name=carrier_placeholder,json=carrierPlaceholder,proto3" json:"carrier_placeholder,omitempty"`
	Carrier                 string                 `protobuf:"bytes,10,opt,name=carrier,proto3" json:"carrier,omitempty"`
	IpAddressPlaceholder    string                 `protobuf:"bytes,11,opt,name=ipAddress_placeholder,json=ipAddressPlaceholder,proto3" json:"ipAddress_placeholder,omitempty"`
	IpAddress               string                 `protobuf:"bytes,12,opt,name=ipAddress,proto3" json:"ipAddress,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ModemInfo) Reset() {
	*x = ModemInfo{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModemInfo) ProtoMessage() {}

func (x *ModemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModemInfo.ProtoReflect.Descriptor instead.
func (*ModemInfo) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{10}
}

func (x *ModemInfo) GetManufacturerPlaceholder() string {
	if x != nil {
		return x.ManufacturerPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *ModemInfo) GetModelPlaceholder() string {
	if x != nil {
		return x.ModelPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ModemInfo) GetSimPlaceholder() string {
	if x != nil {
		return x.SimPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetSim() string {
	if x != nil {
		return x.Sim
	}
	return ""
}

func (x *ModemInfo) GetImeiPlaceholder() string {
	if x != nil {
		return x.ImeiPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *ModemInfo) GetCarrierPlaceholder() string {
	if x != nil {
		return x.CarrierPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *ModemInfo) GetIpAddressPlaceholder() string {
	if x != nil {
		return x.IpAddressPlaceholder
	}
	return ""
}

func (x *ModemInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type ModemInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ModemInfo     *ModemInfo             `protobuf:"bytes,1,opt,name=modem_info,json=modemInfo,proto3" json:"modem_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModemInfoResponse) Reset() {
	*x = ModemInfoResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModemInfoResponse) ProtoMessage() {}

func (x *ModemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModemInfoResponse.ProtoReflect.Descriptor instead.
func (*ModemInfoResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{11}
}

func (x *ModemInfoResponse) GetModemInfo() *ModemInfo {
	if x != nil {
		return x.ModemInfo
	}
	return nil
}

type SensorDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Power         string                 `protobuf:"bytes,1,opt,name=power,proto3" json:"power,omitempty"`
	Temp          string                 `protobuf:"bytes,2,opt,name=temp,proto3" json:"temp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SensorDataResponse) Reset() {
	*x = SensorDataResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SensorDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SensorDataResponse) ProtoMessage() {}

func (x *SensorDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SensorDataResponse.ProtoReflect.Descriptor instead.
func (*SensorDataResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{12}
}

func (x *SensorDataResponse) GetPower() string {
	if x != nil {
		return x.Power
	}
	return ""
}

func (x *SensorDataResponse) GetTemp() string {
	if x != nil {
		return x.Temp
	}
	return ""
}

type DeviceOnlineResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Online        bool                   `protobuf:"varint,1,opt,name=online,proto3" json:"online,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceOnlineResponse) Reset() {
	*x = DeviceOnlineResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceOnlineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceOnlineResponse) ProtoMessage() {}

func (x *DeviceOnlineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceOnlineResponse.ProtoReflect.Descriptor instead.
func (*DeviceOnlineResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{13}
}

func (x *DeviceOnlineResponse) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

type DeviceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Body          []byte                 `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceResponse) Reset() {
	*x = DeviceResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceResponse) ProtoMessage() {}

func (x *DeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceResponse.ProtoReflect.Descriptor instead.
func (*DeviceResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{14}
}

func (x *DeviceResponse) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

// AsyncRequest represents a request for async processing
type AsyncRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Serial number of the device
	SerialNumber string `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	// Path to forward to the device
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// Request priority (0=low, 1=normal, 2=high, 3=critical)
	Priority      int32 `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsyncRequest) Reset() {
	*x = AsyncRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncRequest) ProtoMessage() {}

func (x *AsyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncRequest.ProtoReflect.Descriptor instead.
func (*AsyncRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{15}
}

func (x *AsyncRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *AsyncRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *AsyncRequest) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

// AsyncResponse contains the request ID for tracking
type AsyncResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique request ID for tracking
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Current status of the request
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// Estimated processing time (if available)
	EstimatedCompletionSeconds int64 `protobuf:"varint,3,opt,name=estimated_completion_seconds,json=estimatedCompletionSeconds,proto3" json:"estimated_completion_seconds,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *AsyncResponse) Reset() {
	*x = AsyncResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncResponse) ProtoMessage() {}

func (x *AsyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncResponse.ProtoReflect.Descriptor instead.
func (*AsyncResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{16}
}

func (x *AsyncResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AsyncResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AsyncResponse) GetEstimatedCompletionSeconds() int64 {
	if x != nil {
		return x.EstimatedCompletionSeconds
	}
	return 0
}

// StatusRequest for checking async request status
type StatusRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request ID to check
	RequestId     string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatusRequest) Reset() {
	*x = StatusRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequest) ProtoMessage() {}

func (x *StatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequest.ProtoReflect.Descriptor instead.
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{17}
}

func (x *StatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// StatusResponse contains the current status and result (if completed)
type StatusResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request ID
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Current status (pending, processing, completed, failed, cancelled)
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// HTTP status code (if completed)
	StatusCode int32 `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	// Response body (if completed)
	Body []byte `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
	// Error message (if failed)
	Error string `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	// Timestamps
	CreatedAt     int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CompletedAt   int64 `protobuf:"varint,8,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatusResponse) Reset() {
	*x = StatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusResponse) ProtoMessage() {}

func (x *StatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusResponse.ProtoReflect.Descriptor instead.
func (*StatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{18}
}

func (x *StatusResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *StatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StatusResponse) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *StatusResponse) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *StatusResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *StatusResponse) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *StatusResponse) GetCompletedAt() int64 {
	if x != nil {
		return x.CompletedAt
	}
	return 0
}

// CancelRequestMessage for cancelling pending async requests
type CancelRequestMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request ID to cancel
	RequestId     string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelRequestMessage) Reset() {
	*x = CancelRequestMessage{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelRequestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelRequestMessage) ProtoMessage() {}

func (x *CancelRequestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelRequestMessage.ProtoReflect.Descriptor instead.
func (*CancelRequestMessage) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{19}
}

func (x *CancelRequestMessage) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// CancelResponse confirms cancellation
type CancelResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request ID that was cancelled
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Whether cancellation was successful
	Cancelled bool `protobuf:"varint,2,opt,name=cancelled,proto3" json:"cancelled,omitempty"`
	// Message about cancellation status
	Message       string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelResponse) Reset() {
	*x = CancelResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelResponse) ProtoMessage() {}

func (x *CancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelResponse.ProtoReflect.Descriptor instead.
func (*CancelResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{20}
}

func (x *CancelResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CancelResponse) GetCancelled() bool {
	if x != nil {
		return x.Cancelled
	}
	return false
}

func (x *CancelResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// DashboardEndpoint represents a single endpoint to call for dashboard data
type DashboardEndpoint struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Path to call on the device (e.g., "/dcavgping", "/status", "/health")
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	// Optional: Custom identifier for this endpoint (defaults to path)
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// Optional: Timeout for this specific endpoint in seconds (defaults to 5)
	TimeoutSeconds int32 `protobuf:"varint,3,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DashboardEndpoint) Reset() {
	*x = DashboardEndpoint{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DashboardEndpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardEndpoint) ProtoMessage() {}

func (x *DashboardEndpoint) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardEndpoint.ProtoReflect.Descriptor instead.
func (*DashboardEndpoint) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{21}
}

func (x *DashboardEndpoint) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DashboardEndpoint) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *DashboardEndpoint) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

// DashboardRequest represents a request to fetch data from multiple endpoints
type DashboardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DashboardRequest) Reset() {
	*x = DashboardRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardRequest) ProtoMessage() {}

func (x *DashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardRequest.ProtoReflect.Descriptor instead.
func (*DashboardRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{22}
}

func (x *DashboardRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

// DashboardEndpointResult represents the result from a single endpoint
type DashboardEndpointResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Identifier of the endpoint (path or custom identifier)
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// Path that was called
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// HTTP status code from the device
	StatusCode int32 `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	// Raw response body from the device
	Body []byte `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
	// Error message if the request failed
	Error string `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	// Duration of the request in milliseconds
	DurationMs int64 `protobuf:"varint,7,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	// Whether this endpoint completed successfully
	Success       bool `protobuf:"varint,8,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DashboardEndpointResult) Reset() {
	*x = DashboardEndpointResult{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DashboardEndpointResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardEndpointResult) ProtoMessage() {}

func (x *DashboardEndpointResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardEndpointResult.ProtoReflect.Descriptor instead.
func (*DashboardEndpointResult) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{23}
}

func (x *DashboardEndpointResult) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *DashboardEndpointResult) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DashboardEndpointResult) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *DashboardEndpointResult) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

func (x *DashboardEndpointResult) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *DashboardEndpointResult) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *DashboardEndpointResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// DashboardResponse contains results from all requested endpoints
type DashboardResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Serial number of the device
	SerialNumber string `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	// Results from each endpoint (in order of completion)
	Results []*DashboardEndpointResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`
	// Total number of endpoints requested
	TotalEndpoints int32 `protobuf:"varint,3,opt,name=total_endpoints,json=totalEndpoints,proto3" json:"total_endpoints,omitempty"`
	// Number of endpoints that completed successfully
	SuccessfulEndpoints int32 `protobuf:"varint,4,opt,name=successful_endpoints,json=successfulEndpoints,proto3" json:"successful_endpoints,omitempty"`
	// Number of endpoints that failed
	FailedEndpoints int32 `protobuf:"varint,5,opt,name=failed_endpoints,json=failedEndpoints,proto3" json:"failed_endpoints,omitempty"`
	// Total duration for all requests in milliseconds
	TotalDurationMs int64 `protobuf:"varint,6,opt,name=total_duration_ms,json=totalDurationMs,proto3" json:"total_duration_ms,omitempty"`
	// Overall success status (true if at least one endpoint succeeded)
	OverallSuccess bool `protobuf:"varint,7,opt,name=overall_success,json=overallSuccess,proto3" json:"overall_success,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DashboardResponse) Reset() {
	*x = DashboardResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResponse) ProtoMessage() {}

func (x *DashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResponse.ProtoReflect.Descriptor instead.
func (*DashboardResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{24}
}

func (x *DashboardResponse) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *DashboardResponse) GetResults() []*DashboardEndpointResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *DashboardResponse) GetTotalEndpoints() int32 {
	if x != nil {
		return x.TotalEndpoints
	}
	return 0
}

func (x *DashboardResponse) GetSuccessfulEndpoints() int32 {
	if x != nil {
		return x.SuccessfulEndpoints
	}
	return 0
}

func (x *DashboardResponse) GetFailedEndpoints() int32 {
	if x != nil {
		return x.FailedEndpoints
	}
	return 0
}

func (x *DashboardResponse) GetTotalDurationMs() int64 {
	if x != nil {
		return x.TotalDurationMs
	}
	return 0
}

func (x *DashboardResponse) GetOverallSuccess() bool {
	if x != nil {
		return x.OverallSuccess
	}
	return false
}

type EchoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoRequest) Reset() {
	*x = EchoRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRequest) ProtoMessage() {}

func (x *EchoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRequest.ProtoReflect.Descriptor instead.
func (*EchoRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{25}
}

func (x *EchoRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type EchoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoResponse) Reset() {
	*x = EchoResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoResponse) ProtoMessage() {}

func (x *EchoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoResponse.ProtoReflect.Descriptor instead.
func (*EchoResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{26}
}

func (x *EchoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DcAvgPingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AtPingAvg     string                 `protobuf:"bytes,1,opt,name=atPingAvg,proto3" json:"atPingAvg,omitempty"`
	BestDC        string                 `protobuf:"bytes,2,opt,name=bestDC,proto3" json:"bestDC,omitempty"`
	BestLatency   string                 `protobuf:"bytes,3,opt,name=bestLatency,proto3" json:"bestLatency,omitempty"`
	ChPingAvg     string                 `protobuf:"bytes,4,opt,name=chPingAvg,proto3" json:"chPingAvg,omitempty"`
	DlPingAvg     string                 `protobuf:"bytes,5,opt,name=dlPingAvg,proto3" json:"dlPingAvg,omitempty"`
	LaPingAvg     string                 `protobuf:"bytes,6,opt,name=laPingAvg,proto3" json:"laPingAvg,omitempty"`
	NyPingAvg     string                 `protobuf:"bytes,7,opt,name=nyPingAvg,proto3" json:"nyPingAvg,omitempty"`
	TimeUpdate    string                 `protobuf:"bytes,8,opt,name=timeUpdate,proto3" json:"timeUpdate,omitempty"`
	Error         string                 `protobuf:"bytes,9,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DcAvgPingResponse) Reset() {
	*x = DcAvgPingResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DcAvgPingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DcAvgPingResponse) ProtoMessage() {}

func (x *DcAvgPingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DcAvgPingResponse.ProtoReflect.Descriptor instead.
func (*DcAvgPingResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{27}
}

func (x *DcAvgPingResponse) GetAtPingAvg() string {
	if x != nil {
		return x.AtPingAvg
	}
	return ""
}

func (x *DcAvgPingResponse) GetBestDC() string {
	if x != nil {
		return x.BestDC
	}
	return ""
}

func (x *DcAvgPingResponse) GetBestLatency() string {
	if x != nil {
		return x.BestLatency
	}
	return ""
}

func (x *DcAvgPingResponse) GetChPingAvg() string {
	if x != nil {
		return x.ChPingAvg
	}
	return ""
}

func (x *DcAvgPingResponse) GetDlPingAvg() string {
	if x != nil {
		return x.DlPingAvg
	}
	return ""
}

func (x *DcAvgPingResponse) GetLaPingAvg() string {
	if x != nil {
		return x.LaPingAvg
	}
	return ""
}

func (x *DcAvgPingResponse) GetNyPingAvg() string {
	if x != nil {
		return x.NyPingAvg
	}
	return ""
}

func (x *DcAvgPingResponse) GetTimeUpdate() string {
	if x != nil {
		return x.TimeUpdate
	}
	return ""
}

func (x *DcAvgPingResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type PingMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*PingMessage_Command
	//	*PingMessage_Line
	Payload       isPingMessage_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingMessage) Reset() {
	*x = PingMessage{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingMessage) ProtoMessage() {}

func (x *PingMessage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingMessage.ProtoReflect.Descriptor instead.
func (*PingMessage) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{28}
}

func (x *PingMessage) GetPayload() isPingMessage_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *PingMessage) GetCommand() *PingCommand {
	if x != nil {
		if x, ok := x.Payload.(*PingMessage_Command); ok {
			return x.Command
		}
	}
	return nil
}

func (x *PingMessage) GetLine() *PingLine {
	if x != nil {
		if x, ok := x.Payload.(*PingMessage_Line); ok {
			return x.Line
		}
	}
	return nil
}

type isPingMessage_Payload interface {
	isPingMessage_Payload()
}

type PingMessage_Command struct {
	Command *PingCommand `protobuf:"bytes,1,opt,name=command,proto3,oneof"`
}

type PingMessage_Line struct {
	Line *PingLine `protobuf:"bytes,2,opt,name=line,proto3,oneof"`
}

func (*PingMessage_Command) isPingMessage_Payload() {}

func (*PingMessage_Line) isPingMessage_Payload() {}

type PingCommand struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"` // required for start
	Stop          bool                   `protobuf:"varint,2,opt,name=stop,proto3" json:"stop,omitempty"`                                    // if true, stop the ping
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`                                         // Optional
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingCommand) Reset() {
	*x = PingCommand{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingCommand) ProtoMessage() {}

func (x *PingCommand) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingCommand.ProtoReflect.Descriptor instead.
func (*PingCommand) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{29}
}

func (x *PingCommand) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *PingCommand) GetStop() bool {
	if x != nil {
		return x.Stop
	}
	return false
}

func (x *PingCommand) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type PingLine struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Line          string                 `protobuf:"bytes,1,opt,name=line,proto3" json:"line,omitempty"`            // line of output from ping
	Completed     bool                   `protobuf:"varint,2,opt,name=completed,proto3" json:"completed,omitempty"` // indicates process finished
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingLine) Reset() {
	*x = PingLine{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingLine) ProtoMessage() {}

func (x *PingLine) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingLine.ProtoReflect.Descriptor instead.
func (*PingLine) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{30}
}

func (x *PingLine) GetLine() string {
	if x != nil {
		return x.Line
	}
	return ""
}

func (x *PingLine) GetCompleted() bool {
	if x != nil {
		return x.Completed
	}
	return false
}

type RegStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Registered    bool                   `protobuf:"varint,1,opt,name=registered,proto3" json:"registered,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegStatusResponse) Reset() {
	*x = RegStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegStatusResponse) ProtoMessage() {}

func (x *RegStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegStatusResponse.ProtoReflect.Descriptor instead.
func (*RegStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{31}
}

func (x *RegStatusResponse) GetRegistered() bool {
	if x != nil {
		return x.Registered
	}
	return false
}

type WanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaceHolder   string                 `protobuf:"bytes,1,opt,name=place_holder,json=placeHolder,proto3" json:"place_holder,omitempty"`
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Subnet        string                 `protobuf:"bytes,3,opt,name=subnet,proto3" json:"subnet,omitempty"`
	Gateway       string                 `protobuf:"bytes,4,opt,name=gateway,proto3" json:"gateway,omitempty"`
	Dns           string                 `protobuf:"bytes,5,opt,name=dns,proto3" json:"dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WanInfo) Reset() {
	*x = WanInfo{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WanInfo) ProtoMessage() {}

func (x *WanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WanInfo.ProtoReflect.Descriptor instead.
func (*WanInfo) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{32}
}

func (x *WanInfo) GetPlaceHolder() string {
	if x != nil {
		return x.PlaceHolder
	}
	return ""
}

func (x *WanInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *WanInfo) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

func (x *WanInfo) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *WanInfo) GetDns() string {
	if x != nil {
		return x.Dns
	}
	return ""
}

type Sp1ServiceStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CallState     string                 `protobuf:"bytes,2,opt,name=call_state,json=callState,proto3" json:"call_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sp1ServiceStatus) Reset() {
	*x = Sp1ServiceStatus{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sp1ServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sp1ServiceStatus) ProtoMessage() {}

func (x *Sp1ServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sp1ServiceStatus.ProtoReflect.Descriptor instead.
func (*Sp1ServiceStatus) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{33}
}

func (x *Sp1ServiceStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Sp1ServiceStatus) GetCallState() string {
	if x != nil {
		return x.CallState
	}
	return ""
}

type Sp2ServiceStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CallState     string                 `protobuf:"bytes,2,opt,name=call_state,json=callState,proto3" json:"call_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Sp2ServiceStatus) Reset() {
	*x = Sp2ServiceStatus{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sp2ServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sp2ServiceStatus) ProtoMessage() {}

func (x *Sp2ServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sp2ServiceStatus.ProtoReflect.Descriptor instead.
func (*Sp2ServiceStatus) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{34}
}

func (x *Sp2ServiceStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Sp2ServiceStatus) GetCallState() string {
	if x != nil {
		return x.CallState
	}
	return ""
}

type ObiTalkServiceStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaceHolder   string                 `protobuf:"bytes,1,opt,name=place_holder,json=placeHolder,proto3" json:"place_holder,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ObiTalkServiceStatus) Reset() {
	*x = ObiTalkServiceStatus{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObiTalkServiceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObiTalkServiceStatus) ProtoMessage() {}

func (x *ObiTalkServiceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObiTalkServiceStatus.ProtoReflect.Descriptor instead.
func (*ObiTalkServiceStatus) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{35}
}

func (x *ObiTalkServiceStatus) GetPlaceHolder() string {
	if x != nil {
		return x.PlaceHolder
	}
	return ""
}

func (x *ObiTalkServiceStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type PortRegStatusResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	WanInfo              *WanInfo               `protobuf:"bytes,1,opt,name=wanInfo,proto3" json:"wanInfo,omitempty"`
	Sp1ServiceStatus     *Sp1ServiceStatus      `protobuf:"bytes,2,opt,name=sp1ServiceStatus,proto3" json:"sp1ServiceStatus,omitempty"`
	Sp2ServiceStatus     *Sp2ServiceStatus      `protobuf:"bytes,3,opt,name=sp2ServiceStatus,proto3" json:"sp2ServiceStatus,omitempty"`
	ObiTalkServiceStatus *ObiTalkServiceStatus  `protobuf:"bytes,4,opt,name=obiTalkServiceStatus,proto3" json:"obiTalkServiceStatus,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PortRegStatusResponse) Reset() {
	*x = PortRegStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortRegStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortRegStatusResponse) ProtoMessage() {}

func (x *PortRegStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortRegStatusResponse.ProtoReflect.Descriptor instead.
func (*PortRegStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{36}
}

func (x *PortRegStatusResponse) GetWanInfo() *WanInfo {
	if x != nil {
		return x.WanInfo
	}
	return nil
}

func (x *PortRegStatusResponse) GetSp1ServiceStatus() *Sp1ServiceStatus {
	if x != nil {
		return x.Sp1ServiceStatus
	}
	return nil
}

func (x *PortRegStatusResponse) GetSp2ServiceStatus() *Sp2ServiceStatus {
	if x != nil {
		return x.Sp2ServiceStatus
	}
	return nil
}

func (x *PortRegStatusResponse) GetObiTalkServiceStatus() *ObiTalkServiceStatus {
	if x != nil {
		return x.ObiTalkServiceStatus
	}
	return nil
}

type PortStatus struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	State          string                 `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	LoopCurrent    string                 `protobuf:"bytes,3,opt,name=loopCurrent,proto3" json:"loopCurrent,omitempty"`
	Vbat           string                 `protobuf:"bytes,4,opt,name=vbat,proto3" json:"vbat,omitempty"`
	TipRingVoltage string                 `protobuf:"bytes,5,opt,name=tipRingVoltage,proto3" json:"tipRingVoltage,omitempty"`
	LastCallerInfo string                 `protobuf:"bytes,6,opt,name=lastCallerInfo,proto3" json:"lastCallerInfo,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PortStatus) Reset() {
	*x = PortStatus{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortStatus) ProtoMessage() {}

func (x *PortStatus) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortStatus.ProtoReflect.Descriptor instead.
func (*PortStatus) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{37}
}

func (x *PortStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PortStatus) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *PortStatus) GetLoopCurrent() string {
	if x != nil {
		return x.LoopCurrent
	}
	return ""
}

func (x *PortStatus) GetVbat() string {
	if x != nil {
		return x.Vbat
	}
	return ""
}

func (x *PortStatus) GetTipRingVoltage() string {
	if x != nil {
		return x.TipRingVoltage
	}
	return ""
}

func (x *PortStatus) GetLastCallerInfo() string {
	if x != nil {
		return x.LastCallerInfo
	}
	return ""
}

type PortStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PortStatus    []*PortStatus          `protobuf:"bytes,1,rep,name=portStatus,proto3" json:"portStatus,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortStatusResponse) Reset() {
	*x = PortStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortStatusResponse) ProtoMessage() {}

func (x *PortStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortStatusResponse.ProtoReflect.Descriptor instead.
func (*PortStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{38}
}

func (x *PortStatusResponse) GetPortStatus() []*PortStatus {
	if x != nil {
		return x.PortStatus
	}
	return nil
}

type LiveEpisResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Epis          map[string]string      `protobuf:"bytes,1,rep,name=epis,proto3" json:"epis,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // lowercase 'epis' is safer
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LiveEpisResponse) Reset() {
	*x = LiveEpisResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LiveEpisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiveEpisResponse) ProtoMessage() {}

func (x *LiveEpisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiveEpisResponse.ProtoReflect.Descriptor instead.
func (*LiveEpisResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{39}
}

func (x *LiveEpisResponse) GetEpis() map[string]string {
	if x != nil {
		return x.Epis
	}
	return nil
}

type WifiStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ErrorMsg      string                 `protobuf:"bytes,1,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	Gateway       string                 `protobuf:"bytes,2,opt,name=gateway,proto3" json:"gateway,omitempty"`
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Mode          string                 `protobuf:"bytes,4,opt,name=mode,proto3" json:"mode,omitempty"`
	Password      string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	SSID          string                 `protobuf:"bytes,6,opt,name=SSID,proto3" json:"SSID,omitempty"`
	SecMode       string                 `protobuf:"bytes,7,opt,name=sec_mode,json=secMode,proto3" json:"sec_mode,omitempty"`
	Status        string                 `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	Subnet        string                 `protobuf:"bytes,9,opt,name=subnet,proto3" json:"subnet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WifiStatusResponse) Reset() {
	*x = WifiStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WifiStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WifiStatusResponse) ProtoMessage() {}

func (x *WifiStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WifiStatusResponse.ProtoReflect.Descriptor instead.
func (*WifiStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{40}
}

func (x *WifiStatusResponse) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *WifiStatusResponse) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *WifiStatusResponse) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *WifiStatusResponse) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *WifiStatusResponse) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *WifiStatusResponse) GetSSID() string {
	if x != nil {
		return x.SSID
	}
	return ""
}

func (x *WifiStatusResponse) GetSecMode() string {
	if x != nil {
		return x.SecMode
	}
	return ""
}

func (x *WifiStatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WifiStatusResponse) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

type NetworkInterfaceObj struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Interface     string                 `protobuf:"bytes,1,opt,name=interface,proto3" json:"interface,omitempty"`
	Internet      string                 `protobuf:"bytes,2,opt,name=internet,proto3" json:"internet,omitempty"`
	Icmp          string                 `protobuf:"bytes,3,opt,name=icmp,proto3" json:"icmp,omitempty"`
	Wg            string                 `protobuf:"bytes,4,opt,name=wg,proto3" json:"wg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkInterfaceObj) Reset() {
	*x = NetworkInterfaceObj{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkInterfaceObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkInterfaceObj) ProtoMessage() {}

func (x *NetworkInterfaceObj) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkInterfaceObj.ProtoReflect.Descriptor instead.
func (*NetworkInterfaceObj) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{41}
}

func (x *NetworkInterfaceObj) GetInterface() string {
	if x != nil {
		return x.Interface
	}
	return ""
}

func (x *NetworkInterfaceObj) GetInternet() string {
	if x != nil {
		return x.Internet
	}
	return ""
}

func (x *NetworkInterfaceObj) GetIcmp() string {
	if x != nil {
		return x.Icmp
	}
	return ""
}

func (x *NetworkInterfaceObj) GetWg() string {
	if x != nil {
		return x.Wg
	}
	return ""
}

type NetworkInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dns           string                 `protobuf:"bytes,1,opt,name=dns,proto3" json:"dns,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Interfaces    []*NetworkInterfaceObj `protobuf:"bytes,3,rep,name=interfaces,proto3" json:"interfaces,omitempty"`
	Timestamp     string                 `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkInfoResponse) Reset() {
	*x = NetworkInfoResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkInfoResponse) ProtoMessage() {}

func (x *NetworkInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkInfoResponse.ProtoReflect.Descriptor instead.
func (*NetworkInfoResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{42}
}

func (x *NetworkInfoResponse) GetDns() string {
	if x != nil {
		return x.Dns
	}
	return ""
}

func (x *NetworkInfoResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *NetworkInfoResponse) GetInterfaces() []*NetworkInterfaceObj {
	if x != nil {
		return x.Interfaces
	}
	return nil
}

func (x *NetworkInfoResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

type DeviceNightlyUpdateTimeResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceUpdateTime string                 `protobuf:"bytes,1,opt,name=device_update_time,json=deviceUpdateTime,proto3" json:"device_update_time,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DeviceNightlyUpdateTimeResponse) Reset() {
	*x = DeviceNightlyUpdateTimeResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceNightlyUpdateTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceNightlyUpdateTimeResponse) ProtoMessage() {}

func (x *DeviceNightlyUpdateTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceNightlyUpdateTimeResponse.ProtoReflect.Descriptor instead.
func (*DeviceNightlyUpdateTimeResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{43}
}

func (x *DeviceNightlyUpdateTimeResponse) GetDeviceUpdateTime() string {
	if x != nil {
		return x.DeviceUpdateTime
	}
	return ""
}

type PortForwardObj struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SrcIP         string                 `protobuf:"bytes,1,opt,name=srcIP,proto3" json:"srcIP,omitempty"`
	SrcStartPort  int32                  `protobuf:"varint,2,opt,name=srcStartPort,proto3" json:"srcStartPort,omitempty"`
	SrcEndPort    int32                  `protobuf:"varint,3,opt,name=srcEndPort,proto3" json:"srcEndPort,omitempty"`
	DstIP         string                 `protobuf:"bytes,4,opt,name=dstIP,proto3" json:"dstIP,omitempty"`
	DstStartPort  int32                  `protobuf:"varint,5,opt,name=dstStartPort,proto3" json:"dstStartPort,omitempty"`
	DstEndPort    int32                  `protobuf:"varint,6,opt,name=dstEndPort,proto3" json:"dstEndPort,omitempty"`
	Proto         string                 `protobuf:"bytes,7,opt,name=proto,proto3" json:"proto,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortForwardObj) Reset() {
	*x = PortForwardObj{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortForwardObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortForwardObj) ProtoMessage() {}

func (x *PortForwardObj) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortForwardObj.ProtoReflect.Descriptor instead.
func (*PortForwardObj) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{44}
}

func (x *PortForwardObj) GetSrcIP() string {
	if x != nil {
		return x.SrcIP
	}
	return ""
}

func (x *PortForwardObj) GetSrcStartPort() int32 {
	if x != nil {
		return x.SrcStartPort
	}
	return 0
}

func (x *PortForwardObj) GetSrcEndPort() int32 {
	if x != nil {
		return x.SrcEndPort
	}
	return 0
}

func (x *PortForwardObj) GetDstIP() string {
	if x != nil {
		return x.DstIP
	}
	return ""
}

func (x *PortForwardObj) GetDstStartPort() int32 {
	if x != nil {
		return x.DstStartPort
	}
	return 0
}

func (x *PortForwardObj) GetDstEndPort() int32 {
	if x != nil {
		return x.DstEndPort
	}
	return 0
}

func (x *PortForwardObj) GetProto() string {
	if x != nil {
		return x.Proto
	}
	return ""
}

type PortForwardListResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PortForwardList []*PortForwardObj      `protobuf:"bytes,1,rep,name=portForwardList,proto3" json:"portForwardList,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PortForwardListResponse) Reset() {
	*x = PortForwardListResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortForwardListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortForwardListResponse) ProtoMessage() {}

func (x *PortForwardListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortForwardListResponse.ProtoReflect.Descriptor instead.
func (*PortForwardListResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{45}
}

func (x *PortForwardListResponse) GetPortForwardList() []*PortForwardObj {
	if x != nil {
		return x.PortForwardList
	}
	return nil
}

type PriorityInterfaceResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PriorityInterface string                 `protobuf:"bytes,1,opt,name=priorityInterface,proto3" json:"priorityInterface,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PriorityInterfaceResponse) Reset() {
	*x = PriorityInterfaceResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PriorityInterfaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriorityInterfaceResponse) ProtoMessage() {}

func (x *PriorityInterfaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriorityInterfaceResponse.ProtoReflect.Descriptor instead.
func (*PriorityInterfaceResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{46}
}

func (x *PriorityInterfaceResponse) GetPriorityInterface() string {
	if x != nil {
		return x.PriorityInterface
	}
	return ""
}

type PrimarySimResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PrimarySim    string                 `protobuf:"bytes,1,opt,name=primarySim,proto3" json:"primarySim,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrimarySimResponse) Reset() {
	*x = PrimarySimResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimarySimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimarySimResponse) ProtoMessage() {}

func (x *PrimarySimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimarySimResponse.ProtoReflect.Descriptor instead.
func (*PrimarySimResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{47}
}

func (x *PrimarySimResponse) GetPrimarySim() string {
	if x != nil {
		return x.PrimarySim
	}
	return ""
}

type CurrentApnResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Apn           string                 `protobuf:"bytes,1,opt,name=apn,proto3" json:"apn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CurrentApnResponse) Reset() {
	*x = CurrentApnResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurrentApnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrentApnResponse) ProtoMessage() {}

func (x *CurrentApnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrentApnResponse.ProtoReflect.Descriptor instead.
func (*CurrentApnResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{48}
}

func (x *CurrentApnResponse) GetApn() string {
	if x != nil {
		return x.Apn
	}
	return ""
}

type EpikUpdateStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EpikUpdateStatusResponse) Reset() {
	*x = EpikUpdateStatusResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EpikUpdateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpikUpdateStatusResponse) ProtoMessage() {}

func (x *EpikUpdateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpikUpdateStatusResponse.ProtoReflect.Descriptor instead.
func (*EpikUpdateStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{49}
}

func (x *EpikUpdateStatusResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// Returns an object with arbitrary keys of unknown type and string values
type SystemInfoResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The map allows for any string key with a string value
	SysInfo       map[string]string `protobuf:"bytes,1,rep,name=sysInfo,proto3" json:"sysInfo,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SystemInfoResponse) Reset() {
	*x = SystemInfoResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemInfoResponse) ProtoMessage() {}

func (x *SystemInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemInfoResponse.ProtoReflect.Descriptor instead.
func (*SystemInfoResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{50}
}

func (x *SystemInfoResponse) GetSysInfo() map[string]string {
	if x != nil {
		return x.SysInfo
	}
	return nil
}

type SshCommandRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  string                 `protobuf:"bytes,1,opt,name=serial_number,json=serialNumber,proto3" json:"serial_number,omitempty"`
	Command       string                 `protobuf:"bytes,2,opt,name=command,proto3" json:"command,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SshCommandRequest) Reset() {
	*x = SshCommandRequest{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SshCommandRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SshCommandRequest) ProtoMessage() {}

func (x *SshCommandRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SshCommandRequest.ProtoReflect.Descriptor instead.
func (*SshCommandRequest) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{51}
}

func (x *SshCommandRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *SshCommandRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

type SshCommandResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Output        string                 `protobuf:"bytes,1,opt,name=output,proto3" json:"output,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ExitCode      int32                  `protobuf:"varint,3,opt,name=exit_code,json=exitCode,proto3" json:"exit_code,omitempty"`
	DurationMs    int64                  `protobuf:"varint,4,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	Success       bool                   `protobuf:"varint,5,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SshCommandResponse) Reset() {
	*x = SshCommandResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SshCommandResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SshCommandResponse) ProtoMessage() {}

func (x *SshCommandResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SshCommandResponse.ProtoReflect.Descriptor instead.
func (*SshCommandResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{52}
}

func (x *SshCommandResponse) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *SshCommandResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *SshCommandResponse) GetExitCode() int32 {
	if x != nil {
		return x.ExitCode
	}
	return 0
}

func (x *SshCommandResponse) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *SshCommandResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Returns an object with arbitrary keys of unknown type and string values
type DCConnectionStatsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The map allows for any string key with a string value
	DcConnectionStats map[string]string `protobuf:"bytes,1,rep,name=dc_connection_stats,json=dcConnectionStats,proto3" json:"dc_connection_stats,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DCConnectionStatsResponse) Reset() {
	*x = DCConnectionStatsResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DCConnectionStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCConnectionStatsResponse) ProtoMessage() {}

func (x *DCConnectionStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCConnectionStatsResponse.ProtoReflect.Descriptor instead.
func (*DCConnectionStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{53}
}

func (x *DCConnectionStatsResponse) GetDcConnectionStats() map[string]string {
	if x != nil {
		return x.DcConnectionStats
	}
	return nil
}

type DnsCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dns           bool                   `protobuf:"varint,1,opt,name=dns,proto3" json:"dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DnsCheckResponse) Reset() {
	*x = DnsCheckResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DnsCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DnsCheckResponse) ProtoMessage() {}

func (x *DnsCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DnsCheckResponse.ProtoReflect.Descriptor instead.
func (*DnsCheckResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{54}
}

func (x *DnsCheckResponse) GetDns() bool {
	if x != nil {
		return x.Dns
	}
	return false
}

// Info for a single port
type PortInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Port          string                 `protobuf:"bytes,1,opt,name=port,proto3" json:"port,omitempty"`
	CalledId      string                 `protobuf:"bytes,2,opt,name=calledId,proto3" json:"calledId,omitempty"`
	Recording     string                 `protobuf:"bytes,3,opt,name=recording,proto3" json:"recording,omitempty"`
	TrunkType     string                 `protobuf:"bytes,4,opt,name=trunkType,proto3" json:"trunkType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortInfo) Reset() {
	*x = PortInfo{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortInfo) ProtoMessage() {}

func (x *PortInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortInfo.ProtoReflect.Descriptor instead.
func (*PortInfo) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{55}
}

func (x *PortInfo) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *PortInfo) GetCalledId() string {
	if x != nil {
		return x.CalledId
	}
	return ""
}

func (x *PortInfo) GetRecording() string {
	if x != nil {
		return x.Recording
	}
	return ""
}

func (x *PortInfo) GetTrunkType() string {
	if x != nil {
		return x.TrunkType
	}
	return ""
}

// Response for vSwitch tab
type VSwitchTabResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	Registered                 bool                   `protobuf:"varint,1,opt,name=registered,proto3" json:"registered,omitempty"`
	RegisterationConfigCreated bool                   `protobuf:"varint,2,opt,name=registerationConfigCreated,proto3" json:"registerationConfigCreated,omitempty"`
	PortsInfo                  []*PortInfo            `protobuf:"bytes,3,rep,name=portsInfo,proto3" json:"portsInfo,omitempty"`
	PortsConfigCreated         []string               `protobuf:"bytes,4,rep,name=portsConfigCreated,proto3" json:"portsConfigCreated,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *VSwitchTabResponse) Reset() {
	*x = VSwitchTabResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VSwitchTabResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VSwitchTabResponse) ProtoMessage() {}

func (x *VSwitchTabResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VSwitchTabResponse.ProtoReflect.Descriptor instead.
func (*VSwitchTabResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{56}
}

func (x *VSwitchTabResponse) GetRegistered() bool {
	if x != nil {
		return x.Registered
	}
	return false
}

func (x *VSwitchTabResponse) GetRegisterationConfigCreated() bool {
	if x != nil {
		return x.RegisterationConfigCreated
	}
	return false
}

func (x *VSwitchTabResponse) GetPortsInfo() []*PortInfo {
	if x != nil {
		return x.PortsInfo
	}
	return nil
}

func (x *VSwitchTabResponse) GetPortsConfigCreated() []string {
	if x != nil {
		return x.PortsConfigCreated
	}
	return nil
}

type LteAnalyzerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Busy          bool                   `protobuf:"varint,1,opt,name=Busy,proto3" json:"Busy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LteAnalyzerResponse) Reset() {
	*x = LteAnalyzerResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LteAnalyzerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LteAnalyzerResponse) ProtoMessage() {}

func (x *LteAnalyzerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LteAnalyzerResponse.ProtoReflect.Descriptor instead.
func (*LteAnalyzerResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{57}
}

func (x *LteAnalyzerResponse) GetBusy() bool {
	if x != nil {
		return x.Busy
	}
	return false
}

type SpeedTestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DownloadSpeed string                 `protobuf:"bytes,1,opt,name=downloadSpeed,proto3" json:"downloadSpeed,omitempty"`
	UploadSpeed   string                 `protobuf:"bytes,2,opt,name=uploadSpeed,proto3" json:"uploadSpeed,omitempty"`
	Latency       string                 `protobuf:"bytes,3,opt,name=latency,proto3" json:"latency,omitempty"`
	Jitter        string                 `protobuf:"bytes,4,opt,name=jitter,proto3" json:"jitter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpeedTestResponse) Reset() {
	*x = SpeedTestResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpeedTestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedTestResponse) ProtoMessage() {}

func (x *SpeedTestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedTestResponse.ProtoReflect.Descriptor instead.
func (*SpeedTestResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{58}
}

func (x *SpeedTestResponse) GetDownloadSpeed() string {
	if x != nil {
		return x.DownloadSpeed
	}
	return ""
}

func (x *SpeedTestResponse) GetUploadSpeed() string {
	if x != nil {
		return x.UploadSpeed
	}
	return ""
}

func (x *SpeedTestResponse) GetLatency() string {
	if x != nil {
		return x.Latency
	}
	return ""
}

func (x *SpeedTestResponse) GetJitter() string {
	if x != nil {
		return x.Jitter
	}
	return ""
}

type InitLtePerfResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Initiated     string                 `protobuf:"bytes,1,opt,name=initiated,proto3" json:"initiated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitLtePerfResponse) Reset() {
	*x = InitLtePerfResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitLtePerfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitLtePerfResponse) ProtoMessage() {}

func (x *InitLtePerfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitLtePerfResponse.ProtoReflect.Descriptor instead.
func (*InitLtePerfResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{59}
}

func (x *InitLtePerfResponse) GetInitiated() string {
	if x != nil {
		return x.Initiated
	}
	return ""
}

type SimPingInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         string                 `protobuf:"bytes,1,opt,name=Error,proto3" json:"Error,omitempty"`
	Jitter        float64                `protobuf:"fixed64,2,opt,name=Jitter,proto3" json:"Jitter,omitempty"`
	PacketLoss    float64                `protobuf:"fixed64,3,opt,name=PacketLoss,proto3" json:"PacketLoss,omitempty"`
	PingAvg       float64                `protobuf:"fixed64,4,opt,name=PingAvg,proto3" json:"PingAvg,omitempty"`
	SIM           int32                  `protobuf:"varint,5,opt,name=SIM,proto3" json:"SIM,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimPingInfo) Reset() {
	*x = SimPingInfo{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimPingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimPingInfo) ProtoMessage() {}

func (x *SimPingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimPingInfo.ProtoReflect.Descriptor instead.
func (*SimPingInfo) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{60}
}

func (x *SimPingInfo) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *SimPingInfo) GetJitter() float64 {
	if x != nil {
		return x.Jitter
	}
	return 0
}

func (x *SimPingInfo) GetPacketLoss() float64 {
	if x != nil {
		return x.PacketLoss
	}
	return 0
}

func (x *SimPingInfo) GetPingAvg() float64 {
	if x != nil {
		return x.PingAvg
	}
	return 0
}

func (x *SimPingInfo) GetSIM() int32 {
	if x != nil {
		return x.SIM
	}
	return 0
}

type FetchLtePerfResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SimsInfo      []*SimPingInfo         `protobuf:"bytes,1,rep,name=SimsInfo,proto3" json:"SimsInfo,omitempty"`
	TimeStamp     string                 `protobuf:"bytes,2,opt,name=TimeStamp,proto3" json:"TimeStamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchLtePerfResponse) Reset() {
	*x = FetchLtePerfResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchLtePerfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchLtePerfResponse) ProtoMessage() {}

func (x *FetchLtePerfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchLtePerfResponse.ProtoReflect.Descriptor instead.
func (*FetchLtePerfResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{61}
}

func (x *FetchLtePerfResponse) GetSimsInfo() []*SimPingInfo {
	if x != nil {
		return x.SimsInfo
	}
	return nil
}

func (x *FetchLtePerfResponse) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

type PortConfigValuesResponse struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	OnhookVolts              string                 `protobuf:"bytes,1,opt,name=OnhookVolts,proto3" json:"OnhookVolts,omitempty"`
	OffhookCurrent           string                 `protobuf:"bytes,2,opt,name=OffhookCurrent,proto3" json:"OffhookCurrent,omitempty"`
	DtmfDetectLength         string                 `protobuf:"bytes,3,opt,name=DtmfDetectLength,proto3" json:"DtmfDetectLength,omitempty"`
	DtmfDetectGap            string                 `protobuf:"bytes,4,opt,name=DtmfDetectGap,proto3" json:"DtmfDetectGap,omitempty"`
	TxGain                   string                 `protobuf:"bytes,5,opt,name=TxGain,proto3" json:"TxGain,omitempty"`
	RxGain                   string                 `protobuf:"bytes,6,opt,name=RxGain,proto3" json:"RxGain,omitempty"`
	DtmfMethod               string                 `protobuf:"bytes,7,opt,name=DtmfMethod,proto3" json:"DtmfMethod,omitempty"`
	DtmfPlaybackLevel        string                 `protobuf:"bytes,8,opt,name=DtmfPlaybackLevel,proto3" json:"DtmfPlaybackLevel,omitempty"`
	RingVoltage              string                 `protobuf:"bytes,9,opt,name=RingVoltage,proto3" json:"RingVoltage,omitempty"`
	DigitMapShortTimer       string                 `protobuf:"bytes,10,opt,name=DigitMapShortTimer,proto3" json:"DigitMapShortTimer,omitempty"`
	CpcDuration              string                 `protobuf:"bytes,11,opt,name=CpcDuration,proto3" json:"CpcDuration,omitempty"`
	CpcDelayTime             string                 `protobuf:"bytes,12,opt,name=CpcDelayTime,proto3" json:"CpcDelayTime,omitempty"`
	JitterBufferType         string                 `protobuf:"bytes,13,opt,name=JitterBufferType,proto3" json:"JitterBufferType,omitempty"`
	JitterBufferMinDeley     string                 `protobuf:"bytes,14,opt,name=JitterBufferMinDeley,proto3" json:"JitterBufferMinDeley,omitempty"`
	JitterBufferMaxDeley     string                 `protobuf:"bytes,15,opt,name=JitterBufferMaxDeley,proto3" json:"JitterBufferMaxDeley,omitempty"`
	T38Enabled               string                 `protobuf:"bytes,16,opt,name=T38Enabled,proto3" json:"T38Enabled,omitempty"`
	ModemMode                string                 `protobuf:"bytes,17,opt,name=ModemMode,proto3" json:"ModemMode,omitempty"`
	VadEnable                string                 `protobuf:"bytes,18,opt,name=VadEnable,proto3" json:"VadEnable,omitempty"`
	ThreeWayCalling          string                 `protobuf:"bytes,19,opt,name=ThreeWayCalling,proto3" json:"ThreeWayCalling,omitempty"`
	SilenceDetectSensitivity string                 `protobuf:"bytes,20,opt,name=SilenceDetectSensitivity,proto3" json:"SilenceDetectSensitivity,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *PortConfigValuesResponse) Reset() {
	*x = PortConfigValuesResponse{}
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortConfigValuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortConfigValuesResponse) ProtoMessage() {}

func (x *PortConfigValuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_edge_v1_notifications_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortConfigValuesResponse.ProtoReflect.Descriptor instead.
func (*PortConfigValuesResponse) Descriptor() ([]byte, []int) {
	return file_proto_edge_v1_notifications_proto_rawDescGZIP(), []int{62}
}

func (x *PortConfigValuesResponse) GetOnhookVolts() string {
	if x != nil {
		return x.OnhookVolts
	}
	return ""
}

func (x *PortConfigValuesResponse) GetOffhookCurrent() string {
	if x != nil {
		return x.OffhookCurrent
	}
	return ""
}

func (x *PortConfigValuesResponse) GetDtmfDetectLength() string {
	if x != nil {
		return x.DtmfDetectLength
	}
	return ""
}

func (x *PortConfigValuesResponse) GetDtmfDetectGap() string {
	if x != nil {
		return x.DtmfDetectGap
	}
	return ""
}

func (x *PortConfigValuesResponse) GetTxGain() string {
	if x != nil {
		return x.TxGain
	}
	return ""
}

func (x *PortConfigValuesResponse) GetRxGain() string {
	if x != nil {
		return x.RxGain
	}
	return ""
}

func (x *PortConfigValuesResponse) GetDtmfMethod() string {
	if x != nil {
		return x.DtmfMethod
	}
	return ""
}

func (x *PortConfigValuesResponse) GetDtmfPlaybackLevel() string {
	if x != nil {
		return x.DtmfPlaybackLevel
	}
	return ""
}

func (x *PortConfigValuesResponse) GetRingVoltage() string {
	if x != nil {
		return x.RingVoltage
	}
	return ""
}

func (x *PortConfigValuesResponse) GetDigitMapShortTimer() string {
	if x != nil {
		return x.DigitMapShortTimer
	}
	return ""
}

func (x *PortConfigValuesResponse) GetCpcDuration() string {
	if x != nil {
		return x.CpcDuration
	}
	return ""
}

func (x *PortConfigValuesResponse) GetCpcDelayTime() string {
	if x != nil {
		return x.CpcDelayTime
	}
	return ""
}

func (x *PortConfigValuesResponse) GetJitterBufferType() string {
	if x != nil {
		return x.JitterBufferType
	}
	return ""
}

func (x *PortConfigValuesResponse) GetJitterBufferMinDeley() string {
	if x != nil {
		return x.JitterBufferMinDeley
	}
	return ""
}

func (x *PortConfigValuesResponse) GetJitterBufferMaxDeley() string {
	if x != nil {
		return x.JitterBufferMaxDeley
	}
	return ""
}

func (x *PortConfigValuesResponse) GetT38Enabled() string {
	if x != nil {
		return x.T38Enabled
	}
	return ""
}

func (x *PortConfigValuesResponse) GetModemMode() string {
	if x != nil {
		return x.ModemMode
	}
	return ""
}

func (x *PortConfigValuesResponse) GetVadEnable() string {
	if x != nil {
		return x.VadEnable
	}
	return ""
}

func (x *PortConfigValuesResponse) GetThreeWayCalling() string {
	if x != nil {
		return x.ThreeWayCalling
	}
	return ""
}

func (x *PortConfigValuesResponse) GetSilenceDetectSensitivity() string {
	if x != nil {
		return x.SilenceDetectSensitivity
	}
	return ""
}

var File_proto_edge_v1_notifications_proto protoreflect.FileDescriptor

const file_proto_edge_v1_notifications_proto_rawDesc = "" +
	"\n" +
	"!proto/edge/v1/notifications.proto\x12\aedge.v1\"N\n" +
	"\rDeviceRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x18\n" +
	"\apayload\x18\x02 \x01(\tR\apayload\"k\n" +
	"\x18EnableDisablePortRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x16\n" +
	"\x06enable\x18\x03 \x01(\bR\x06enable\"5\n" +
	"\x19EnableDisablePortResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"f\n" +
	"\n" +
	"EpiRequest\x12\x1f\n" +
	"\vmac_address\x18\x01 \x01(\tR\n" +
	"macAddress\x12#\n" +
	"\rserial_number\x18\x02 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04port\x18\x03 \x01(\tR\x04port\"8\n" +
	"\x13PowerSourceResponse\x12!\n" +
	"\fpower_source\x18\x01 \x01(\tR\vpowerSource\"D\n" +
	"\x17ActiveInterfaceResponse\x12)\n" +
	"\x10active_interface\x18\x01 \x01(\tR\x0factiveInterface\"&\n" +
	"\rLanIpResponse\x12\x15\n" +
	"\x06lan_ip\x18\x01 \x01(\tR\x05lanIp\"/\n" +
	"\x10PublicIpResponse\x12\x1b\n" +
	"\tpublic_ip\x18\x01 \x01(\tR\bpublicIp\"A\n" +
	"\x16SignalStrengthResponse\x12'\n" +
	"\x0fsignal_strength\x18\x01 \x01(\tR\x0esignalStrength\"2\n" +
	"\x11SimStatusResponse\x12\x1d\n" +
	"\n" +
	"sim_status\x18\x01 \x01(\tR\tsimStatus\"\xc5\x03\n" +
	"\tModemInfo\x129\n" +
	"\x18manufacturer_placeholder\x18\x01 \x01(\tR\x17manufacturerPlaceholder\x12\"\n" +
	"\fmanufacturer\x18\x02 \x01(\tR\fmanufacturer\x12+\n" +
	"\x11model_placeholder\x18\x03 \x01(\tR\x10modelPlaceholder\x12\x14\n" +
	"\x05model\x18\x04 \x01(\tR\x05model\x12'\n" +
	"\x0fsim_placeholder\x18\x05 \x01(\tR\x0esimPlaceholder\x12\x10\n" +
	"\x03sim\x18\x06 \x01(\tR\x03sim\x12)\n" +
	"\x10imei_placeholder\x18\a \x01(\tR\x0fimeiPlaceholder\x12\x12\n" +
	"\x04imei\x18\b \x01(\tR\x04imei\x12/\n" +
	"\x13carrier_placeholder\x18\t \x01(\tR\x12carrierPlaceholder\x12\x18\n" +
	"\acarrier\x18\n" +
	" \x01(\tR\acarrier\x123\n" +
	"\x15ipAddress_placeholder\x18\v \x01(\tR\x14ipAddressPlaceholder\x12\x1c\n" +
	"\tipAddress\x18\f \x01(\tR\tipAddress\"F\n" +
	"\x11ModemInfoResponse\x121\n" +
	"\n" +
	"modem_info\x18\x01 \x01(\v2\x12.edge.v1.ModemInfoR\tmodemInfo\">\n" +
	"\x12SensorDataResponse\x12\x14\n" +
	"\x05power\x18\x01 \x01(\tR\x05power\x12\x12\n" +
	"\x04temp\x18\x02 \x01(\tR\x04temp\".\n" +
	"\x14DeviceOnlineResponse\x12\x16\n" +
	"\x06online\x18\x01 \x01(\bR\x06online\"$\n" +
	"\x0eDeviceResponse\x12\x12\n" +
	"\x04body\x18\x01 \x01(\fR\x04body\"c\n" +
	"\fAsyncRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04path\x18\x02 \x01(\tR\x04path\x12\x1a\n" +
	"\bpriority\x18\x03 \x01(\x05R\bpriority\"\x88\x01\n" +
	"\rAsyncResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12@\n" +
	"\x1cestimated_completion_seconds\x18\x03 \x01(\x03R\x1aestimatedCompletionSeconds\".\n" +
	"\rStatusRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\"\xd4\x01\n" +
	"\x0eStatusResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1f\n" +
	"\vstatus_code\x18\x03 \x01(\x05R\n" +
	"statusCode\x12\x12\n" +
	"\x04body\x18\x04 \x01(\fR\x04body\x12\x14\n" +
	"\x05error\x18\x06 \x01(\tR\x05error\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12!\n" +
	"\fcompleted_at\x18\b \x01(\x03R\vcompletedAt\"5\n" +
	"\x14CancelRequestMessage\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\"g\n" +
	"\x0eCancelResponse\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x1c\n" +
	"\tcancelled\x18\x02 \x01(\bR\tcancelled\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"p\n" +
	"\x11DashboardEndpoint\x12\x12\n" +
	"\x04path\x18\x01 \x01(\tR\x04path\x12\x1e\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tR\n" +
	"identifier\x12'\n" +
	"\x0ftimeout_seconds\x18\x03 \x01(\x05R\x0etimeoutSeconds\"7\n" +
	"\x10DashboardRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\"\xd3\x01\n" +
	"\x17DashboardEndpointResult\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x12\n" +
	"\x04path\x18\x02 \x01(\tR\x04path\x12\x1f\n" +
	"\vstatus_code\x18\x03 \x01(\x05R\n" +
	"statusCode\x12\x12\n" +
	"\x04body\x18\x04 \x01(\fR\x04body\x12\x14\n" +
	"\x05error\x18\x06 \x01(\tR\x05error\x12\x1f\n" +
	"\vduration_ms\x18\a \x01(\x03R\n" +
	"durationMs\x12\x18\n" +
	"\asuccess\x18\b \x01(\bR\asuccess\"\xd0\x02\n" +
	"\x11DashboardResponse\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12:\n" +
	"\aresults\x18\x02 \x03(\v2 .edge.v1.DashboardEndpointResultR\aresults\x12'\n" +
	"\x0ftotal_endpoints\x18\x03 \x01(\x05R\x0etotalEndpoints\x121\n" +
	"\x14successful_endpoints\x18\x04 \x01(\x05R\x13successfulEndpoints\x12)\n" +
	"\x10failed_endpoints\x18\x05 \x01(\x05R\x0ffailedEndpoints\x12*\n" +
	"\x11total_duration_ms\x18\x06 \x01(\x03R\x0ftotalDurationMs\x12'\n" +
	"\x0foverall_success\x18\a \x01(\bR\x0eoverallSuccess\"'\n" +
	"\vEchoRequest\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"(\n" +
	"\fEchoResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\x99\x02\n" +
	"\x11DcAvgPingResponse\x12\x1c\n" +
	"\tatPingAvg\x18\x01 \x01(\tR\tatPingAvg\x12\x16\n" +
	"\x06bestDC\x18\x02 \x01(\tR\x06bestDC\x12 \n" +
	"\vbestLatency\x18\x03 \x01(\tR\vbestLatency\x12\x1c\n" +
	"\tchPingAvg\x18\x04 \x01(\tR\tchPingAvg\x12\x1c\n" +
	"\tdlPingAvg\x18\x05 \x01(\tR\tdlPingAvg\x12\x1c\n" +
	"\tlaPingAvg\x18\x06 \x01(\tR\tlaPingAvg\x12\x1c\n" +
	"\tnyPingAvg\x18\a \x01(\tR\tnyPingAvg\x12\x1e\n" +
	"\n" +
	"timeUpdate\x18\b \x01(\tR\n" +
	"timeUpdate\x12\x14\n" +
	"\x05error\x18\t \x01(\tR\x05error\"s\n" +
	"\vPingMessage\x120\n" +
	"\acommand\x18\x01 \x01(\v2\x14.edge.v1.PingCommandH\x00R\acommand\x12'\n" +
	"\x04line\x18\x02 \x01(\v2\x11.edge.v1.PingLineH\x00R\x04lineB\t\n" +
	"\apayload\"V\n" +
	"\vPingCommand\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x12\n" +
	"\x04stop\x18\x02 \x01(\bR\x04stop\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\"<\n" +
	"\bPingLine\x12\x12\n" +
	"\x04line\x18\x01 \x01(\tR\x04line\x12\x1c\n" +
	"\tcompleted\x18\x02 \x01(\bR\tcompleted\"3\n" +
	"\x11RegStatusResponse\x12\x1e\n" +
	"\n" +
	"registered\x18\x01 \x01(\bR\n" +
	"registered\"\x80\x01\n" +
	"\aWanInfo\x12!\n" +
	"\fplace_holder\x18\x01 \x01(\tR\vplaceHolder\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12\x16\n" +
	"\x06subnet\x18\x03 \x01(\tR\x06subnet\x12\x18\n" +
	"\agateway\x18\x04 \x01(\tR\agateway\x12\x10\n" +
	"\x03dns\x18\x05 \x01(\tR\x03dns\"I\n" +
	"\x10Sp1ServiceStatus\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"call_state\x18\x02 \x01(\tR\tcallState\"I\n" +
	"\x10Sp2ServiceStatus\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"call_state\x18\x02 \x01(\tR\tcallState\"Q\n" +
	"\x14ObiTalkServiceStatus\x12!\n" +
	"\fplace_holder\x18\x01 \x01(\tR\vplaceHolder\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"\xa4\x02\n" +
	"\x15PortRegStatusResponse\x12*\n" +
	"\awanInfo\x18\x01 \x01(\v2\x10.edge.v1.WanInfoR\awanInfo\x12E\n" +
	"\x10sp1ServiceStatus\x18\x02 \x01(\v2\x19.edge.v1.Sp1ServiceStatusR\x10sp1ServiceStatus\x12E\n" +
	"\x10sp2ServiceStatus\x18\x03 \x01(\v2\x19.edge.v1.Sp2ServiceStatusR\x10sp2ServiceStatus\x12Q\n" +
	"\x14obiTalkServiceStatus\x18\x04 \x01(\v2\x1d.edge.v1.ObiTalkServiceStatusR\x14obiTalkServiceStatus\"\xbc\x01\n" +
	"\n" +
	"PortStatus\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05state\x18\x02 \x01(\tR\x05state\x12 \n" +
	"\vloopCurrent\x18\x03 \x01(\tR\vloopCurrent\x12\x12\n" +
	"\x04vbat\x18\x04 \x01(\tR\x04vbat\x12&\n" +
	"\x0etipRingVoltage\x18\x05 \x01(\tR\x0etipRingVoltage\x12&\n" +
	"\x0elastCallerInfo\x18\x06 \x01(\tR\x0elastCallerInfo\"I\n" +
	"\x12PortStatusResponse\x123\n" +
	"\n" +
	"portStatus\x18\x01 \x03(\v2\x13.edge.v1.PortStatusR\n" +
	"portStatus\"\x84\x01\n" +
	"\x10LiveEpisResponse\x127\n" +
	"\x04epis\x18\x01 \x03(\v2#.edge.v1.LiveEpisResponse.EpisEntryR\x04epis\x1a7\n" +
	"\tEpisEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xea\x01\n" +
	"\x12WifiStatusResponse\x12\x1b\n" +
	"\terror_msg\x18\x01 \x01(\tR\berrorMsg\x12\x18\n" +
	"\agateway\x18\x02 \x01(\tR\agateway\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\x12\x12\n" +
	"\x04mode\x18\x04 \x01(\tR\x04mode\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12\x12\n" +
	"\x04SSID\x18\x06 \x01(\tR\x04SSID\x12\x19\n" +
	"\bsec_mode\x18\a \x01(\tR\asecMode\x12\x16\n" +
	"\x06status\x18\b \x01(\tR\x06status\x12\x16\n" +
	"\x06subnet\x18\t \x01(\tR\x06subnet\"s\n" +
	"\x13NetworkInterfaceObj\x12\x1c\n" +
	"\tinterface\x18\x01 \x01(\tR\tinterface\x12\x1a\n" +
	"\binternet\x18\x02 \x01(\tR\binternet\x12\x12\n" +
	"\x04icmp\x18\x03 \x01(\tR\x04icmp\x12\x0e\n" +
	"\x02wg\x18\x04 \x01(\tR\x02wg\"\x99\x01\n" +
	"\x13NetworkInfoResponse\x12\x10\n" +
	"\x03dns\x18\x01 \x01(\tR\x03dns\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\x12<\n" +
	"\n" +
	"interfaces\x18\x03 \x03(\v2\x1c.edge.v1.NetworkInterfaceObjR\n" +
	"interfaces\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\tR\ttimestamp\"O\n" +
	"\x1fDeviceNightlyUpdateTimeResponse\x12,\n" +
	"\x12device_update_time\x18\x01 \x01(\tR\x10deviceUpdateTime\"\xda\x01\n" +
	"\x0ePortForwardObj\x12\x14\n" +
	"\x05srcIP\x18\x01 \x01(\tR\x05srcIP\x12\"\n" +
	"\fsrcStartPort\x18\x02 \x01(\x05R\fsrcStartPort\x12\x1e\n" +
	"\n" +
	"srcEndPort\x18\x03 \x01(\x05R\n" +
	"srcEndPort\x12\x14\n" +
	"\x05dstIP\x18\x04 \x01(\tR\x05dstIP\x12\"\n" +
	"\fdstStartPort\x18\x05 \x01(\x05R\fdstStartPort\x12\x1e\n" +
	"\n" +
	"dstEndPort\x18\x06 \x01(\x05R\n" +
	"dstEndPort\x12\x14\n" +
	"\x05proto\x18\a \x01(\tR\x05proto\"\\\n" +
	"\x17PortForwardListResponse\x12A\n" +
	"\x0fportForwardList\x18\x01 \x03(\v2\x17.edge.v1.PortForwardObjR\x0fportForwardList\"I\n" +
	"\x19PriorityInterfaceResponse\x12,\n" +
	"\x11priorityInterface\x18\x01 \x01(\tR\x11priorityInterface\"4\n" +
	"\x12PrimarySimResponse\x12\x1e\n" +
	"\n" +
	"primarySim\x18\x01 \x01(\tR\n" +
	"primarySim\"&\n" +
	"\x12CurrentApnResponse\x12\x10\n" +
	"\x03apn\x18\x01 \x01(\tR\x03apn\"2\n" +
	"\x18EpikUpdateStatusResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\x94\x01\n" +
	"\x12SystemInfoResponse\x12B\n" +
	"\asysInfo\x18\x01 \x03(\v2(.edge.v1.SystemInfoResponse.SysInfoEntryR\asysInfo\x1a:\n" +
	"\fSysInfoEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"R\n" +
	"\x11SshCommandRequest\x12#\n" +
	"\rserial_number\x18\x01 \x01(\tR\fserialNumber\x12\x18\n" +
	"\acommand\x18\x02 \x01(\tR\acommand\"\xa9\x01\n" +
	"\x12SshCommandResponse\x12\x16\n" +
	"\x06output\x18\x01 \x01(\tR\x06output\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage\x12\x1b\n" +
	"\texit_code\x18\x03 \x01(\x05R\bexitCode\x12\x1f\n" +
	"\vduration_ms\x18\x04 \x01(\x03R\n" +
	"durationMs\x12\x18\n" +
	"\asuccess\x18\x05 \x01(\bR\asuccess\"\xcc\x01\n" +
	"\x19DCConnectionStatsResponse\x12i\n" +
	"\x13dc_connection_stats\x18\x01 \x03(\v29.edge.v1.DCConnectionStatsResponse.DcConnectionStatsEntryR\x11dcConnectionStats\x1aD\n" +
	"\x16DcConnectionStatsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"$\n" +
	"\x10DnsCheckResponse\x12\x10\n" +
	"\x03dns\x18\x01 \x01(\bR\x03dns\"v\n" +
	"\bPortInfo\x12\x12\n" +
	"\x04port\x18\x01 \x01(\tR\x04port\x12\x1a\n" +
	"\bcalledId\x18\x02 \x01(\tR\bcalledId\x12\x1c\n" +
	"\trecording\x18\x03 \x01(\tR\trecording\x12\x1c\n" +
	"\ttrunkType\x18\x04 \x01(\tR\ttrunkType\"\xd5\x01\n" +
	"\x12VSwitchTabResponse\x12\x1e\n" +
	"\n" +
	"registered\x18\x01 \x01(\bR\n" +
	"registered\x12>\n" +
	"\x1aregisterationConfigCreated\x18\x02 \x01(\bR\x1aregisterationConfigCreated\x12/\n" +
	"\tportsInfo\x18\x03 \x03(\v2\x11.edge.v1.PortInfoR\tportsInfo\x12.\n" +
	"\x12portsConfigCreated\x18\x04 \x03(\tR\x12portsConfigCreated\")\n" +
	"\x13LteAnalyzerResponse\x12\x12\n" +
	"\x04Busy\x18\x01 \x01(\bR\x04Busy\"\x8d\x01\n" +
	"\x11SpeedTestResponse\x12$\n" +
	"\rdownloadSpeed\x18\x01 \x01(\tR\rdownloadSpeed\x12 \n" +
	"\vuploadSpeed\x18\x02 \x01(\tR\vuploadSpeed\x12\x18\n" +
	"\alatency\x18\x03 \x01(\tR\alatency\x12\x16\n" +
	"\x06jitter\x18\x04 \x01(\tR\x06jitter\"3\n" +
	"\x13InitLtePerfResponse\x12\x1c\n" +
	"\tinitiated\x18\x01 \x01(\tR\tinitiated\"\x87\x01\n" +
	"\vSimPingInfo\x12\x14\n" +
	"\x05Error\x18\x01 \x01(\tR\x05Error\x12\x16\n" +
	"\x06Jitter\x18\x02 \x01(\x01R\x06Jitter\x12\x1e\n" +
	"\n" +
	"PacketLoss\x18\x03 \x01(\x01R\n" +
	"PacketLoss\x12\x18\n" +
	"\aPingAvg\x18\x04 \x01(\x01R\aPingAvg\x12\x10\n" +
	"\x03SIM\x18\x05 \x01(\x05R\x03SIM\"f\n" +
	"\x14FetchLtePerfResponse\x120\n" +
	"\bSimsInfo\x18\x01 \x03(\v2\x14.edge.v1.SimPingInfoR\bSimsInfo\x12\x1c\n" +
	"\tTimeStamp\x18\x02 \x01(\tR\tTimeStamp\"\xa2\x06\n" +
	"\x18PortConfigValuesResponse\x12 \n" +
	"\vOnhookVolts\x18\x01 \x01(\tR\vOnhookVolts\x12&\n" +
	"\x0eOffhookCurrent\x18\x02 \x01(\tR\x0eOffhookCurrent\x12*\n" +
	"\x10DtmfDetectLength\x18\x03 \x01(\tR\x10DtmfDetectLength\x12$\n" +
	"\rDtmfDetectGap\x18\x04 \x01(\tR\rDtmfDetectGap\x12\x16\n" +
	"\x06TxGain\x18\x05 \x01(\tR\x06TxGain\x12\x16\n" +
	"\x06RxGain\x18\x06 \x01(\tR\x06RxGain\x12\x1e\n" +
	"\n" +
	"DtmfMethod\x18\a \x01(\tR\n" +
	"DtmfMethod\x12,\n" +
	"\x11DtmfPlaybackLevel\x18\b \x01(\tR\x11DtmfPlaybackLevel\x12 \n" +
	"\vRingVoltage\x18\t \x01(\tR\vRingVoltage\x12.\n" +
	"\x12DigitMapShortTimer\x18\n" +
	" \x01(\tR\x12DigitMapShortTimer\x12 \n" +
	"\vCpcDuration\x18\v \x01(\tR\vCpcDuration\x12\"\n" +
	"\fCpcDelayTime\x18\f \x01(\tR\fCpcDelayTime\x12*\n" +
	"\x10JitterBufferType\x18\r \x01(\tR\x10JitterBufferType\x122\n" +
	"\x14JitterBufferMinDeley\x18\x0e \x01(\tR\x14JitterBufferMinDeley\x122\n" +
	"\x14JitterBufferMaxDeley\x18\x0f \x01(\tR\x14JitterBufferMaxDeley\x12\x1e\n" +
	"\n" +
	"T38Enabled\x18\x10 \x01(\tR\n" +
	"T38Enabled\x12\x1c\n" +
	"\tModemMode\x18\x11 \x01(\tR\tModemMode\x12\x1c\n" +
	"\tVadEnable\x18\x12 \x01(\tR\tVadEnable\x12(\n" +
	"\x0fThreeWayCalling\x18\x13 \x01(\tR\x0fThreeWayCalling\x12:\n" +
	"\x18SilenceDetectSensitivity\x18\x14 \x01(\tR\x18SilenceDetectSensitivity2\xa6\x17\n" +
	"\x0fEdgeDeviceProxy\x129\n" +
	"\aPingBox\x12\x14.edge.v1.PingMessage\x1a\x14.edge.v1.PingMessage(\x010\x01\x12:\n" +
	"\tUnaryEcho\x12\x14.edge.v1.EchoRequest\x1a\x15.edge.v1.EchoResponse\"\x00\x12E\n" +
	"\vPowerSource\x12\x16.edge.v1.DeviceRequest\x1a\x1c.edge.v1.PowerSourceResponse\"\x00\x12M\n" +
	"\x0fActiveInterface\x12\x16.edge.v1.DeviceRequest\x1a .edge.v1.ActiveInterfaceResponse\"\x00\x129\n" +
	"\x05LanIp\x12\x16.edge.v1.DeviceRequest\x1a\x16.edge.v1.LanIpResponse\"\x00\x12?\n" +
	"\bPublicIp\x12\x16.edge.v1.DeviceRequest\x1a\x19.edge.v1.PublicIpResponse\"\x00\x12K\n" +
	"\x0eSignalStrength\x12\x16.edge.v1.DeviceRequest\x1a\x1f.edge.v1.SignalStrengthResponse\"\x00\x12A\n" +
	"\tSimStatus\x12\x16.edge.v1.DeviceRequest\x1a\x1a.edge.v1.SimStatusResponse\"\x00\x12A\n" +
	"\tModemInfo\x12\x16.edge.v1.DeviceRequest\x1a\x1a.edge.v1.ModemInfoResponse\"\x00\x12C\n" +
	"\n" +
	"SensorData\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.SensorDataResponse\"\x00\x12G\n" +
	"\fDeviceOnline\x12\x16.edge.v1.DeviceRequest\x1a\x1d.edge.v1.DeviceOnlineResponse\"\x00\x12A\n" +
	"\tDcAvgPing\x12\x16.edge.v1.DeviceRequest\x1a\x1a.edge.v1.DcAvgPingResponse\"\x00\x12A\n" +
	"\tRegStatus\x12\x16.edge.v1.DeviceRequest\x1a\x1a.edge.v1.RegStatusResponse\"\x00\x12F\n" +
	"\rPortRegStatus\x12\x13.edge.v1.EpiRequest\x1a\x1e.edge.v1.PortRegStatusResponse\"\x00\x12H\n" +
	"\x12PortPhysicalStatus\x12\x13.edge.v1.EpiRequest\x1a\x1b.edge.v1.PortStatusResponse\"\x00\x12\\\n" +
	"\x11EnableDisablePort\x12!.edge.v1.EnableDisablePortRequest\x1a\".edge.v1.EnableDisablePortResponse\"\x00\x12?\n" +
	"\bLiveEpis\x12\x16.edge.v1.DeviceRequest\x1a\x19.edge.v1.LiveEpisResponse\"\x00\x12C\n" +
	"\n" +
	"WifiStatus\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.WifiStatusResponse\"\x00\x12E\n" +
	"\vNetworkInfo\x12\x16.edge.v1.DeviceRequest\x1a\x1c.edge.v1.NetworkInfoResponse\"\x00\x12]\n" +
	"\x17DeviceNightlyUpdateTime\x12\x16.edge.v1.DeviceRequest\x1a(.edge.v1.DeviceNightlyUpdateTimeResponse\"\x00\x12M\n" +
	"\x0fPortForwardList\x12\x16.edge.v1.DeviceRequest\x1a .edge.v1.PortForwardListResponse\"\x00\x12Q\n" +
	"\x11PriorityInterface\x12\x16.edge.v1.DeviceRequest\x1a\".edge.v1.PriorityInterfaceResponse\"\x00\x12C\n" +
	"\n" +
	"PrimarySim\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.PrimarySimResponse\"\x00\x12C\n" +
	"\n" +
	"CurrentApn\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.CurrentApnResponse\"\x00\x12O\n" +
	"\x10EpikUpdateStatus\x12\x16.edge.v1.DeviceRequest\x1a!.edge.v1.EpikUpdateStatusResponse\"\x00\x12C\n" +
	"\n" +
	"SystemInfo\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.SystemInfoResponse\"\x00\x12Q\n" +
	"\x11DCConnectionStats\x12\x16.edge.v1.DeviceRequest\x1a\".edge.v1.DCConnectionStatsResponse\"\x00\x12?\n" +
	"\bDnsCheck\x12\x16.edge.v1.DeviceRequest\x1a\x19.edge.v1.DnsCheckResponse\"\x00\x12C\n" +
	"\n" +
	"VSwitchTab\x12\x16.edge.v1.DeviceRequest\x1a\x1b.edge.v1.VSwitchTabResponse\"\x00\x12E\n" +
	"\vLteAnalyzer\x12\x16.edge.v1.DeviceRequest\x1a\x1c.edge.v1.LteAnalyzerResponse\"\x00\x12A\n" +
	"\tSpeedTest\x12\x16.edge.v1.DeviceRequest\x1a\x1a.edge.v1.SpeedTestResponse\"\x00\x12E\n" +
	"\vInitLtePerf\x12\x16.edge.v1.DeviceRequest\x1a\x1c.edge.v1.InitLtePerfResponse\"\x00\x12G\n" +
	"\fFetchLtePerf\x12\x16.edge.v1.DeviceRequest\x1a\x1d.edge.v1.FetchLtePerfResponse\"\x00\x12L\n" +
	"\x10PortConfigValues\x12\x13.edge.v1.EpiRequest\x1a!.edge.v1.PortConfigValuesResponse\"\x00\x12B\n" +
	"\rHandleRequest\x12\x16.edge.v1.DeviceRequest\x1a\x17.edge.v1.DeviceResponse\"\x00\x12A\n" +
	"\x0eEnqueueRequest\x12\x15.edge.v1.AsyncRequest\x1a\x16.edge.v1.AsyncResponse\"\x00\x12E\n" +
	"\x10GetRequestStatus\x12\x16.edge.v1.StatusRequest\x1a\x17.edge.v1.StatusResponse\"\x00\x12I\n" +
	"\rCancelRequest\x12\x1d.edge.v1.CancelRequestMessage\x1a\x17.edge.v1.CancelResponse\"\x00\x12H\n" +
	"\rDashboardData\x12\x19.edge.v1.DashboardRequest\x1a\x1a.edge.v1.DashboardResponse\"\x00\x12L\n" +
	"\x11ExecuteSshCommand\x12\x1a.edge.v1.SshCommandRequest\x1a\x1b.edge.v1.SshCommandResponse\x12S\n" +
	"\x16ExecuteSshCommandAsync\x12\x1a.edge.v1.SshCommandRequest\x1a\x1b.edge.v1.SshCommandResponse0\x01B6Z4github.com/EPIKio/myepikV2/edge/proto/edge/v1;edgev1b\x06proto3"

var (
	file_proto_edge_v1_notifications_proto_rawDescOnce sync.Once
	file_proto_edge_v1_notifications_proto_rawDescData []byte
)

func file_proto_edge_v1_notifications_proto_rawDescGZIP() []byte {
	file_proto_edge_v1_notifications_proto_rawDescOnce.Do(func() {
		file_proto_edge_v1_notifications_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_edge_v1_notifications_proto_rawDesc), len(file_proto_edge_v1_notifications_proto_rawDesc)))
	})
	return file_proto_edge_v1_notifications_proto_rawDescData
}

var file_proto_edge_v1_notifications_proto_msgTypes = make([]protoimpl.MessageInfo, 66)
var file_proto_edge_v1_notifications_proto_goTypes = []any{
	(*DeviceRequest)(nil),                   // 0: edge.v1.DeviceRequest
	(*EnableDisablePortRequest)(nil),        // 1: edge.v1.EnableDisablePortRequest
	(*EnableDisablePortResponse)(nil),       // 2: edge.v1.EnableDisablePortResponse
	(*EpiRequest)(nil),                      // 3: edge.v1.EpiRequest
	(*PowerSourceResponse)(nil),             // 4: edge.v1.PowerSourceResponse
	(*ActiveInterfaceResponse)(nil),         // 5: edge.v1.ActiveInterfaceResponse
	(*LanIpResponse)(nil),                   // 6: edge.v1.LanIpResponse
	(*PublicIpResponse)(nil),                // 7: edge.v1.PublicIpResponse
	(*SignalStrengthResponse)(nil),          // 8: edge.v1.SignalStrengthResponse
	(*SimStatusResponse)(nil),               // 9: edge.v1.SimStatusResponse
	(*ModemInfo)(nil),                       // 10: edge.v1.ModemInfo
	(*ModemInfoResponse)(nil),               // 11: edge.v1.ModemInfoResponse
	(*SensorDataResponse)(nil),              // 12: edge.v1.SensorDataResponse
	(*DeviceOnlineResponse)(nil),            // 13: edge.v1.DeviceOnlineResponse
	(*DeviceResponse)(nil),                  // 14: edge.v1.DeviceResponse
	(*AsyncRequest)(nil),                    // 15: edge.v1.AsyncRequest
	(*AsyncResponse)(nil),                   // 16: edge.v1.AsyncResponse
	(*StatusRequest)(nil),                   // 17: edge.v1.StatusRequest
	(*StatusResponse)(nil),                  // 18: edge.v1.StatusResponse
	(*CancelRequestMessage)(nil),            // 19: edge.v1.CancelRequestMessage
	(*CancelResponse)(nil),                  // 20: edge.v1.CancelResponse
	(*DashboardEndpoint)(nil),               // 21: edge.v1.DashboardEndpoint
	(*DashboardRequest)(nil),                // 22: edge.v1.DashboardRequest
	(*DashboardEndpointResult)(nil),         // 23: edge.v1.DashboardEndpointResult
	(*DashboardResponse)(nil),               // 24: edge.v1.DashboardResponse
	(*EchoRequest)(nil),                     // 25: edge.v1.EchoRequest
	(*EchoResponse)(nil),                    // 26: edge.v1.EchoResponse
	(*DcAvgPingResponse)(nil),               // 27: edge.v1.DcAvgPingResponse
	(*PingMessage)(nil),                     // 28: edge.v1.PingMessage
	(*PingCommand)(nil),                     // 29: edge.v1.PingCommand
	(*PingLine)(nil),                        // 30: edge.v1.PingLine
	(*RegStatusResponse)(nil),               // 31: edge.v1.RegStatusResponse
	(*WanInfo)(nil),                         // 32: edge.v1.WanInfo
	(*Sp1ServiceStatus)(nil),                // 33: edge.v1.Sp1ServiceStatus
	(*Sp2ServiceStatus)(nil),                // 34: edge.v1.Sp2ServiceStatus
	(*ObiTalkServiceStatus)(nil),            // 35: edge.v1.ObiTalkServiceStatus
	(*PortRegStatusResponse)(nil),           // 36: edge.v1.PortRegStatusResponse
	(*PortStatus)(nil),                      // 37: edge.v1.PortStatus
	(*PortStatusResponse)(nil),              // 38: edge.v1.PortStatusResponse
	(*LiveEpisResponse)(nil),                // 39: edge.v1.LiveEpisResponse
	(*WifiStatusResponse)(nil),              // 40: edge.v1.WifiStatusResponse
	(*NetworkInterfaceObj)(nil),             // 41: edge.v1.NetworkInterfaceObj
	(*NetworkInfoResponse)(nil),             // 42: edge.v1.NetworkInfoResponse
	(*DeviceNightlyUpdateTimeResponse)(nil), // 43: edge.v1.DeviceNightlyUpdateTimeResponse
	(*PortForwardObj)(nil),                  // 44: edge.v1.PortForwardObj
	(*PortForwardListResponse)(nil),         // 45: edge.v1.PortForwardListResponse
	(*PriorityInterfaceResponse)(nil),       // 46: edge.v1.PriorityInterfaceResponse
	(*PrimarySimResponse)(nil),              // 47: edge.v1.PrimarySimResponse
	(*CurrentApnResponse)(nil),              // 48: edge.v1.CurrentApnResponse
	(*EpikUpdateStatusResponse)(nil),        // 49: edge.v1.EpikUpdateStatusResponse
	(*SystemInfoResponse)(nil),              // 50: edge.v1.SystemInfoResponse
	(*SshCommandRequest)(nil),               // 51: edge.v1.SshCommandRequest
	(*SshCommandResponse)(nil),              // 52: edge.v1.SshCommandResponse
	(*DCConnectionStatsResponse)(nil),       // 53: edge.v1.DCConnectionStatsResponse
	(*DnsCheckResponse)(nil),                // 54: edge.v1.DnsCheckResponse
	(*PortInfo)(nil),                        // 55: edge.v1.PortInfo
	(*VSwitchTabResponse)(nil),              // 56: edge.v1.VSwitchTabResponse
	(*LteAnalyzerResponse)(nil),             // 57: edge.v1.LteAnalyzerResponse
	(*SpeedTestResponse)(nil),               // 58: edge.v1.SpeedTestResponse
	(*InitLtePerfResponse)(nil),             // 59: edge.v1.InitLtePerfResponse
	(*SimPingInfo)(nil),                     // 60: edge.v1.SimPingInfo
	(*FetchLtePerfResponse)(nil),            // 61: edge.v1.FetchLtePerfResponse
	(*PortConfigValuesResponse)(nil),        // 62: edge.v1.PortConfigValuesResponse
	nil,                                     // 63: edge.v1.LiveEpisResponse.EpisEntry
	nil,                                     // 64: edge.v1.SystemInfoResponse.SysInfoEntry
	nil,                                     // 65: edge.v1.DCConnectionStatsResponse.DcConnectionStatsEntry
}
var file_proto_edge_v1_notifications_proto_depIdxs = []int32{
	10, // 0: edge.v1.ModemInfoResponse.modem_info:type_name -> edge.v1.ModemInfo
	23, // 1: edge.v1.DashboardResponse.results:type_name -> edge.v1.DashboardEndpointResult
	29, // 2: edge.v1.PingMessage.command:type_name -> edge.v1.PingCommand
	30, // 3: edge.v1.PingMessage.line:type_name -> edge.v1.PingLine
	32, // 4: edge.v1.PortRegStatusResponse.wanInfo:type_name -> edge.v1.WanInfo
	33, // 5: edge.v1.PortRegStatusResponse.sp1ServiceStatus:type_name -> edge.v1.Sp1ServiceStatus
	34, // 6: edge.v1.PortRegStatusResponse.sp2ServiceStatus:type_name -> edge.v1.Sp2ServiceStatus
	35, // 7: edge.v1.PortRegStatusResponse.obiTalkServiceStatus:type_name -> edge.v1.ObiTalkServiceStatus
	37, // 8: edge.v1.PortStatusResponse.portStatus:type_name -> edge.v1.PortStatus
	63, // 9: edge.v1.LiveEpisResponse.epis:type_name -> edge.v1.LiveEpisResponse.EpisEntry
	41, // 10: edge.v1.NetworkInfoResponse.interfaces:type_name -> edge.v1.NetworkInterfaceObj
	44, // 11: edge.v1.PortForwardListResponse.portForwardList:type_name -> edge.v1.PortForwardObj
	64, // 12: edge.v1.SystemInfoResponse.sysInfo:type_name -> edge.v1.SystemInfoResponse.SysInfoEntry
	65, // 13: edge.v1.DCConnectionStatsResponse.dc_connection_stats:type_name -> edge.v1.DCConnectionStatsResponse.DcConnectionStatsEntry
	55, // 14: edge.v1.VSwitchTabResponse.portsInfo:type_name -> edge.v1.PortInfo
	60, // 15: edge.v1.FetchLtePerfResponse.SimsInfo:type_name -> edge.v1.SimPingInfo
	28, // 16: edge.v1.EdgeDeviceProxy.PingBox:input_type -> edge.v1.PingMessage
	25, // 17: edge.v1.EdgeDeviceProxy.UnaryEcho:input_type -> edge.v1.EchoRequest
	0,  // 18: edge.v1.EdgeDeviceProxy.PowerSource:input_type -> edge.v1.DeviceRequest
	0,  // 19: edge.v1.EdgeDeviceProxy.ActiveInterface:input_type -> edge.v1.DeviceRequest
	0,  // 20: edge.v1.EdgeDeviceProxy.LanIp:input_type -> edge.v1.DeviceRequest
	0,  // 21: edge.v1.EdgeDeviceProxy.PublicIp:input_type -> edge.v1.DeviceRequest
	0,  // 22: edge.v1.EdgeDeviceProxy.SignalStrength:input_type -> edge.v1.DeviceRequest
	0,  // 23: edge.v1.EdgeDeviceProxy.SimStatus:input_type -> edge.v1.DeviceRequest
	0,  // 24: edge.v1.EdgeDeviceProxy.ModemInfo:input_type -> edge.v1.DeviceRequest
	0,  // 25: edge.v1.EdgeDeviceProxy.SensorData:input_type -> edge.v1.DeviceRequest
	0,  // 26: edge.v1.EdgeDeviceProxy.DeviceOnline:input_type -> edge.v1.DeviceRequest
	0,  // 27: edge.v1.EdgeDeviceProxy.DcAvgPing:input_type -> edge.v1.DeviceRequest
	0,  // 28: edge.v1.EdgeDeviceProxy.RegStatus:input_type -> edge.v1.DeviceRequest
	3,  // 29: edge.v1.EdgeDeviceProxy.PortRegStatus:input_type -> edge.v1.EpiRequest
	3,  // 30: edge.v1.EdgeDeviceProxy.PortPhysicalStatus:input_type -> edge.v1.EpiRequest
	1,  // 31: edge.v1.EdgeDeviceProxy.EnableDisablePort:input_type -> edge.v1.EnableDisablePortRequest
	0,  // 32: edge.v1.EdgeDeviceProxy.LiveEpis:input_type -> edge.v1.DeviceRequest
	0,  // 33: edge.v1.EdgeDeviceProxy.WifiStatus:input_type -> edge.v1.DeviceRequest
	0,  // 34: edge.v1.EdgeDeviceProxy.NetworkInfo:input_type -> edge.v1.DeviceRequest
	0,  // 35: edge.v1.EdgeDeviceProxy.DeviceNightlyUpdateTime:input_type -> edge.v1.DeviceRequest
	0,  // 36: edge.v1.EdgeDeviceProxy.PortForwardList:input_type -> edge.v1.DeviceRequest
	0,  // 37: edge.v1.EdgeDeviceProxy.PriorityInterface:input_type -> edge.v1.DeviceRequest
	0,  // 38: edge.v1.EdgeDeviceProxy.PrimarySim:input_type -> edge.v1.DeviceRequest
	0,  // 39: edge.v1.EdgeDeviceProxy.CurrentApn:input_type -> edge.v1.DeviceRequest
	0,  // 40: edge.v1.EdgeDeviceProxy.EpikUpdateStatus:input_type -> edge.v1.DeviceRequest
	0,  // 41: edge.v1.EdgeDeviceProxy.SystemInfo:input_type -> edge.v1.DeviceRequest
	0,  // 42: edge.v1.EdgeDeviceProxy.DCConnectionStats:input_type -> edge.v1.DeviceRequest
	0,  // 43: edge.v1.EdgeDeviceProxy.DnsCheck:input_type -> edge.v1.DeviceRequest
	0,  // 44: edge.v1.EdgeDeviceProxy.VSwitchTab:input_type -> edge.v1.DeviceRequest
	0,  // 45: edge.v1.EdgeDeviceProxy.LteAnalyzer:input_type -> edge.v1.DeviceRequest
	0,  // 46: edge.v1.EdgeDeviceProxy.SpeedTest:input_type -> edge.v1.DeviceRequest
	0,  // 47: edge.v1.EdgeDeviceProxy.InitLtePerf:input_type -> edge.v1.DeviceRequest
	0,  // 48: edge.v1.EdgeDeviceProxy.FetchLtePerf:input_type -> edge.v1.DeviceRequest
	3,  // 49: edge.v1.EdgeDeviceProxy.PortConfigValues:input_type -> edge.v1.EpiRequest
	0,  // 50: edge.v1.EdgeDeviceProxy.HandleRequest:input_type -> edge.v1.DeviceRequest
	15, // 51: edge.v1.EdgeDeviceProxy.EnqueueRequest:input_type -> edge.v1.AsyncRequest
	17, // 52: edge.v1.EdgeDeviceProxy.GetRequestStatus:input_type -> edge.v1.StatusRequest
	19, // 53: edge.v1.EdgeDeviceProxy.CancelRequest:input_type -> edge.v1.CancelRequestMessage
	22, // 54: edge.v1.EdgeDeviceProxy.DashboardData:input_type -> edge.v1.DashboardRequest
	51, // 55: edge.v1.EdgeDeviceProxy.ExecuteSshCommand:input_type -> edge.v1.SshCommandRequest
	51, // 56: edge.v1.EdgeDeviceProxy.ExecuteSshCommandAsync:input_type -> edge.v1.SshCommandRequest
	28, // 57: edge.v1.EdgeDeviceProxy.PingBox:output_type -> edge.v1.PingMessage
	26, // 58: edge.v1.EdgeDeviceProxy.UnaryEcho:output_type -> edge.v1.EchoResponse
	4,  // 59: edge.v1.EdgeDeviceProxy.PowerSource:output_type -> edge.v1.PowerSourceResponse
	5,  // 60: edge.v1.EdgeDeviceProxy.ActiveInterface:output_type -> edge.v1.ActiveInterfaceResponse
	6,  // 61: edge.v1.EdgeDeviceProxy.LanIp:output_type -> edge.v1.LanIpResponse
	7,  // 62: edge.v1.EdgeDeviceProxy.PublicIp:output_type -> edge.v1.PublicIpResponse
	8,  // 63: edge.v1.EdgeDeviceProxy.SignalStrength:output_type -> edge.v1.SignalStrengthResponse
	9,  // 64: edge.v1.EdgeDeviceProxy.SimStatus:output_type -> edge.v1.SimStatusResponse
	11, // 65: edge.v1.EdgeDeviceProxy.ModemInfo:output_type -> edge.v1.ModemInfoResponse
	12, // 66: edge.v1.EdgeDeviceProxy.SensorData:output_type -> edge.v1.SensorDataResponse
	13, // 67: edge.v1.EdgeDeviceProxy.DeviceOnline:output_type -> edge.v1.DeviceOnlineResponse
	27, // 68: edge.v1.EdgeDeviceProxy.DcAvgPing:output_type -> edge.v1.DcAvgPingResponse
	31, // 69: edge.v1.EdgeDeviceProxy.RegStatus:output_type -> edge.v1.RegStatusResponse
	36, // 70: edge.v1.EdgeDeviceProxy.PortRegStatus:output_type -> edge.v1.PortRegStatusResponse
	38, // 71: edge.v1.EdgeDeviceProxy.PortPhysicalStatus:output_type -> edge.v1.PortStatusResponse
	2,  // 72: edge.v1.EdgeDeviceProxy.EnableDisablePort:output_type -> edge.v1.EnableDisablePortResponse
	39, // 73: edge.v1.EdgeDeviceProxy.LiveEpis:output_type -> edge.v1.LiveEpisResponse
	40, // 74: edge.v1.EdgeDeviceProxy.WifiStatus:output_type -> edge.v1.WifiStatusResponse
	42, // 75: edge.v1.EdgeDeviceProxy.NetworkInfo:output_type -> edge.v1.NetworkInfoResponse
	43, // 76: edge.v1.EdgeDeviceProxy.DeviceNightlyUpdateTime:output_type -> edge.v1.DeviceNightlyUpdateTimeResponse
	45, // 77: edge.v1.EdgeDeviceProxy.PortForwardList:output_type -> edge.v1.PortForwardListResponse
	46, // 78: edge.v1.EdgeDeviceProxy.PriorityInterface:output_type -> edge.v1.PriorityInterfaceResponse
	47, // 79: edge.v1.EdgeDeviceProxy.PrimarySim:output_type -> edge.v1.PrimarySimResponse
	48, // 80: edge.v1.EdgeDeviceProxy.CurrentApn:output_type -> edge.v1.CurrentApnResponse
	49, // 81: edge.v1.EdgeDeviceProxy.EpikUpdateStatus:output_type -> edge.v1.EpikUpdateStatusResponse
	50, // 82: edge.v1.EdgeDeviceProxy.SystemInfo:output_type -> edge.v1.SystemInfoResponse
	53, // 83: edge.v1.EdgeDeviceProxy.DCConnectionStats:output_type -> edge.v1.DCConnectionStatsResponse
	54, // 84: edge.v1.EdgeDeviceProxy.DnsCheck:output_type -> edge.v1.DnsCheckResponse
	56, // 85: edge.v1.EdgeDeviceProxy.VSwitchTab:output_type -> edge.v1.VSwitchTabResponse
	57, // 86: edge.v1.EdgeDeviceProxy.LteAnalyzer:output_type -> edge.v1.LteAnalyzerResponse
	58, // 87: edge.v1.EdgeDeviceProxy.SpeedTest:output_type -> edge.v1.SpeedTestResponse
	59, // 88: edge.v1.EdgeDeviceProxy.InitLtePerf:output_type -> edge.v1.InitLtePerfResponse
	61, // 89: edge.v1.EdgeDeviceProxy.FetchLtePerf:output_type -> edge.v1.FetchLtePerfResponse
	62, // 90: edge.v1.EdgeDeviceProxy.PortConfigValues:output_type -> edge.v1.PortConfigValuesResponse
	14, // 91: edge.v1.EdgeDeviceProxy.HandleRequest:output_type -> edge.v1.DeviceResponse
	16, // 92: edge.v1.EdgeDeviceProxy.EnqueueRequest:output_type -> edge.v1.AsyncResponse
	18, // 93: edge.v1.EdgeDeviceProxy.GetRequestStatus:output_type -> edge.v1.StatusResponse
	20, // 94: edge.v1.EdgeDeviceProxy.CancelRequest:output_type -> edge.v1.CancelResponse
	24, // 95: edge.v1.EdgeDeviceProxy.DashboardData:output_type -> edge.v1.DashboardResponse
	52, // 96: edge.v1.EdgeDeviceProxy.ExecuteSshCommand:output_type -> edge.v1.SshCommandResponse
	52, // 97: edge.v1.EdgeDeviceProxy.ExecuteSshCommandAsync:output_type -> edge.v1.SshCommandResponse
	57, // [57:98] is the sub-list for method output_type
	16, // [16:57] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proto_edge_v1_notifications_proto_init() }
func file_proto_edge_v1_notifications_proto_init() {
	if File_proto_edge_v1_notifications_proto != nil {
		return
	}
	file_proto_edge_v1_notifications_proto_msgTypes[28].OneofWrappers = []any{
		(*PingMessage_Command)(nil),
		(*PingMessage_Line)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_edge_v1_notifications_proto_rawDesc), len(file_proto_edge_v1_notifications_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   66,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_edge_v1_notifications_proto_goTypes,
		DependencyIndexes: file_proto_edge_v1_notifications_proto_depIdxs,
		MessageInfos:      file_proto_edge_v1_notifications_proto_msgTypes,
	}.Build()
	File_proto_edge_v1_notifications_proto = out.File
	file_proto_edge_v1_notifications_proto_goTypes = nil
	file_proto_edge_v1_notifications_proto_depIdxs = nil
}

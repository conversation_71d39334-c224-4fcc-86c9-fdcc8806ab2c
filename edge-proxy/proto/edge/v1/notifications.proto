syntax = "proto3";

package edge.v1;

option go_package = "github.com/EPIKio/myepikV2/edge/proto/edge/v1;edgev1";


//----------Request Constructs-----------
// DeviceRequest represents a request to proxy to an edge device.
message DeviceRequest {
  string serial_number = 1;
  string payload = 2;

}

message EnableDisablePortRequest {
  string serial_number = 1;
  int32 port = 2;
  bool enable = 3;
}

message EnableDisablePortResponse {
  bool success = 1;
}

// EpiRequest represents a request to proxy to an Epi device.
message EpiRequest {
  string mac_address = 1;
  string serial_number = 2;
  string port = 3;
}

//----------Response Constructs-----------

// Response from the edge device
message PowerSourceResponse {
  string power_source = 1;
}

message ActiveInterfaceResponse {
  string active_interface = 1;
}

message LanIpResponse {
  string lan_ip = 1;
}

message PublicIpResponse {
  string public_ip = 1;
}

message SignalStrengthResponse {
  string signal_strength = 1;
}

message SimStatusResponse {
  string sim_status = 1;
}

message ModemInfo {
  string manufacturer_placeholder = 1;
  string manufacturer = 2;
  string model_placeholder = 3;
  string model = 4;
  string sim_placeholder = 5;
  string sim = 6; 
  string imei_placeholder = 7;
  string imei = 8;
  string carrier_placeholder = 9;
  string carrier = 10;
  string ipAddress_placeholder = 11;
  string ipAddress = 12; 
}

message ModemInfoResponse {
    ModemInfo modem_info = 1;
}

message SensorDataResponse {
  string power = 1;
  string temp = 2;
}

message DeviceOnlineResponse {
  bool online = 1;
}

message DeviceResponse {
  bytes body = 1;
}

// AsyncRequest represents a request for async processing
message AsyncRequest {
  // Serial number of the device
  string serial_number = 1;

  // Path to forward to the device
  string path = 2;

  // Request priority (0=low, 1=normal, 2=high, 3=critical)
  int32 priority = 3;
}

// AsyncResponse contains the request ID for tracking
message AsyncResponse {
  // Unique request ID for tracking
  string request_id = 1;

  // Current status of the request
  string status = 2;

  // Estimated processing time (if available)
  int64 estimated_completion_seconds = 3;
}

// StatusRequest for checking async request status
message StatusRequest {
  // Request ID to check
  string request_id = 1;
}

// StatusResponse contains the current status and result (if completed)
message StatusResponse {
  // Request ID
  string request_id = 1;

  // Current status (pending, processing, completed, failed, cancelled)
  string status = 2;

  // HTTP status code (if completed)
  int32 status_code = 3;

  // Response body (if completed)
  bytes body = 4;

  // Error message (if failed)
  string error = 6;

  // Timestamps
  int64 created_at = 7;
  int64 completed_at = 8;
}

// CancelRequestMessage for cancelling pending async requests
message CancelRequestMessage {
  // Request ID to cancel
  string request_id = 1;
}

// CancelResponse confirms cancellation
message CancelResponse {
  // Request ID that was cancelled
  string request_id = 1;

  // Whether cancellation was successful
  bool cancelled = 2;

  // Message about cancellation status
  string message = 3;
}

// DashboardEndpoint represents a single endpoint to call for dashboard data
message DashboardEndpoint {
  // Path to call on the device (e.g., "/dcavgping", "/status", "/health")
  string path = 1;

  // Optional: Custom identifier for this endpoint (defaults to path)
  string identifier = 2;

  // Optional: Timeout for this specific endpoint in seconds (defaults to 5)
  int32 timeout_seconds = 3;
}

// DashboardRequest represents a request to fetch data from multiple endpoints
message DashboardRequest {
  string serial_number = 1;
}

// DashboardEndpointResult represents the result from a single endpoint
message DashboardEndpointResult {
  // Identifier of the endpoint (path or custom identifier)
  string identifier = 1;

  // Path that was called
  string path = 2;

  // HTTP status code from the device
  int32 status_code = 3;

  // Raw response body from the device
  bytes body = 4;

  // Error message if the request failed
  string error = 6;

  // Duration of the request in milliseconds
  int64 duration_ms = 7;

  // Whether this endpoint completed successfully
  bool success = 8;
}

// DashboardResponse contains results from all requested endpoints
message DashboardResponse {
  // Serial number of the device
  string serial_number = 1;

  // Results from each endpoint (in order of completion)
  repeated DashboardEndpointResult results = 2;

  // Total number of endpoints requested
  int32 total_endpoints = 3;

  // Number of endpoints that completed successfully
  int32 successful_endpoints = 4;

  // Number of endpoints that failed
  int32 failed_endpoints = 5;

  // Total duration for all requests in milliseconds
  int64 total_duration_ms = 6;

  // Overall success status (true if at least one endpoint succeeded)
  bool overall_success = 7;
}

message EchoRequest {
  string message = 1;
}

message EchoResponse {
  string message = 1;
}

message DcAvgPingResponse {
  string atPingAvg = 1;
  string bestDC = 2;
  string bestLatency = 3;
  string chPingAvg = 4;
  string dlPingAvg = 5;
  string laPingAvg = 6;
  string nyPingAvg = 7;
  string timeUpdate = 8;
  string error = 9;
}


message PingMessage {
  oneof payload {
    PingCommand command = 1;
    PingLine line = 2;
  }
}

message PingCommand {
  string serial_number = 1;    // required for start
  bool stop = 2;      // if true, stop the ping
  string ip = 3;    // Optional
}

message PingLine {
  string line = 1;    // line of output from ping
  bool completed = 2; // indicates process finished
}
message RegStatusResponse {
  bool registered = 1;
}

message WanInfo {
  string place_holder = 1;
  string ip = 2;
  string subnet = 3;
  string gateway = 4;
  string dns = 5;
}

message Sp1ServiceStatus {
  string status = 1;
  string call_state = 2;
}

message Sp2ServiceStatus {
  string status = 1;
  string call_state = 2;
}

message ObiTalkServiceStatus {
  string place_holder = 1;
  string status = 2;
}

message PortRegStatusResponse {
  WanInfo wanInfo = 1;
  Sp1ServiceStatus sp1ServiceStatus = 2;
  Sp2ServiceStatus sp2ServiceStatus = 3;
  ObiTalkServiceStatus obiTalkServiceStatus = 4;
}

message PortStatus {
  string name = 1;
  string state = 2;
  string loopCurrent = 3;
  string vbat = 4;
  string tipRingVoltage = 5;
  string lastCallerInfo = 6;

}
message PortStatusResponse {
  repeated PortStatus portStatus = 1;
}
message LiveEpisResponse {
  map<string, string> epis = 1; // lowercase 'epis' is safer
}

message WifiStatusResponse {
  string error_msg = 1;
  string gateway = 2;
  string ip = 3;
  string mode = 4;
  string password = 5;
  string SSID = 6;
  string sec_mode = 7;
  string status = 8;
  string subnet = 9;
}


message NetworkInterfaceObj{
   string interface = 1;
      string internet = 2;
      string icmp = 3;
      string wg = 4;
}
message NetworkInfoResponse{
  string dns = 1;
  string error = 2;
  repeated NetworkInterfaceObj interfaces = 3;
  string timestamp = 4;
}

message DeviceNightlyUpdateTimeResponse{
  string device_update_time = 1;
}

message PortForwardObj{
    string srcIP = 1 ;
    int32 srcStartPort = 2 ;
    int32 srcEndPort = 3 ;
    string dstIP = 4 ;
    int32 dstStartPort = 5 ;
    int32 dstEndPort = 6 ;
    string proto = 7 ;
}

message PortForwardListResponse{
  repeated PortForwardObj portForwardList = 1 ;
}

message PriorityInterfaceResponse{
  string priorityInterface = 1 ;
}

message PrimarySimResponse{
  string primarySim = 1 ;
}
message CurrentApnResponse{
  string apn = 1 ;
}

message EpikUpdateStatusResponse{
  bool status = 1 ;
}

// Returns an object with arbitrary keys of unknown type and string values
message SystemInfoResponse {
  // The map allows for any string key with a string value
  map<string, string> sysInfo = 1;
}
message SshCommandRequest {
  string serial_number = 1;
  string command = 2;
}

message SshCommandResponse {
  string output = 1;
  string error_message = 2;
  int32 exit_code = 3;
  int64 duration_ms = 4;
  bool success = 5;
}

// Returns an object with arbitrary keys of unknown type and string values
message DCConnectionStatsResponse {
  // The map allows for any string key with a string value
  map<string, string> dc_connection_stats = 1;
}

message DnsCheckResponse {
  bool dns = 1;
}


// Info for a single port
message PortInfo {
  string port = 1;
  string calledId = 2;
  string recording = 3;
  string trunkType = 4;
}


// Response for vSwitch tab
message VSwitchTabResponse {
  bool registered = 1;
  bool registerationConfigCreated = 2;
  repeated PortInfo portsInfo = 3;
  repeated string portsConfigCreated = 4;
}

message LteAnalyzerResponse {
  bool Busy = 1;
}
message SpeedTestResponse {
  string downloadSpeed = 1;
  string uploadSpeed = 2;
  string latency = 3;
  string jitter = 4;
}


message InitLtePerfResponse {
  string initiated = 1;
}

message SimPingInfo {
  string Error = 1;
  double Jitter = 2;
  double PacketLoss = 3;
  double PingAvg = 4;
  int32 SIM = 5;
}

message FetchLtePerfResponse {
  repeated SimPingInfo SimsInfo = 1;
  string TimeStamp = 2;
}





message PortConfigValuesResponse {
    string OnhookVolts = 1;
    string OffhookCurrent = 2;
    string DtmfDetectLength =  3;
    string DtmfDetectGap = 4;
    string TxGain = 5;
    string RxGain = 6;
    string DtmfMethod = 7;
    string DtmfPlaybackLevel = 8;
    string RingVoltage = 9;
    string DigitMapShortTimer = 10;
    string CpcDuration = 11;
    string CpcDelayTime = 12;
    string JitterBufferType = 13;
    string JitterBufferMinDeley = 14;
    string JitterBufferMaxDeley = 15;
    string T38Enabled = 16;
    string ModemMode = 17;
    string VadEnable = 18;
    string ThreeWayCalling = 19;
    string SilenceDetectSensitivity = 20;
}

//------------------------Service--------------------

service EdgeDeviceProxy {

  rpc PingBox(stream PingMessage) returns (stream PingMessage);

  rpc UnaryEcho(EchoRequest) returns (EchoResponse) {}
  // Battery/AC/ N/A
  rpc PowerSource(DeviceRequest) returns (PowerSourceResponse) {}
  //eth0 wwan0 wwan1 wlan0
  rpc ActiveInterface(DeviceRequest) returns (ActiveInterfaceResponse) {}
  //net/eth0 lan ip
  rpc LanIp(DeviceRequest) returns (LanIpResponse) {}
  //publicip
  rpc PublicIp(DeviceRequest) returns (PublicIpResponse) {}
  //ltesignalstrength
  rpc SignalStrength(DeviceRequest) returns (SignalStrengthResponse) {}
  //simStatus
  rpc SimStatus(DeviceRequest) returns (SimStatusResponse) {}
  //modeminfo
  rpc ModemInfo(DeviceRequest) returns (ModemInfoResponse) {}
  //sensorsdata
  rpc SensorData(DeviceRequest) returns (SensorDataResponse) {}
  //device online
  rpc DeviceOnline(DeviceRequest) returns (DeviceOnlineResponse) {}
  // DcAvgPing calls the DC avg ping endpoint on the device
  rpc DcAvgPing(DeviceRequest) returns (DcAvgPingResponse) {}
  // RegStatus calls the registered endpoint on the device
  rpc RegStatus(DeviceRequest) returns (RegStatusResponse) {}
  // PortRegStatus calls the epi registration endpoint on the device
  rpc PortRegStatus(EpiRequest) returns (PortRegStatusResponse) {}
  // PortStatus calls the epi physicalStatus endpoint 
  rpc PortPhysicalStatus(EpiRequest) returns (PortStatusResponse) {}
  // EnableDisablePort calls the epi enable/disable endpoint
  rpc EnableDisablePort(EnableDisablePortRequest) returns (EnableDisablePortResponse) {}
   //LiveEpis
  rpc LiveEpis(DeviceRequest) returns (LiveEpisResponse) {}
   //WifiStatus
  rpc WifiStatus(DeviceRequest) returns (WifiStatusResponse) {}
   //NetworkInfo
  rpc NetworkInfo(DeviceRequest) returns (NetworkInfoResponse) {}
    //DeviceNightlyUpdateTime
  rpc DeviceNightlyUpdateTime(DeviceRequest) returns (DeviceNightlyUpdateTimeResponse) {}
   //PortForwardList
  rpc PortForwardList(DeviceRequest) returns (PortForwardListResponse) {}
   //PriorityInterface
  rpc PriorityInterface(DeviceRequest) returns (PriorityInterfaceResponse) {}
   //PrimarySim
  rpc PrimarySim(DeviceRequest) returns (PrimarySimResponse) {}
   //CurrentApn
  rpc CurrentApn(DeviceRequest) returns (CurrentApnResponse) {}
    //EpikUpdateStatus
  rpc EpikUpdateStatus(DeviceRequest) returns (EpikUpdateStatusResponse) {}
    //SystemInfo
  rpc SystemInfo(DeviceRequest) returns (SystemInfoResponse) {}
    //DCConnectionStats
  rpc DCConnectionStats(DeviceRequest) returns (DCConnectionStatsResponse) {}
    //DnsCheck
  rpc DnsCheck(DeviceRequest) returns (DnsCheckResponse) {}
    //VSwitchTab
  rpc VSwitchTab(DeviceRequest) returns (VSwitchTabResponse) {}
    //LteAnalyzer
  rpc LteAnalyzer(DeviceRequest) returns (LteAnalyzerResponse) {}
    //SpeedTest
  rpc SpeedTest(DeviceRequest) returns (SpeedTestResponse) {}
    //InitLtePerf
  rpc InitLtePerf(DeviceRequest) returns (InitLtePerfResponse) {}
    //FetchLtePerf
  rpc FetchLtePerf(DeviceRequest) returns (FetchLtePerfResponse) {}
  // PortConfigValues
  rpc PortConfigValues(EpiRequest) returns (PortConfigValuesResponse) {}
  // HandleRequest proxies a request to an edge device
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse) {}

  // EnqueueRequest queues a request for async processing
  rpc EnqueueRequest(AsyncRequest) returns (AsyncResponse) {}

  // GetRequestStatus checks the status of an async request
  rpc GetRequestStatus(StatusRequest) returns (StatusResponse) {}

  // CancelRequest cancels a pending async request
  rpc CancelRequest(CancelRequestMessage) returns (CancelResponse) {}

  // DashboardData calls multiple endpoints concurrently and returns aggregated results
  rpc DashboardData(DashboardRequest) returns (DashboardResponse) {}

  // ExecuteSshCommand executes SSH command synchronously
  rpc ExecuteSshCommand(SshCommandRequest) returns (SshCommandResponse);

  // ExecuteSshCommandAsync executes SSH command with streaming response
  rpc ExecuteSshCommandAsync(SshCommandRequest) returns (stream SshCommandResponse);
}

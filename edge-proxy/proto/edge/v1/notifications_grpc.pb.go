// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: proto/edge/v1/notifications.proto

package edgev1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EdgeDeviceProxy_PingBox_FullMethodName                 = "/edge.v1.EdgeDeviceProxy/PingBox"
	EdgeDeviceProxy_UnaryEcho_FullMethodName               = "/edge.v1.EdgeDeviceProxy/UnaryEcho"
	EdgeDeviceProxy_PowerSource_FullMethodName             = "/edge.v1.EdgeDeviceProxy/PowerSource"
	EdgeDeviceProxy_ActiveInterface_FullMethodName         = "/edge.v1.EdgeDeviceProxy/ActiveInterface"
	EdgeDeviceProxy_LanIp_FullMethodName                   = "/edge.v1.EdgeDeviceProxy/LanIp"
	EdgeDeviceProxy_PublicIp_FullMethodName                = "/edge.v1.EdgeDeviceProxy/PublicIp"
	EdgeDeviceProxy_SignalStrength_FullMethodName          = "/edge.v1.EdgeDeviceProxy/SignalStrength"
	EdgeDeviceProxy_SimStatus_FullMethodName               = "/edge.v1.EdgeDeviceProxy/SimStatus"
	EdgeDeviceProxy_ModemInfo_FullMethodName               = "/edge.v1.EdgeDeviceProxy/ModemInfo"
	EdgeDeviceProxy_SensorData_FullMethodName              = "/edge.v1.EdgeDeviceProxy/SensorData"
	EdgeDeviceProxy_DeviceOnline_FullMethodName            = "/edge.v1.EdgeDeviceProxy/DeviceOnline"
	EdgeDeviceProxy_DcAvgPing_FullMethodName               = "/edge.v1.EdgeDeviceProxy/DcAvgPing"
	EdgeDeviceProxy_RegStatus_FullMethodName               = "/edge.v1.EdgeDeviceProxy/RegStatus"
	EdgeDeviceProxy_PortRegStatus_FullMethodName           = "/edge.v1.EdgeDeviceProxy/PortRegStatus"
	EdgeDeviceProxy_PortPhysicalStatus_FullMethodName      = "/edge.v1.EdgeDeviceProxy/PortPhysicalStatus"
	EdgeDeviceProxy_EnableDisablePort_FullMethodName       = "/edge.v1.EdgeDeviceProxy/EnableDisablePort"
	EdgeDeviceProxy_LiveEpis_FullMethodName                = "/edge.v1.EdgeDeviceProxy/LiveEpis"
	EdgeDeviceProxy_WifiStatus_FullMethodName              = "/edge.v1.EdgeDeviceProxy/WifiStatus"
	EdgeDeviceProxy_NetworkInfo_FullMethodName             = "/edge.v1.EdgeDeviceProxy/NetworkInfo"
	EdgeDeviceProxy_DeviceNightlyUpdateTime_FullMethodName = "/edge.v1.EdgeDeviceProxy/DeviceNightlyUpdateTime"
	EdgeDeviceProxy_PortForwardList_FullMethodName         = "/edge.v1.EdgeDeviceProxy/PortForwardList"
	EdgeDeviceProxy_PriorityInterface_FullMethodName       = "/edge.v1.EdgeDeviceProxy/PriorityInterface"
	EdgeDeviceProxy_PrimarySim_FullMethodName              = "/edge.v1.EdgeDeviceProxy/PrimarySim"
	EdgeDeviceProxy_CurrentApn_FullMethodName              = "/edge.v1.EdgeDeviceProxy/CurrentApn"
	EdgeDeviceProxy_EpikUpdateStatus_FullMethodName        = "/edge.v1.EdgeDeviceProxy/EpikUpdateStatus"
	EdgeDeviceProxy_SystemInfo_FullMethodName              = "/edge.v1.EdgeDeviceProxy/SystemInfo"
	EdgeDeviceProxy_DCConnectionStats_FullMethodName       = "/edge.v1.EdgeDeviceProxy/DCConnectionStats"
	EdgeDeviceProxy_DnsCheck_FullMethodName                = "/edge.v1.EdgeDeviceProxy/DnsCheck"
	EdgeDeviceProxy_VSwitchTab_FullMethodName              = "/edge.v1.EdgeDeviceProxy/VSwitchTab"
	EdgeDeviceProxy_LteAnalyzer_FullMethodName             = "/edge.v1.EdgeDeviceProxy/LteAnalyzer"
	EdgeDeviceProxy_SpeedTest_FullMethodName               = "/edge.v1.EdgeDeviceProxy/SpeedTest"
	EdgeDeviceProxy_InitLtePerf_FullMethodName             = "/edge.v1.EdgeDeviceProxy/InitLtePerf"
	EdgeDeviceProxy_FetchLtePerf_FullMethodName            = "/edge.v1.EdgeDeviceProxy/FetchLtePerf"
	EdgeDeviceProxy_PortConfigValues_FullMethodName        = "/edge.v1.EdgeDeviceProxy/PortConfigValues"
	EdgeDeviceProxy_HandleRequest_FullMethodName           = "/edge.v1.EdgeDeviceProxy/HandleRequest"
	EdgeDeviceProxy_EnqueueRequest_FullMethodName          = "/edge.v1.EdgeDeviceProxy/EnqueueRequest"
	EdgeDeviceProxy_GetRequestStatus_FullMethodName        = "/edge.v1.EdgeDeviceProxy/GetRequestStatus"
	EdgeDeviceProxy_CancelRequest_FullMethodName           = "/edge.v1.EdgeDeviceProxy/CancelRequest"
	EdgeDeviceProxy_DashboardData_FullMethodName           = "/edge.v1.EdgeDeviceProxy/DashboardData"
	EdgeDeviceProxy_ExecuteSshCommand_FullMethodName       = "/edge.v1.EdgeDeviceProxy/ExecuteSshCommand"
	EdgeDeviceProxy_ExecuteSshCommandAsync_FullMethodName  = "/edge.v1.EdgeDeviceProxy/ExecuteSshCommandAsync"
)

// EdgeDeviceProxyClient is the client API for EdgeDeviceProxy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EdgeDeviceProxyClient interface {
	PingBox(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[PingMessage, PingMessage], error)
	UnaryEcho(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error)
	// Battery/AC/ N/A
	PowerSource(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PowerSourceResponse, error)
	// eth0 wwan0 wwan1 wlan0
	ActiveInterface(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*ActiveInterfaceResponse, error)
	// net/eth0 lan ip
	LanIp(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LanIpResponse, error)
	// publicip
	PublicIp(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PublicIpResponse, error)
	// ltesignalstrength
	SignalStrength(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SignalStrengthResponse, error)
	// simStatus
	SimStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SimStatusResponse, error)
	// modeminfo
	ModemInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*ModemInfoResponse, error)
	// sensorsdata
	SensorData(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SensorDataResponse, error)
	// device online
	DeviceOnline(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceOnlineResponse, error)
	// DcAvgPing calls the DC avg ping endpoint on the device
	DcAvgPing(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DcAvgPingResponse, error)
	// RegStatus calls the registered endpoint on the device
	RegStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*RegStatusResponse, error)
	// PortRegStatus calls the epi registration endpoint on the device
	PortRegStatus(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortRegStatusResponse, error)
	// PortStatus calls the epi physicalStatus endpoint
	PortPhysicalStatus(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortStatusResponse, error)
	// EnableDisablePort calls the epi enable/disable endpoint
	EnableDisablePort(ctx context.Context, in *EnableDisablePortRequest, opts ...grpc.CallOption) (*EnableDisablePortResponse, error)
	// LiveEpis
	LiveEpis(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LiveEpisResponse, error)
	// WifiStatus
	WifiStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*WifiStatusResponse, error)
	// NetworkInfo
	NetworkInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*NetworkInfoResponse, error)
	// DeviceNightlyUpdateTime
	DeviceNightlyUpdateTime(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceNightlyUpdateTimeResponse, error)
	// PortForwardList
	PortForwardList(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PortForwardListResponse, error)
	// PriorityInterface
	PriorityInterface(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PriorityInterfaceResponse, error)
	// PrimarySim
	PrimarySim(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PrimarySimResponse, error)
	// CurrentApn
	CurrentApn(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*CurrentApnResponse, error)
	// EpikUpdateStatus
	EpikUpdateStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*EpikUpdateStatusResponse, error)
	// SystemInfo
	SystemInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SystemInfoResponse, error)
	// DCConnectionStats
	DCConnectionStats(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DCConnectionStatsResponse, error)
	// DnsCheck
	DnsCheck(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DnsCheckResponse, error)
	// VSwitchTab
	VSwitchTab(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*VSwitchTabResponse, error)
	// LteAnalyzer
	LteAnalyzer(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LteAnalyzerResponse, error)
	// SpeedTest
	SpeedTest(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SpeedTestResponse, error)
	// InitLtePerf
	InitLtePerf(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*InitLtePerfResponse, error)
	// FetchLtePerf
	FetchLtePerf(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*FetchLtePerfResponse, error)
	// PortConfigValues
	PortConfigValues(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortConfigValuesResponse, error)
	// HandleRequest proxies a request to an edge device
	HandleRequest(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceResponse, error)
	// EnqueueRequest queues a request for async processing
	EnqueueRequest(ctx context.Context, in *AsyncRequest, opts ...grpc.CallOption) (*AsyncResponse, error)
	// GetRequestStatus checks the status of an async request
	GetRequestStatus(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*StatusResponse, error)
	// CancelRequest cancels a pending async request
	CancelRequest(ctx context.Context, in *CancelRequestMessage, opts ...grpc.CallOption) (*CancelResponse, error)
	// DashboardData calls multiple endpoints concurrently and returns aggregated results
	DashboardData(ctx context.Context, in *DashboardRequest, opts ...grpc.CallOption) (*DashboardResponse, error)
	// ExecuteSshCommand executes SSH command synchronously
	ExecuteSshCommand(ctx context.Context, in *SshCommandRequest, opts ...grpc.CallOption) (*SshCommandResponse, error)
	// ExecuteSshCommandAsync executes SSH command with streaming response
	ExecuteSshCommandAsync(ctx context.Context, in *SshCommandRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[SshCommandResponse], error)
}

type edgeDeviceProxyClient struct {
	cc grpc.ClientConnInterface
}

func NewEdgeDeviceProxyClient(cc grpc.ClientConnInterface) EdgeDeviceProxyClient {
	return &edgeDeviceProxyClient{cc}
}

func (c *edgeDeviceProxyClient) PingBox(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[PingMessage, PingMessage], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EdgeDeviceProxy_ServiceDesc.Streams[0], EdgeDeviceProxy_PingBox_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[PingMessage, PingMessage]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EdgeDeviceProxy_PingBoxClient = grpc.BidiStreamingClient[PingMessage, PingMessage]

func (c *edgeDeviceProxyClient) UnaryEcho(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EchoResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_UnaryEcho_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PowerSource(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PowerSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PowerSourceResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PowerSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) ActiveInterface(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*ActiveInterfaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActiveInterfaceResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_ActiveInterface_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) LanIp(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LanIpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LanIpResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_LanIp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PublicIp(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PublicIpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PublicIpResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PublicIp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) SignalStrength(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SignalStrengthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignalStrengthResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_SignalStrength_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) SimStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SimStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_SimStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) ModemInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*ModemInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModemInfoResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_ModemInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) SensorData(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SensorDataResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SensorDataResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_SensorData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DeviceOnline(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceOnlineResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceOnlineResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DeviceOnline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DcAvgPing(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DcAvgPingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DcAvgPingResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DcAvgPing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) RegStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*RegStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_RegStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PortRegStatus(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortRegStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PortRegStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PortRegStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PortPhysicalStatus(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PortStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PortPhysicalStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) EnableDisablePort(ctx context.Context, in *EnableDisablePortRequest, opts ...grpc.CallOption) (*EnableDisablePortResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EnableDisablePortResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_EnableDisablePort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) LiveEpis(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LiveEpisResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LiveEpisResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_LiveEpis_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) WifiStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*WifiStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WifiStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_WifiStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) NetworkInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*NetworkInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NetworkInfoResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_NetworkInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DeviceNightlyUpdateTime(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceNightlyUpdateTimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceNightlyUpdateTimeResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DeviceNightlyUpdateTime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PortForwardList(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PortForwardListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PortForwardListResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PortForwardList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PriorityInterface(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PriorityInterfaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PriorityInterfaceResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PriorityInterface_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PrimarySim(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*PrimarySimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrimarySimResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PrimarySim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) CurrentApn(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*CurrentApnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CurrentApnResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_CurrentApn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) EpikUpdateStatus(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*EpikUpdateStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EpikUpdateStatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_EpikUpdateStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) SystemInfo(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SystemInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SystemInfoResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_SystemInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DCConnectionStats(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DCConnectionStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DCConnectionStatsResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DCConnectionStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DnsCheck(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DnsCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DnsCheckResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DnsCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) VSwitchTab(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*VSwitchTabResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VSwitchTabResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_VSwitchTab_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) LteAnalyzer(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*LteAnalyzerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LteAnalyzerResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_LteAnalyzer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) SpeedTest(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*SpeedTestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SpeedTestResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_SpeedTest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) InitLtePerf(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*InitLtePerfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InitLtePerfResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_InitLtePerf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) FetchLtePerf(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*FetchLtePerfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchLtePerfResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_FetchLtePerf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) PortConfigValues(ctx context.Context, in *EpiRequest, opts ...grpc.CallOption) (*PortConfigValuesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PortConfigValuesResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_PortConfigValues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) HandleRequest(ctx context.Context, in *DeviceRequest, opts ...grpc.CallOption) (*DeviceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeviceResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_HandleRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) EnqueueRequest(ctx context.Context, in *AsyncRequest, opts ...grpc.CallOption) (*AsyncResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AsyncResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_EnqueueRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) GetRequestStatus(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*StatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StatusResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_GetRequestStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) CancelRequest(ctx context.Context, in *CancelRequestMessage, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_CancelRequest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) DashboardData(ctx context.Context, in *DashboardRequest, opts ...grpc.CallOption) (*DashboardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DashboardResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_DashboardData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) ExecuteSshCommand(ctx context.Context, in *SshCommandRequest, opts ...grpc.CallOption) (*SshCommandResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SshCommandResponse)
	err := c.cc.Invoke(ctx, EdgeDeviceProxy_ExecuteSshCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *edgeDeviceProxyClient) ExecuteSshCommandAsync(ctx context.Context, in *SshCommandRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[SshCommandResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &EdgeDeviceProxy_ServiceDesc.Streams[1], EdgeDeviceProxy_ExecuteSshCommandAsync_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[SshCommandRequest, SshCommandResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EdgeDeviceProxy_ExecuteSshCommandAsyncClient = grpc.ServerStreamingClient[SshCommandResponse]

// EdgeDeviceProxyServer is the server API for EdgeDeviceProxy service.
// All implementations must embed UnimplementedEdgeDeviceProxyServer
// for forward compatibility.
type EdgeDeviceProxyServer interface {
	PingBox(grpc.BidiStreamingServer[PingMessage, PingMessage]) error
	UnaryEcho(context.Context, *EchoRequest) (*EchoResponse, error)
	// Battery/AC/ N/A
	PowerSource(context.Context, *DeviceRequest) (*PowerSourceResponse, error)
	// eth0 wwan0 wwan1 wlan0
	ActiveInterface(context.Context, *DeviceRequest) (*ActiveInterfaceResponse, error)
	// net/eth0 lan ip
	LanIp(context.Context, *DeviceRequest) (*LanIpResponse, error)
	// publicip
	PublicIp(context.Context, *DeviceRequest) (*PublicIpResponse, error)
	// ltesignalstrength
	SignalStrength(context.Context, *DeviceRequest) (*SignalStrengthResponse, error)
	// simStatus
	SimStatus(context.Context, *DeviceRequest) (*SimStatusResponse, error)
	// modeminfo
	ModemInfo(context.Context, *DeviceRequest) (*ModemInfoResponse, error)
	// sensorsdata
	SensorData(context.Context, *DeviceRequest) (*SensorDataResponse, error)
	// device online
	DeviceOnline(context.Context, *DeviceRequest) (*DeviceOnlineResponse, error)
	// DcAvgPing calls the DC avg ping endpoint on the device
	DcAvgPing(context.Context, *DeviceRequest) (*DcAvgPingResponse, error)
	// RegStatus calls the registered endpoint on the device
	RegStatus(context.Context, *DeviceRequest) (*RegStatusResponse, error)
	// PortRegStatus calls the epi registration endpoint on the device
	PortRegStatus(context.Context, *EpiRequest) (*PortRegStatusResponse, error)
	// PortStatus calls the epi physicalStatus endpoint
	PortPhysicalStatus(context.Context, *EpiRequest) (*PortStatusResponse, error)
	// EnableDisablePort calls the epi enable/disable endpoint
	EnableDisablePort(context.Context, *EnableDisablePortRequest) (*EnableDisablePortResponse, error)
	// LiveEpis
	LiveEpis(context.Context, *DeviceRequest) (*LiveEpisResponse, error)
	// WifiStatus
	WifiStatus(context.Context, *DeviceRequest) (*WifiStatusResponse, error)
	// NetworkInfo
	NetworkInfo(context.Context, *DeviceRequest) (*NetworkInfoResponse, error)
	// DeviceNightlyUpdateTime
	DeviceNightlyUpdateTime(context.Context, *DeviceRequest) (*DeviceNightlyUpdateTimeResponse, error)
	// PortForwardList
	PortForwardList(context.Context, *DeviceRequest) (*PortForwardListResponse, error)
	// PriorityInterface
	PriorityInterface(context.Context, *DeviceRequest) (*PriorityInterfaceResponse, error)
	// PrimarySim
	PrimarySim(context.Context, *DeviceRequest) (*PrimarySimResponse, error)
	// CurrentApn
	CurrentApn(context.Context, *DeviceRequest) (*CurrentApnResponse, error)
	// EpikUpdateStatus
	EpikUpdateStatus(context.Context, *DeviceRequest) (*EpikUpdateStatusResponse, error)
	// SystemInfo
	SystemInfo(context.Context, *DeviceRequest) (*SystemInfoResponse, error)
	// DCConnectionStats
	DCConnectionStats(context.Context, *DeviceRequest) (*DCConnectionStatsResponse, error)
	// DnsCheck
	DnsCheck(context.Context, *DeviceRequest) (*DnsCheckResponse, error)
	// VSwitchTab
	VSwitchTab(context.Context, *DeviceRequest) (*VSwitchTabResponse, error)
	// LteAnalyzer
	LteAnalyzer(context.Context, *DeviceRequest) (*LteAnalyzerResponse, error)
	// SpeedTest
	SpeedTest(context.Context, *DeviceRequest) (*SpeedTestResponse, error)
	// InitLtePerf
	InitLtePerf(context.Context, *DeviceRequest) (*InitLtePerfResponse, error)
	// FetchLtePerf
	FetchLtePerf(context.Context, *DeviceRequest) (*FetchLtePerfResponse, error)
	// PortConfigValues
	PortConfigValues(context.Context, *EpiRequest) (*PortConfigValuesResponse, error)
	// HandleRequest proxies a request to an edge device
	HandleRequest(context.Context, *DeviceRequest) (*DeviceResponse, error)
	// EnqueueRequest queues a request for async processing
	EnqueueRequest(context.Context, *AsyncRequest) (*AsyncResponse, error)
	// GetRequestStatus checks the status of an async request
	GetRequestStatus(context.Context, *StatusRequest) (*StatusResponse, error)
	// CancelRequest cancels a pending async request
	CancelRequest(context.Context, *CancelRequestMessage) (*CancelResponse, error)
	// DashboardData calls multiple endpoints concurrently and returns aggregated results
	DashboardData(context.Context, *DashboardRequest) (*DashboardResponse, error)
	// ExecuteSshCommand executes SSH command synchronously
	ExecuteSshCommand(context.Context, *SshCommandRequest) (*SshCommandResponse, error)
	// ExecuteSshCommandAsync executes SSH command with streaming response
	ExecuteSshCommandAsync(*SshCommandRequest, grpc.ServerStreamingServer[SshCommandResponse]) error
	mustEmbedUnimplementedEdgeDeviceProxyServer()
}

// UnimplementedEdgeDeviceProxyServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEdgeDeviceProxyServer struct{}

func (UnimplementedEdgeDeviceProxyServer) PingBox(grpc.BidiStreamingServer[PingMessage, PingMessage]) error {
	return status.Errorf(codes.Unimplemented, "method PingBox not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) UnaryEcho(context.Context, *EchoRequest) (*EchoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnaryEcho not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PowerSource(context.Context, *DeviceRequest) (*PowerSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PowerSource not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) ActiveInterface(context.Context, *DeviceRequest) (*ActiveInterfaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActiveInterface not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) LanIp(context.Context, *DeviceRequest) (*LanIpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LanIp not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PublicIp(context.Context, *DeviceRequest) (*PublicIpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublicIp not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) SignalStrength(context.Context, *DeviceRequest) (*SignalStrengthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignalStrength not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) SimStatus(context.Context, *DeviceRequest) (*SimStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SimStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) ModemInfo(context.Context, *DeviceRequest) (*ModemInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModemInfo not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) SensorData(context.Context, *DeviceRequest) (*SensorDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SensorData not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DeviceOnline(context.Context, *DeviceRequest) (*DeviceOnlineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceOnline not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DcAvgPing(context.Context, *DeviceRequest) (*DcAvgPingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DcAvgPing not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) RegStatus(context.Context, *DeviceRequest) (*RegStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PortRegStatus(context.Context, *EpiRequest) (*PortRegStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PortRegStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PortPhysicalStatus(context.Context, *EpiRequest) (*PortStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PortPhysicalStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) EnableDisablePort(context.Context, *EnableDisablePortRequest) (*EnableDisablePortResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnableDisablePort not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) LiveEpis(context.Context, *DeviceRequest) (*LiveEpisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LiveEpis not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) WifiStatus(context.Context, *DeviceRequest) (*WifiStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WifiStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) NetworkInfo(context.Context, *DeviceRequest) (*NetworkInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NetworkInfo not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DeviceNightlyUpdateTime(context.Context, *DeviceRequest) (*DeviceNightlyUpdateTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeviceNightlyUpdateTime not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PortForwardList(context.Context, *DeviceRequest) (*PortForwardListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PortForwardList not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PriorityInterface(context.Context, *DeviceRequest) (*PriorityInterfaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PriorityInterface not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PrimarySim(context.Context, *DeviceRequest) (*PrimarySimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrimarySim not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) CurrentApn(context.Context, *DeviceRequest) (*CurrentApnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CurrentApn not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) EpikUpdateStatus(context.Context, *DeviceRequest) (*EpikUpdateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EpikUpdateStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) SystemInfo(context.Context, *DeviceRequest) (*SystemInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SystemInfo not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DCConnectionStats(context.Context, *DeviceRequest) (*DCConnectionStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DCConnectionStats not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DnsCheck(context.Context, *DeviceRequest) (*DnsCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DnsCheck not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) VSwitchTab(context.Context, *DeviceRequest) (*VSwitchTabResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VSwitchTab not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) LteAnalyzer(context.Context, *DeviceRequest) (*LteAnalyzerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LteAnalyzer not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) SpeedTest(context.Context, *DeviceRequest) (*SpeedTestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SpeedTest not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) InitLtePerf(context.Context, *DeviceRequest) (*InitLtePerfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitLtePerf not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) FetchLtePerf(context.Context, *DeviceRequest) (*FetchLtePerfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchLtePerf not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) PortConfigValues(context.Context, *EpiRequest) (*PortConfigValuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PortConfigValues not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) HandleRequest(context.Context, *DeviceRequest) (*DeviceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleRequest not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) EnqueueRequest(context.Context, *AsyncRequest) (*AsyncResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EnqueueRequest not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) GetRequestStatus(context.Context, *StatusRequest) (*StatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRequestStatus not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) CancelRequest(context.Context, *CancelRequestMessage) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelRequest not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) DashboardData(context.Context, *DashboardRequest) (*DashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DashboardData not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) ExecuteSshCommand(context.Context, *SshCommandRequest) (*SshCommandResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteSshCommand not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) ExecuteSshCommandAsync(*SshCommandRequest, grpc.ServerStreamingServer[SshCommandResponse]) error {
	return status.Errorf(codes.Unimplemented, "method ExecuteSshCommandAsync not implemented")
}
func (UnimplementedEdgeDeviceProxyServer) mustEmbedUnimplementedEdgeDeviceProxyServer() {}
func (UnimplementedEdgeDeviceProxyServer) testEmbeddedByValue()                         {}

// UnsafeEdgeDeviceProxyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EdgeDeviceProxyServer will
// result in compilation errors.
type UnsafeEdgeDeviceProxyServer interface {
	mustEmbedUnimplementedEdgeDeviceProxyServer()
}

func RegisterEdgeDeviceProxyServer(s grpc.ServiceRegistrar, srv EdgeDeviceProxyServer) {
	// If the following call pancis, it indicates UnimplementedEdgeDeviceProxyServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EdgeDeviceProxy_ServiceDesc, srv)
}

func _EdgeDeviceProxy_PingBox_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(EdgeDeviceProxyServer).PingBox(&grpc.GenericServerStream[PingMessage, PingMessage]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EdgeDeviceProxy_PingBoxServer = grpc.BidiStreamingServer[PingMessage, PingMessage]

func _EdgeDeviceProxy_UnaryEcho_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EchoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).UnaryEcho(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_UnaryEcho_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).UnaryEcho(ctx, req.(*EchoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PowerSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PowerSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PowerSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PowerSource(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_ActiveInterface_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).ActiveInterface(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_ActiveInterface_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).ActiveInterface(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_LanIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).LanIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_LanIp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).LanIp(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PublicIp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PublicIp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PublicIp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PublicIp(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_SignalStrength_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).SignalStrength(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_SignalStrength_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).SignalStrength(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_SimStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).SimStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_SimStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).SimStatus(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_ModemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).ModemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_ModemInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).ModemInfo(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_SensorData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).SensorData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_SensorData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).SensorData(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DeviceOnline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DeviceOnline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DeviceOnline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DeviceOnline(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DcAvgPing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DcAvgPing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DcAvgPing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DcAvgPing(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_RegStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).RegStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_RegStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).RegStatus(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PortRegStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EpiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PortRegStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PortRegStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PortRegStatus(ctx, req.(*EpiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PortPhysicalStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EpiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PortPhysicalStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PortPhysicalStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PortPhysicalStatus(ctx, req.(*EpiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_EnableDisablePort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableDisablePortRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).EnableDisablePort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_EnableDisablePort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).EnableDisablePort(ctx, req.(*EnableDisablePortRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_LiveEpis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).LiveEpis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_LiveEpis_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).LiveEpis(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_WifiStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).WifiStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_WifiStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).WifiStatus(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_NetworkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).NetworkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_NetworkInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).NetworkInfo(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DeviceNightlyUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DeviceNightlyUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DeviceNightlyUpdateTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DeviceNightlyUpdateTime(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PortForwardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PortForwardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PortForwardList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PortForwardList(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PriorityInterface_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PriorityInterface(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PriorityInterface_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PriorityInterface(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PrimarySim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PrimarySim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PrimarySim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PrimarySim(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_CurrentApn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).CurrentApn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_CurrentApn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).CurrentApn(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_EpikUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).EpikUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_EpikUpdateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).EpikUpdateStatus(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_SystemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).SystemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_SystemInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).SystemInfo(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DCConnectionStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DCConnectionStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DCConnectionStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DCConnectionStats(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DnsCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DnsCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DnsCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DnsCheck(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_VSwitchTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).VSwitchTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_VSwitchTab_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).VSwitchTab(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_LteAnalyzer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).LteAnalyzer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_LteAnalyzer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).LteAnalyzer(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_SpeedTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).SpeedTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_SpeedTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).SpeedTest(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_InitLtePerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).InitLtePerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_InitLtePerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).InitLtePerf(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_FetchLtePerf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).FetchLtePerf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_FetchLtePerf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).FetchLtePerf(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_PortConfigValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EpiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).PortConfigValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_PortConfigValues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).PortConfigValues(ctx, req.(*EpiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_HandleRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeviceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).HandleRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_HandleRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).HandleRequest(ctx, req.(*DeviceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_EnqueueRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AsyncRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).EnqueueRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_EnqueueRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).EnqueueRequest(ctx, req.(*AsyncRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_GetRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).GetRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_GetRequestStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).GetRequestStatus(ctx, req.(*StatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_CancelRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequestMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).CancelRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_CancelRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).CancelRequest(ctx, req.(*CancelRequestMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_DashboardData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).DashboardData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_DashboardData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).DashboardData(ctx, req.(*DashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_ExecuteSshCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SshCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EdgeDeviceProxyServer).ExecuteSshCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EdgeDeviceProxy_ExecuteSshCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EdgeDeviceProxyServer).ExecuteSshCommand(ctx, req.(*SshCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EdgeDeviceProxy_ExecuteSshCommandAsync_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SshCommandRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EdgeDeviceProxyServer).ExecuteSshCommandAsync(m, &grpc.GenericServerStream[SshCommandRequest, SshCommandResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type EdgeDeviceProxy_ExecuteSshCommandAsyncServer = grpc.ServerStreamingServer[SshCommandResponse]

// EdgeDeviceProxy_ServiceDesc is the grpc.ServiceDesc for EdgeDeviceProxy service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EdgeDeviceProxy_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "edge.v1.EdgeDeviceProxy",
	HandlerType: (*EdgeDeviceProxyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UnaryEcho",
			Handler:    _EdgeDeviceProxy_UnaryEcho_Handler,
		},
		{
			MethodName: "PowerSource",
			Handler:    _EdgeDeviceProxy_PowerSource_Handler,
		},
		{
			MethodName: "ActiveInterface",
			Handler:    _EdgeDeviceProxy_ActiveInterface_Handler,
		},
		{
			MethodName: "LanIp",
			Handler:    _EdgeDeviceProxy_LanIp_Handler,
		},
		{
			MethodName: "PublicIp",
			Handler:    _EdgeDeviceProxy_PublicIp_Handler,
		},
		{
			MethodName: "SignalStrength",
			Handler:    _EdgeDeviceProxy_SignalStrength_Handler,
		},
		{
			MethodName: "SimStatus",
			Handler:    _EdgeDeviceProxy_SimStatus_Handler,
		},
		{
			MethodName: "ModemInfo",
			Handler:    _EdgeDeviceProxy_ModemInfo_Handler,
		},
		{
			MethodName: "SensorData",
			Handler:    _EdgeDeviceProxy_SensorData_Handler,
		},
		{
			MethodName: "DeviceOnline",
			Handler:    _EdgeDeviceProxy_DeviceOnline_Handler,
		},
		{
			MethodName: "DcAvgPing",
			Handler:    _EdgeDeviceProxy_DcAvgPing_Handler,
		},
		{
			MethodName: "RegStatus",
			Handler:    _EdgeDeviceProxy_RegStatus_Handler,
		},
		{
			MethodName: "PortRegStatus",
			Handler:    _EdgeDeviceProxy_PortRegStatus_Handler,
		},
		{
			MethodName: "PortPhysicalStatus",
			Handler:    _EdgeDeviceProxy_PortPhysicalStatus_Handler,
		},
		{
			MethodName: "EnableDisablePort",
			Handler:    _EdgeDeviceProxy_EnableDisablePort_Handler,
		},
		{
			MethodName: "LiveEpis",
			Handler:    _EdgeDeviceProxy_LiveEpis_Handler,
		},
		{
			MethodName: "WifiStatus",
			Handler:    _EdgeDeviceProxy_WifiStatus_Handler,
		},
		{
			MethodName: "NetworkInfo",
			Handler:    _EdgeDeviceProxy_NetworkInfo_Handler,
		},
		{
			MethodName: "DeviceNightlyUpdateTime",
			Handler:    _EdgeDeviceProxy_DeviceNightlyUpdateTime_Handler,
		},
		{
			MethodName: "PortForwardList",
			Handler:    _EdgeDeviceProxy_PortForwardList_Handler,
		},
		{
			MethodName: "PriorityInterface",
			Handler:    _EdgeDeviceProxy_PriorityInterface_Handler,
		},
		{
			MethodName: "PrimarySim",
			Handler:    _EdgeDeviceProxy_PrimarySim_Handler,
		},
		{
			MethodName: "CurrentApn",
			Handler:    _EdgeDeviceProxy_CurrentApn_Handler,
		},
		{
			MethodName: "EpikUpdateStatus",
			Handler:    _EdgeDeviceProxy_EpikUpdateStatus_Handler,
		},
		{
			MethodName: "SystemInfo",
			Handler:    _EdgeDeviceProxy_SystemInfo_Handler,
		},
		{
			MethodName: "DCConnectionStats",
			Handler:    _EdgeDeviceProxy_DCConnectionStats_Handler,
		},
		{
			MethodName: "DnsCheck",
			Handler:    _EdgeDeviceProxy_DnsCheck_Handler,
		},
		{
			MethodName: "VSwitchTab",
			Handler:    _EdgeDeviceProxy_VSwitchTab_Handler,
		},
		{
			MethodName: "LteAnalyzer",
			Handler:    _EdgeDeviceProxy_LteAnalyzer_Handler,
		},
		{
			MethodName: "SpeedTest",
			Handler:    _EdgeDeviceProxy_SpeedTest_Handler,
		},
		{
			MethodName: "InitLtePerf",
			Handler:    _EdgeDeviceProxy_InitLtePerf_Handler,
		},
		{
			MethodName: "FetchLtePerf",
			Handler:    _EdgeDeviceProxy_FetchLtePerf_Handler,
		},
		{
			MethodName: "PortConfigValues",
			Handler:    _EdgeDeviceProxy_PortConfigValues_Handler,
		},
		{
			MethodName: "HandleRequest",
			Handler:    _EdgeDeviceProxy_HandleRequest_Handler,
		},
		{
			MethodName: "EnqueueRequest",
			Handler:    _EdgeDeviceProxy_EnqueueRequest_Handler,
		},
		{
			MethodName: "GetRequestStatus",
			Handler:    _EdgeDeviceProxy_GetRequestStatus_Handler,
		},
		{
			MethodName: "CancelRequest",
			Handler:    _EdgeDeviceProxy_CancelRequest_Handler,
		},
		{
			MethodName: "DashboardData",
			Handler:    _EdgeDeviceProxy_DashboardData_Handler,
		},
		{
			MethodName: "ExecuteSshCommand",
			Handler:    _EdgeDeviceProxy_ExecuteSshCommand_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "PingBox",
			Handler:       _EdgeDeviceProxy_PingBox_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "ExecuteSshCommandAsync",
			Handler:       _EdgeDeviceProxy_ExecuteSshCommandAsync_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "proto/edge/v1/notifications.proto",
}

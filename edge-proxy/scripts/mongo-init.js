// MongoDB initialization script for edge-proxy
// This script sets up the database schema and sample data

// Switch to the application database
db = db.getSiblingDB('epikFax');

// Queue-related collections removed - using direct synchronous processing

// Add SSH connections collection
db.createCollection('ssh_connections', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['host', 'status', 'created_at', 'last_used'],
      properties: {
        host: {
          bsonType: 'string',
          description: 'SSH host address'
        },
        status: {
          bsonType: 'string',
          enum: ['connected', 'disconnected', 'error'],
          description: 'Connection status'
        },
        created_at: {
          bsonType: 'date',
          description: 'Connection creation timestamp'
        },
        expires_at: {
          bsonType: 'date',
          description: 'Connection expiration timestamp'
        },
        last_used: {
          bsonType: 'date',
          description: 'Last usage timestamp'
        },
        error: {
          bsonType: 'string',
          description: 'Error message if status is error'
        }
      }
    }
  }
});

// Create indexes for optimal performance
print('Creating indexes...');

// epikboxes indexes
db.epikboxes.createIndex({ 'serialNumber': 1 }, { unique: true });
db.epikboxes.createIndex({ 'datacenter': 1 });
db.epikboxes.createIndex({ 'vpnAddress': 1 });

// Queue-related indexes removed

// SSH connections indexes
db.ssh_connections.createIndex({ 'host': 1 }, { unique: true });
db.ssh_connections.createIndex({ 'status': 1 });
db.ssh_connections.createIndex({ 'expires_at': 1 });
db.ssh_connections.createIndex({ 'last_used': 1 });

print('Indexes created successfully!');

package proxy

import (
	"context"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Server struct {
	client        boxes.EdgeDeviceClient
	BoxRepository boxes.BoxFinder // Changed to BoxFinder
}

func NewServer(client boxes.EdgeDeviceClient, boxRepo boxes.BoxFinder) (*Server, error) { // Changed to BoxFinder
	return &Server{
		client:        client,
		BoxRepository: boxRepo,
	}, nil
}

func (s *Server) GetBoxVpnAddress(ctx context.Context, serialNumber string) (string, error) {
	box, err := s.BoxRepository.FindBySerialNumber(ctx, serialNumber)
	if err != nil {
		return "", err
	}
	if box == nil {
		return "", status.Errorf(codes.NotFound, "box not found: %s", serialNumber)
	}
	vpnAddr, err := boxes.ToDatacenterIP(box.VPNAddress, box.Datacenter)
	if err != nil {
		return "", status.Errorf(codes.Internal, "failed to translate VPN address: %v", err)
	}
	return vpnAddr, nil
}

func (s *Server) ProxyRequest(ctx context.Context, serialNumber string, path string, method string, payload []byte) (int32, []byte, map[string]string, error) { // pass nil if no payload
	// Add timeout for the entire proxy operation
	ctx, cancel := context.WithTimeout(ctx, 120*time.Second)
	// ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // increase timeout
	defer cancel()
	vpnAddr, err := s.GetBoxVpnAddress(ctx, serialNumber)
	if err != nil {
		return 0, nil, nil, err
	}
	// Make the request to the box with remaining context
	// resp, err := s.client.Request(ctx, vpnAddr, boxes.Restbin, path, method...)

	var resp *boxes.Response
	if payload != nil {
		resp, err = s.client.RequestWithPayload(ctx, vpnAddr, boxes.Restbin, path, method, payload)
	} else {
		resp, err = s.client.Request(ctx, vpnAddr, boxes.Restbin, path, method)
	}
	if err != nil {
		return 0, nil, nil, status.Errorf(codes.Internal, "failed to contact box: %v", err)
	}

	return int32(resp.StatusCode), resp.Body, resp.Headers, nil
}

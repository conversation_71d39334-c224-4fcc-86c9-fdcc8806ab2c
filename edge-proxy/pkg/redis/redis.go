package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/config"
	"github.com/redis/go-redis/v9"
)

var Client *redis.Client

// Connect initializes the global Redis client
func Connect(db int) error {
	Client = redis.NewClient(&redis.Options{
		Addr: config.AppConfig.REDIS_SERVER + ":" + config.AppConfig.REDIS_PORT,
		DB:   db,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := Client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	fmt.Println("Redis connected.")
	return nil
}

// Close closes the Redis connection
func Close() error {
	if Client != nil {
		return Client.Close()
	}
	return nil
}

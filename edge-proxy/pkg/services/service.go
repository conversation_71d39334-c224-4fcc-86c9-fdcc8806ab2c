package services

import (
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/services/apr"
	"github.com/EPIKio/myepikV2/edge/pkg/services/epi"
)

// Services contains all service instances
type Services struct {
	APR *apr.Service
	EPI *epi.Service
}

// NewServices creates and returns all service instances
func NewServices(httpClient boxes.EdgeDeviceClient) *Services {
	epiService := epi.NewService(httpClient)
	aprService := apr.NewService(httpClient, epiService)

	return &Services{
		APR: aprService,
		EPI: epiService,
	}
}

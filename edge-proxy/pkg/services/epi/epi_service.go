package epi

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
)

func calculateEpiIpByBoxPortNumber(portNumber int32) string {
	ataNumber := int(math.Ceil(float64(portNumber) / 2))
	epiIp := fmt.Sprintf("************%d", ataNumber)
	return epiIp
}

func calculateEpiPortByBoxPortNumber(portNumber int) int {
	if portNumber%2 == 0 {
		return 2
	}
	return 1
}

// Service provides centralized EPI functionality
type Service struct {
	httpClient boxes.EdgeDeviceClient
}

// NewService creates a new EPI service instance
func NewService(httpClient boxes.EdgeDeviceClient) *Service {
	return &Service{httpClient: httpClient}
}
func (s *Service) EnableDisablePort(ctx context.Context, boxID string, portNumber int32, enable bool) (*EnableDisablePortResult, error) {
	type EpiStatusRaw struct {
		Resp string `json:"resp"`
	}

	epiIp := calculateEpiIpByBoxPortNumber(portNumber)
	epiPort := calculateEpiPortByBoxPortNumber(int(portNumber))
	mode := "enable"
	if !enable {
		mode = "disable"
	}

	path := fmt.Sprintf("/epi/%s/%d/%s", epiIp, epiPort, mode)
	response, err := s.httpClient.Request(ctx, boxID, boxes.Restbin, path, "GET")
	if err != nil {
		return nil, fmt.Errorf("failed to %s port: %w", mode, err)
	}

	var bodyObj EpiStatusRaw
	err = json.Unmarshal(response.Body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	success := strings.Contains(strings.ToLower(bodyObj.Resp), "enabled successfully") ||
		strings.Contains(strings.ToLower(bodyObj.Resp), "disabled successfully")

	return &EnableDisablePortResult{
		Success: success,
		Message: bodyObj.Resp,
	}, nil
}

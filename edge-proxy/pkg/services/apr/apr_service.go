package apr

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/services/epi"
)

// Service provides centralized APR functionality
type Service struct {
	httpClient boxes.EdgeDeviceClient
	epiService *epi.Service
}

func containsAny(s string, subs ...string) bool {
	for _, sub := range subs {
		if strings.Contains(s, sub) {
			return true
		}
	}
	return false
}

func firstNonEmpty(values ...string) string {
	for _, v := range values {
		if strings.TrimSpace(v) != "" {
			return v
		}
	}
	return ""
}

// NewService creates a new APR service
func NewService(httpClient boxes.EdgeDeviceClient, epiService *epi.Service) *Service {
	return &Service{
		httpClient: httpClient,
		epiService: epiService,
	}
}

// ToggleAPR enables or disables APR on a specific port
func (s *Service) ToggleAPR(ctx context.Context, boxID, port string, enable bool) (*APRToggleResult, error) {
	enableDisableText := map[bool]string{true: "enable", false: "disable"}[enable]
	path := fmt.Sprintf("/aprtoggle/%s/%s", port, enableDisableText)

	var bodyObj struct {
		Response string `json:"response"`
	}

	response, err := s.httpClient.Request(ctx, boxID, boxes.Restbin, path, "GET")
	if err != nil {
		return nil, fmt.Errorf("failed to toggle APR: %w", err)
	}

	err = json.Unmarshal(response.Body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	var status bool
	if enable {
		status = containsAny(strings.ToLower(bodyObj.Response), "already enabled", "successfully enabled")
	} else {
		status = containsAny(strings.ToLower(bodyObj.Response), "already disabled", "successfully disabled")
	}

	return &APRToggleResult{
		Status:   status,
		Response: bodyObj.Response,
	}, nil
}

// initiateAPRTest starts the actual APR test on the device
func (s *Service) InitiateAPRTest(ctx context.Context, boxID, port string) (*APRStatus, error) {
	path := fmt.Sprintf("/aprtest/%s", port)
	var bodyObj struct {
		Response string `json:"response"`
	}
	response, err := s.httpClient.Request(ctx, boxID, boxes.Restbin, path, "GET")
	if err != nil {
		return nil, fmt.Errorf("failed to initiate APR test: %w", err)
	}
	err = json.Unmarshal(response.Body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	return &APRStatus{
		Status:   containsAny(strings.ToLower(bodyObj.Response), "in progress", "Test Initiated"),
		Response: bodyObj.Response,
	}, nil
}

// GetAPRTestResults gets the test results for a specific port
func (s *Service) GetAPRTestResults(ctx context.Context, boxID, port string) (*APRTestResultResponse, error) {
	path := fmt.Sprintf("/aprtestresults/%s", port)

	response, err := s.httpClient.Request(ctx, boxID, boxes.Restbin, path, "GET")
	if err != nil {
		return nil, fmt.Errorf("failed to get test results: %w", err)
	}

	var result struct {
		Result    string `json:"result"`
		Details   string `json:"details"`
		Timestamp string `json:"timeStamp"`
		Error     string `json:"error"`
	}
	err = json.Unmarshal(response.Body, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to parse test results: %w", err)
	}

	return &APRTestResultResponse{
		Result:    result.Result == "Passed",
		Response:  firstNonEmpty(result.Details, result.Error, result.Result),
		Timestamp: result.Timestamp,
	}, nil
}

// GetAPRStatus gets the current status of an APR port
func (s *Service) GetAPRStatus(ctx context.Context, boxID, port string) (*APRStatus, error) {
	path := fmt.Sprintf("/aprstatus/%s", port)

	response, err := s.httpClient.Request(ctx, boxID, boxes.Restbin, path, "GET")
	if err != nil {
		return nil, fmt.Errorf("failed to get APR status: %w", err)
	}

	var status struct {
		Status   bool   `json:"status"`
		Response string `json:"msg"`
	}
	err = json.Unmarshal(response.Body, &status)
	if err != nil {
		return nil, fmt.Errorf("failed to parse status response: %w", err)
	}

	return &APRStatus{
		Status:   status.Status,
		Response: status.Response,
	}, nil
}

func (s *Service) DisableCompanionPort(ctx context.Context, boxID, companionPort string) error {
	if companionPort == "" {
		return nil
	}

	// Convert string port to int32
	var portNum int32
	fmt.Sscanf(companionPort, "%d", &portNum)

	result, err := s.epiService.EnableDisablePort(ctx, boxID, portNum, false)
	if err != nil {
		return err
	}

	if !result.Success {
		return fmt.Errorf("failed to disable companion port: %s", result.Message)
	}

	return nil
}

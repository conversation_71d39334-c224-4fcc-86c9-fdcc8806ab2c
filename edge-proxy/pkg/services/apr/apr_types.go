package apr

import "time"

// APRToggleResult represents the result of toggling APR
type APRToggleResult struct {
	Status   bool   `json:"status"`
	Response string `json:"response"`
}

// APRStatus represents APR port status
type APRStatus struct {
	Status   bool   `json:"status"`
	Response string `json:"response"`
}

// APRTestResultResponse represents test result response
type APRTestResultResponse struct {
	Result    bool   `json:"result"`
	Response  string `json:"response"`
	Timestamp string `json:"timestamp"`
}

// TestStartResult represents the result of starting a test
type TestStartResult struct {
	TestID    string    `json:"test_id"`
	Status    string    `json:"status"`
	StartTime time.Time `json:"start_time"`
}

// TestStatusResult represents current test status
type TestStatusResult struct {
	TestID      string     `json:"test_id"`
	Status      string     `json:"status"`
	StartTime   time.Time  `json:"start_time"`
	EndTime     *time.Time `json:"end_time,omitempty"`
	Result      string     `json:"result,omitempty"`
	TimeElapsed string     `json:"time_elapsed"`
	TimeLeft    string     `json:"time_left,omitempty"`
}

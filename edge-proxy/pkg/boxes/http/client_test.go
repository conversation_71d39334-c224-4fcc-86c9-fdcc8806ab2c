package http_test

import (
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	. "github.com/EPIKio/myepikV2/edge/pkg/bdd"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
)

// mockEngine simulates real HTTP responses and implements the Requester interface.
type mockEngine struct {
	response  *http.Response
	err       error
	lastReq   *http.Request
	delay     time.Duration // Simulates network delay
	callCount int           // Count the number of calls for retry testing

	// doFunc allows customizing the behavior of Do in tests.
	doFunc func(req *http.Request) (*http.Response, error)
}

// Do implements the Requester interface.
func (m *mockEngine) Do(req *http.Request) (*http.Response, error) {
	m.callCount++
	m.lastReq = req
	if m.delay > 0 {
		time.Sleep(m.delay)
	}
	if m.doFunc != nil {
		return m.doFunc(req)
	}
	return m.response, m.err
}

func TestClient(t *testing.T) {
	Scenario("Making HTTP requests to edge devices", t, func() {
		Given("a successful HTTP response", func() {
			engine := &mockEngine{
				response: &http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(`{"status":"ok"}`)),
					Header:     http.Header{"Content-Type": []string{"application/json"}},
				},
			}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			// Override sleep to avoid real delays.
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should succeed", func() {
					Check(err, ShouldBeNil)
				})

				Then("it should construct the correct URL", func() {
					Check(engine.lastReq.URL.String(), ShouldEqual, "http://10.15.1.2:9988/test")
				})

				Then("it should return status 200", func() {
					Check(resp.StatusCode, ShouldEqual, 200)
				})

				Then("it should parse the response correctly", func() {
					Check(string(resp.Body), ShouldEqual, `{"status":"ok"}`)
				})
			})
		})

		Given("a timeout occurs", func() {
			engine := &mockEngine{err: context.DeadlineExceeded}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should return a timeout error", func() {
					Check(errors.Is(err, context.DeadlineExceeded), ShouldBeTrue)
				})

				Then("response should be nil", func() {
					Check(resp, ShouldBeNil)
				})
			})
		})

		Given("a network failure occurs", func() {
			engine := &mockEngine{err: errors.New("network failure")}
			client := boxhttp.NewClient(engine, boxhttp.DefaultConfig)
			client.SetSleep(func(d time.Duration) {})

			When("sending a request", func() {
				resp, err := client.Request(context.Background(), "10.15.1.2", boxes.Restbin, "/test")

				Then("it should return a network error", func() {
					Check(err.Error(), ShouldContainSubstring, "network failure")
				})

				Then("response should be nil", func() {
					Check(resp, ShouldBeNil)
				})
			})
		})

		// Retry functionality tests removed - not implemented in current client
	})
}

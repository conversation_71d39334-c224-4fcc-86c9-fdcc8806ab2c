package boxes

import (
	"fmt"
	"strconv"
	"strings"
)

// ErrInvalidIP is returned when the IP address format is invalid
var ErrInvalidIP = fmt.Errorf("invalid IP address format")

// ToDatacenterIP converts an IP address based on datacenter-specific VPN rules.
// Returns the translated IP address or an error if the input is invalid.
func ToDatacenterIP(ip, datacenter string) (string, error) {
	// LA is our reference datacenter - no translation needed
	if datacenter == "LA" {
		return ip, nil
	}

	// Parse IP address
	octets := strings.Split(ip, ".")
	if len(octets) != 4 {
		return "", ErrInvalidIP
	}

	// Convert octets to integers for comparison
	o1, err := strconv.Atoi(octets[0])
	if err != nil {
		return "", ErrInvalidIP
	}
	o2, err := strconv.Atoi(octets[1])
	if err != nil {
		return "", ErrInvalidIP
	}

	// Only translate VPN addresses (10.x.x.x)
	if o1 != 10 {
		return ip, nil
	}

	// Only translate addresses with second octet 15 or 64
	if o2 != 15 && o2 != 64 {
		return ip, nil
	}

	// Get the translation map for this second octet
	translations := getTranslations(o2)

	// Look up the new second octet (case-insensitive)
	newO2, exists := translations[strings.ToLower(datacenter)]
	if !exists {
		// If datacenter isn't in our translation map, return unchanged
		return ip, nil
	}

	// Return the translated IP
	return fmt.Sprintf("10.%d.%s.%s", newO2, octets[2], octets[3]), nil
}

// getTranslations returns the translation map for a given second octet
func getTranslations(secondOctet int) map[string]int {
	if secondOctet == 15 {
		return map[string]int{
			"ch":  18,
			"ny":  19,
			"at":  20,
			"da":  21, // Dallas
			"dl":  21, // Dallas alternative
			"dal": 21, // Dallas alternative
		}
	}
	if secondOctet == 64 {
		return map[string]int{
			"ch":  66,
			"ny":  67,
			"at":  68,
			"da":  69, // Dallas
			"dl":  69, // Dallas alternative
			"dal": 69, // Dallas alternative
		}
	}
	return map[string]int{}
}

package methods

import (
	"context"
	"time"
)

var Commands = map[string]func(context.Context, *Command) MethodResponse{
	// "apr": ExecuteAPRTest,
}

// Command represents a command to be executed on a box
type Command struct {
	ID               string    `bson:"_id" json:"id"`
	BoxID            string    `bson:"box_id" json:"box_id"`
	Command          string    `bson:"command" json:"command"`
	Payload          []byte    `bson:"payload" json:"payload"`
	ExecutionSorting int       `bson:"executionSorting" json:"executionSorting"`
	Status           string    `bson:"status" json:"status"`
	CreatedAt        time.Time `bson:"created_at" json:"created_at"`
	UpdatedAt        time.Time `bson:"updated_at" json:"updated_at"`

	ResponseBody map[string]interface{} `bson:"response_body,omitempty" json:"response_body,omitempty"`
	Error        string                 `bson:"error,omitempty" json:"error,omitempty"`
	CompletedAt  *time.Time             `bson:"completed_at,omitempty" json:"completed_at,omitempty"`

	// Webhook
	WebhookURL string `bson:"webhook_url,omitempty" json:"webhook_url,omitempty"`
}

// Status constants
const (
	StatusQueued     = "queued"
	StatusScheduled  = "scheduled"
	StatusProcessing = "processing"
	StatusCompleted  = "completed"
	StatusFailed     = "failed"
	StatusCancelled  = "cancelled"
)

type MethodResponse struct {
	Body  map[string]interface{} `json:"body"`
	Error error                  `json:"error"`
}

package command

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/command/methods"
	"github.com/EPIKio/myepikV2/edge/pkg/db"
	"github.com/EPIKio/myepikV2/edge/pkg/redis"
	redisv9 "github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// Service handles command operations
type Service struct{}

// CommandRequest represents a command request
type CommandRequest struct {
	BoxID            string `json:"box_id"`
	Command          string `json:"command"`
	Payload          []byte `json:"payload"`
	ExecutionSorting int    `json:"executionSorting"`
	WebhookURL       string `json:"webhook_url"`
}

// CommandResponse represents a command response
type CommandResponse struct {
	CommandID string `json:"command_id"`
	Status    string `json:"status"`
}

// NewService creates a new command service
func NewService() *Service {
	return &Service{}
}

// ExecuteCommand handles command execution requests
func (s *Service) ExecuteCommand(ctx context.Context, req *CommandRequest) (*CommandResponse, error) {
	// Generate command ID
	commandID := generateCommandID()

	// Create command
	cmd := &methods.Command{
		ID:               commandID,
		BoxID:            req.BoxID,
		Command:          req.Command,
		Payload:          req.Payload,
		ExecutionSorting: req.ExecutionSorting,
		Status:           methods.StatusQueued,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
		WebhookURL:       req.WebhookURL,
	}

	// Check if box is online
	isOnline, err := isBoxOnline(ctx, req.BoxID)
	if err != nil {
		return nil, fmt.Errorf("failed to check box status: %v", err)
	}

	if isOnline {
		// Queue in Redis for immediate processing
		if err := queueCommandInRedis(ctx, cmd); err != nil {
			return nil, fmt.Errorf("failed to queue command: %v", err)
		}
	} else {
		// Store in MongoDB as scheduled
		cmd.Status = methods.StatusScheduled
		if err := storeCommandInMongo(ctx, cmd); err != nil {
			return nil, fmt.Errorf("failed to store command: %v", err)
		}

		// Start waiting for box to come online (1 hour)
		go waitForBoxOnline(ctx, req.BoxID, commandID)
	}

	return &CommandResponse{
		CommandID: commandID,
		Status:    cmd.Status,
	}, nil
}

// GetCommandStatus retrieves command status
func (s *Service) GetCommandStatus(ctx context.Context, commandID string) (*methods.Command, error) {
	// First check Redis
	cmd, err := getCommandFromRedis(ctx, commandID)
	if err == nil && cmd != nil {
		return cmd, nil
	}

	// Then check MongoDB
	cmd, err = getCommandFromMongo(ctx, commandID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("command not found")
		}
		return nil, fmt.Errorf("failed to get command: %v", err)
	}

	return cmd, nil
}

// NotifyBoxOnline handles box online notifications
func (s *Service) NotifyBoxOnline(ctx context.Context, boxID string) error {
	// Mark box as online in Redis
	if err := markBoxOnline(ctx, boxID); err != nil {
		return fmt.Errorf("failed to mark box online: %v", err)
	}

	// Process any scheduled commands for this box
	go processScheduledCommands(ctx, boxID)

	return nil
}

// Helper functions

func generateCommandID() string {
	return fmt.Sprintf("cmd_%d", time.Now().UnixNano())
}

func isBoxOnline(ctx context.Context, boxID string) (bool, error) {
	if redis.Client == nil {
		return false, fmt.Errorf("redis not connected")
	}

	result := redis.Client.Get(ctx, fmt.Sprintf("box:online:%s", boxID))
	return result.Err() == nil, nil
}

func markBoxOnline(ctx context.Context, boxID string) error {
	if redis.Client == nil {
		return fmt.Errorf("redis not connected")
	}

	return redis.Client.Set(ctx, fmt.Sprintf("box:online:%s", boxID), "1", 5*time.Minute).Err()
}

func queueCommandInRedis(ctx context.Context, cmd *methods.Command) error {
	if redis.Client == nil {
		return fmt.Errorf("redis not connected")
	}

	// Store command data
	cmdJSON, err := json.Marshal(cmd)
	if err != nil {
		return err
	}

	if err := redis.Client.Set(ctx, fmt.Sprintf("command:%s", cmd.ID), cmdJSON, time.Hour).Err(); err != nil {
		return err
	}

	// Add to priority queue
	queueKey := fmt.Sprintf("queue:commands:%s", cmd.BoxID)
	return redis.Client.ZAdd(ctx, queueKey, redisv9.Z{
		Score:  float64(cmd.ExecutionSorting),
		Member: cmd.ID,
	}).Err()
}

func storeCommandInMongo(ctx context.Context, cmd *methods.Command) error {
	if db.DB == nil {
		return fmt.Errorf("mongodb not connected")
	}

	collection := db.DB.Collection("commands")
	_, err := collection.InsertOne(ctx, cmd)
	return err
}

func getCommandFromRedis(ctx context.Context, commandID string) (*methods.Command, error) {
	if redis.Client == nil {
		return nil, fmt.Errorf("redis not connected")
	}

	result := redis.Client.Get(ctx, fmt.Sprintf("command:%s", commandID))
	if result.Err() != nil {
		return nil, result.Err()
	}

	var cmd methods.Command
	if err := json.Unmarshal([]byte(result.Val()), &cmd); err != nil {
		return nil, err
	}

	return &cmd, nil
}

func getCommandFromMongo(ctx context.Context, commandID string) (*methods.Command, error) {
	if db.DB == nil {
		return nil, fmt.Errorf("mongodb not connected")
	}

	collection := db.DB.Collection("commands")
	var cmd methods.Command
	err := collection.FindOne(ctx, bson.M{"_id": commandID}).Decode(&cmd)
	return &cmd, err
}

func waitForBoxOnline(ctx context.Context, boxID, commandID string) {
	// Wait for 1 hour for box to come online
	timeout := time.After(time.Hour)
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			// Box didn't come online in 1 hour, keep command scheduled
			return
		case <-ticker.C:
			online, err := isBoxOnline(ctx, boxID)
			if err == nil && online {
				// Box came online, move command to Redis queue
				cmd, err := getCommandFromMongo(ctx, commandID)
				if err == nil && cmd.Status == methods.StatusScheduled {
					cmd.Status = methods.StatusQueued
					cmd.UpdatedAt = time.Now()
					queueCommandInRedis(ctx, cmd)
				}
				return
			}
		}
	}
}

func processScheduledCommands(ctx context.Context, boxID string) {
	if db.DB == nil {
		return
	}

	// Find scheduled commands for this box
	collection := db.DB.Collection("commands")
	cursor, err := collection.Find(ctx, bson.M{
		"box_id": boxID,
		"status": methods.StatusScheduled,
	})
	if err != nil {
		return
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var cmd methods.Command
		if err := cursor.Decode(&cmd); err != nil {
			continue
		}

		// Move to Redis queue
		cmd.Status = methods.StatusQueued
		cmd.UpdatedAt = time.Now()
		if err := queueCommandInRedis(ctx, &cmd); err != nil {
			continue
		}

		// Update status in MongoDB
		collection.UpdateOne(ctx, bson.M{"_id": cmd.ID}, bson.M{
			"$set": bson.M{
				"status":     methods.StatusQueued,
				"updated_at": time.Now(),
			},
		})
	}
}

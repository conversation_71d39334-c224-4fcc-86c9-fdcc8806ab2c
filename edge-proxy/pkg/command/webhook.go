package command

import (
	"encoding/json"
	"net/http"

	"github.com/gorilla/mux"
)

// WebhookHandler handles webhook notifications for box online status
type WebhookHandler struct{}

// NewWebhookHandler creates a new webhook handler
func NewWebhookHandler() *WebhookHandler {
	return &WebhookHandler{}
}

// RegisterRoutes registers webhook routes
func (w *WebhookHandler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/webhook/box-online", w.handleBoxOnline).Methods("POST")
}

// BoxOnlineWebhook represents the webhook payload for box online notification
type BoxOnlineWebhook struct {
	BoxID string `json:"box_id"`
}

// handleBoxOnline handles box online webhook notifications
func (w *WebhookHandler) handleBoxOnline(rw http.ResponseWriter, r *http.Request) {
	var payload BoxOnlineWebhook
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(rw, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if payload.BoxID == "" {
		http.Error(rw, "box_id is required", http.StatusBadRequest)
		return
	}

	// Mark box as online and process scheduled commands
	if err := markBoxOnline(r.Context(), payload.BoxID); err != nil {
		http.Error(rw, "Failed to mark box online", http.StatusInternalServerError)
		return
	}

	// Process scheduled commands
	go processScheduledCommands(r.Context(), payload.BoxID)

	rw.Header().Set("Content-Type", "application/json")
	json.NewEncoder(rw).Encode(map[string]string{
		"status": "success",
		"box_id": payload.BoxID,
	})
}

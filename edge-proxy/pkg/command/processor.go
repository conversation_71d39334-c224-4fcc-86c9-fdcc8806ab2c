package command

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	"github.com/EPIKio/myepikV2/edge/pkg/command/methods"
	"github.com/EPIKio/myepikV2/edge/pkg/db"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"github.com/EPIKio/myepikV2/edge/pkg/redis"
	"go.mongodb.org/mongo-driver/bson"
)

// Processor handles command processing
type Processor struct {
	httpClient  boxes.EdgeDeviceClient
	proxyServer *proxy.Server
	stopCh      chan struct{}
}

// NewProcessor creates a new command processor
func NewProcessor(httpClient boxes.EdgeDeviceClient, proxy *proxy.Server) *Processor {

	return &Processor{
		httpClient:  httpClient,
		proxyServer: proxy,
		stopCh:      make(chan struct{}),
	}
}

// Start starts the command processor
func (p *Processor) Start(ctx context.Context) {
	go p.processCommands(ctx)
}

// Stop stops the command processor
func (p *Processor) Stop() {
	close(p.stopCh)
}

// processCommands continuously processes commands from Redis queues
func (p *Processor) processCommands(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-p.stopCh:
			return
		case <-ticker.C:
			p.processNextCommand(ctx)
		}
	}
}

// processNextCommand processes the next available command
func (p *Processor) processNextCommand(ctx context.Context) {
	if redis.Client == nil {
		return
	}

	// Get all queue keys (box queues)
	keys, err := redis.Client.Keys(ctx, "queue:commands:*").Result()
	if err != nil {
		return
	}

	for _, queueKey := range keys {
		// Get highest priority command (lowest score)
		result := redis.Client.ZPopMin(ctx, queueKey, 1)
		if result.Err() != nil || len(result.Val()) == 0 {
			continue
		}

		commandID := result.Val()[0].Member.(string)

		// Get command details
		cmd, err := getCommandFromRedis(ctx, commandID)
		if err != nil {
			log.Printf("Failed to get command %s: %v", commandID, err)
			continue
		}

		// Process the command
		p.executeCommand(ctx, cmd)
		return // Process one command at a time
	}
}

// executeCommand executes a single command
func (p *Processor) executeCommand(ctx context.Context, cmd *methods.Command) {
	log.Printf("Processing command %s for box %s", cmd.ID, cmd.BoxID)

	// Update status to processing
	cmd.Status = methods.StatusProcessing
	cmd.UpdatedAt = time.Now()
	p.updateCommand(ctx, cmd)

	commands := methods.Commands
	var response methods.MethodResponse
	// Call function if it exists
	if fn, ok := commands[cmd.Command]; ok {
		response = fn(ctx, cmd)
		if response.Error != nil {
			cmd.Error = response.Error.Error()
			p.failCommand(ctx, cmd, fmt.Sprintf("Command execution failed: %v", response.Error))
			return
		}
	} else {
		fmt.Println("Unknown command:", cmd)
	}

	// Update command with result
	now := time.Now()
	cmd.Status = methods.StatusCompleted
	cmd.ResponseBody = response.Body
	cmd.CompletedAt = &now
	cmd.UpdatedAt = now

	p.updateCommand(ctx, cmd)

	// Send webhook if configured
	if cmd.WebhookURL != "" {
		go p.sendWebhook(cmd)
	}

	log.Printf("Command %s completed successfully", cmd.ID)
}

// failCommand marks a command as failed
func (p *Processor) failCommand(ctx context.Context, cmd *methods.Command, errorMsg string) {
	now := time.Now()
	cmd.Status = methods.StatusFailed
	cmd.Error = errorMsg
	cmd.CompletedAt = &now
	cmd.UpdatedAt = now

	p.updateCommand(ctx, cmd)
	log.Printf("Command %s failed: %s", cmd.ID, errorMsg)
}

// updateCommand updates command in both Redis and MongoDB
func (p *Processor) updateCommand(ctx context.Context, cmd *methods.Command) {
	// Update in Redis
	if redis.Client != nil {
		cmdJSON, _ := json.Marshal(cmd)
		redis.Client.Set(ctx, fmt.Sprintf("command:%s", cmd.ID), cmdJSON, time.Hour)
	}

	// Update in MongoDB
	if db.DB != nil {
		collection := db.DB.Collection("commands")
		collection.ReplaceOne(ctx, bson.M{"_id": cmd.ID}, cmd)
	}
}

// sendWebhook sends a webhook notification (simplified implementation)
func (p *Processor) sendWebhook(cmd *methods.Command) {
	// TODO: Implement webhook sending
	log.Printf("Would send webhook for command %s to %s", cmd.ID, cmd.WebhookURL)
}

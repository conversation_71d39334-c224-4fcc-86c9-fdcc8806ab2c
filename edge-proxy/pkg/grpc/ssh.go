package grpc

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"time"

	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"golang.org/x/crypto/ssh"
)

// ExecuteSshCommand executes SSH command synchronously with connection caching
func (s *Server) ExecuteSshCommand(ctx context.Context, req *pb.SshCommandRequest) (*pb.SshCommandResponse, error) {
	start := time.Now()

	// Get VPN address for the device
	vpnAddr, err := s.proxyServer.GetBoxVpnAddress(ctx, req.SerialNumber)
	if err != nil {
		return &pb.SshCommandResponse{
			ErrorMessage: fmt.Sprintf("Failed to get VPN address: %v", err),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		}, nil
	}

	// Execute SSH command using cached connection
	session, err := s.getConnectionSession(vpnAddr)
	if err != nil {
		return nil, err
	}
	output, err := session.CombinedOutput(req.Command)
	exitCode := int32(0)

	duration := time.Since(start).Milliseconds()

	if err != nil {
		return &pb.SshCommandResponse{
			Output:       string(output),
			ErrorMessage: err.Error(),
			ExitCode:     exitCode,
			DurationMs:   duration,
			Success:      false,
		}, nil
	}

	return &pb.SshCommandResponse{
		Output:     string(output),
		ExitCode:   exitCode,
		DurationMs: duration,
		Success:    exitCode == 0,
	}, nil
}

// ExecuteSshCommandAsync executes SSH command with streaming response and connection caching
func (s *Server) ExecuteSshCommandAsync(req *pb.SshCommandRequest, stream pb.EdgeDeviceProxy_ExecuteSshCommandAsyncServer) error {
	start := time.Now()

	// Send initial status
	stream.Send(&pb.SshCommandResponse{
		Output:     "Connecting to device...",
		Success:    false,
		DurationMs: 0,
	})

	// Get VPN address
	vpnAddr, err := s.proxyServer.GetBoxVpnAddress(stream.Context(), req.SerialNumber)
	if err != nil {
		return stream.Send(&pb.SshCommandResponse{
			ErrorMessage: fmt.Sprintf("Failed to get VPN address: %v", err),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		})
	}

	// Send connection status
	stream.Send(&pb.SshCommandResponse{
		Output:     fmt.Sprintf("Executing command on %s...", vpnAddr),
		Success:    false,
		DurationMs: time.Since(start).Milliseconds(),
	})

	// Execute command using cached connection
	session, err := s.getConnectionSession(vpnAddr)
	if err != nil {
		return stream.Send(&pb.SshCommandResponse{
			ErrorMessage: err.Error(),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		})
	}
	defer session.Close()

	// Get pipes for stdout and stderr
	stdout, err := session.StdoutPipe()
	if err != nil {
		return stream.Send(&pb.SshCommandResponse{
			ErrorMessage: fmt.Sprintf("Failed to get stdout pipe: %v", err),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		})
	}

	stderr, err := session.StderrPipe()
	if err != nil {
		return stream.Send(&pb.SshCommandResponse{
			ErrorMessage: fmt.Sprintf("Failed to get stderr pipe: %v", err),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		})
	}

	// Start the command
	if err := session.Start(req.Command); err != nil {
		return stream.Send(&pb.SshCommandResponse{
			ErrorMessage: fmt.Sprintf("Failed to start command: %v", err),
			Success:      false,
			DurationMs:   time.Since(start).Milliseconds(),
		})
	}

	// Stream output in real-time
	done := make(chan error, 1)
	go func() {
		done <- session.Wait()
	}()

	// Stream stdout and stderr concurrently
	outputChan := make(chan string, 100)
	errorChan := make(chan string, 100)

	go s.streamPipe(stdout, outputChan, "stdout")
	go s.streamPipe(stderr, errorChan, "stderr")

	// Stream responses until command completes
	for {
		select {
		case output := <-outputChan:
			if output != "" {
				if err := stream.Send(&pb.SshCommandResponse{
					Output:     output,
					Success:    false,
					DurationMs: time.Since(start).Milliseconds(),
				}); err != nil {
					// Client disconnected, terminate command
					session.Signal(ssh.SIGTERM)
					return err
				}
			}
		case errOutput := <-errorChan:
			if errOutput != "" {
				if err := stream.Send(&pb.SshCommandResponse{
					ErrorMessage: errOutput,
					Success:      false,
					DurationMs:   time.Since(start).Milliseconds(),
				}); err != nil {
					// Client disconnected, terminate command
					session.Signal(ssh.SIGTERM)
					return err
				}
			}
		case err := <-done:
			// Command finished
			exitCode := int32(0)
			if err != nil {
				if exitError, ok := err.(*ssh.ExitError); ok {
					exitCode = int32(exitError.ExitStatus())
				}
			}

			return stream.Send(&pb.SshCommandResponse{
				ExitCode:   exitCode,
				DurationMs: time.Since(start).Milliseconds(),
				Success:    exitCode == 0,
			})
		case <-stream.Context().Done():
			// Client cancelled or disconnected
			stream.Send(&pb.SshCommandResponse{
				ErrorMessage: "Command cancelled by client",
				Success:      false,
				DurationMs:   time.Since(start).Milliseconds(),
			})

			// Try to terminate gracefully first
			session.Signal(ssh.SIGTERM)

			// Wait a bit for graceful termination
			select {
			case <-done:
				// Command terminated gracefully
			case <-time.After(2 * time.Second):
				// Force kill if not terminated
				session.Signal(ssh.SIGKILL)
			}

			return stream.Context().Err()
		}
	}
}

func (s *Server) getConnectionSession(host string) (*ssh.Session, error) {
	client, err := s.sshManager.GetConnection(host)
	if err != nil {
		return nil, fmt.Errorf("failed to get SSH connection: %w", err)
	}

	session, err := client.NewSession()
	if err != nil {
		s.sshManager.CloseConnection(host)
		client, err = s.sshManager.GetConnection(host)
		if err != nil {
			return nil, fmt.Errorf("failed to reconnect SSH: %w", err)
		}

		session, err = client.NewSession()
		if err != nil {
			return nil, fmt.Errorf("failed to create session after reconnect: %w", err)
		}
	}
	return session, nil
}

func (s *Server) streamPipe(pipe io.Reader, outputChan chan<- string, pipeType string) {
	defer close(outputChan)

	scanner := bufio.NewScanner(pipe)
	for scanner.Scan() {
		line := scanner.Text()
		if line != "" {
			outputChan <- line
		}
	}

	if err := scanner.Err(); err != nil {
		outputChan <- fmt.Sprintf("[%s error: %v]", pipeType, err)
	}
}

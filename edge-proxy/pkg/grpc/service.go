package grpc

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
	"github.com/EPIKio/myepikV2/edge/pkg/proxy"
	"github.com/EPIKio/myepikV2/edge/pkg/services"
	aprpb "github.com/EPIKio/myepikV2/edge/proto/apr/v1"
	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"
)

// Wraps the gRPC server with our service implementation.
type Server struct {
	pb.UnimplementedEdgeDeviceProxyServer
	proxyServer *proxy.Server
	grpcServer  *grpc.Server
	listener    net.Listener
	sshManager  *SSHConnectionManager
	services    *services.Services
}

// Config contains server configuration.
type Config struct {
	Port           int
	BoxRepository  boxes.BoxFinder // Changed to use BoxFinder interface
	ProxyServer    *proxy.Server
	AutomationKey  string
	V2RootPassword string
}

// Creates a new gRPC server instance.
func New(cfg Config) (*Server, error) {
	// Create listener.
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Port))
	if err != nil {
		return nil, fmt.Errorf("failed to listen: %w", err)
	}

	// Create gRPC server with recovery interceptors
	grpcServer := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			RecoveryInterceptor,
		),
		grpc.ChainStreamInterceptor(
			StreamRecoveryInterceptor,
		),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             1 * time.Second,
			PermitWithoutStream: true,
		}),
	)

	// Create HTTP client and services
	httpClient := boxhttp.NewDefaultClient()
	allServices := services.NewServices(httpClient)

	// Create server instance.
	s := &Server{
		proxyServer: cfg.ProxyServer,
		grpcServer:  grpcServer,
		listener:    lis,
		sshManager:  NewSSHConnectionManager(),
		services:    allServices,
	}

	// Register our services.
	pb.RegisterEdgeDeviceProxyServer(grpcServer, s)

	// Register APR service
	aprServer := NewAPRServer(allServices)
	aprpb.RegisterAPRServiceServer(grpcServer, aprServer)

	// Enable reflection (add just this one line)
	reflection.Register(grpcServer)

	return s, nil
}

// Starts the gRPC server.
func (s *Server) Run() error {
	return s.grpcServer.Serve(s.listener)
}

// Gracefully stops the gRPC server.
func (s *Server) Stop() {
	if s.grpcServer != nil {
		s.grpcServer.GracefulStop()
	}
}

func (s *Server) UnaryEcho(context.Context, *pb.EchoRequest) (*pb.EchoResponse, error) {
	return &pb.EchoResponse{Message: "Hello"}, nil
}

// Implements the gRPC service method.
func (s *Server) HandleRequest(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceResponse, error) {

	return nil, nil
	// _, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber)
	// if err != nil {
	// 	return nil, err // Error is already in the correct gRPC format
	// }

	// return &pb.DeviceResponse{
	// 	Body: body,
	// }, nil
}

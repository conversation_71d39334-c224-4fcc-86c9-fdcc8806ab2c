package grpc

import (
	"context"
	"fmt"

	"github.com/EPIKio/myepikV2/edge/pkg/services"
	aprpb "github.com/EPIKio/myepikV2/edge/proto/apr/v1"
)

// APRServer implements the APR gRPC service
type APRServer struct {
	aprpb.UnimplementedAPRServiceServer
	services *services.Services
}

// NewAPRServer creates a new APR gRPC server
func NewAPRServer(services *services.Services) *APRServer {
	return &APRServer{
		services: services,
	}
}

// ToggleAPR enables or disables APR on a specific port
func (s *APRServer) ToggleAPR(ctx context.Context, req *aprpb.APRToggleRequest) (*aprpb.APRToggleResponse, error) {
	result, err := s.services.APR.ToggleAPR(ctx, req.SerialNumber, req.Port, req.Enable)
	if err != nil {
		return nil, err
	}

	return &aprpb.APRToggleResponse{
		Status:   result.Status,
		Response: result.Response,
	}, nil
}

// APRStatus gets the current APR status for a port
func (s *APRServer) APRStatus(ctx context.Context, req *aprpb.APRStatusRequest) (*aprpb.APRStatusResponse, error) {
	status, err := s.services.APR.GetAPRStatus(ctx, req.SerialNumber, req.Port)
	if err != nil {
		return nil, err
	}

	return &aprpb.APRStatusResponse{
		Status:   status.Status,
		Response: status.Response,
	}, nil
}

// APRTestResults gets the test results for a specific port
func (s *APRServer) APRTestResults(ctx context.Context, req *aprpb.APRTestResultRequest) (*aprpb.APRTestResultResponse, error) {
	result, err := s.services.APR.GetAPRTestResults(ctx, req.SerialNumber, req.Port)
	if err != nil {
		return nil, err
	}

	return &aprpb.APRTestResultResponse{
		Result:    result.Result,
		Response:  result.Response,
		Timestamp: result.Timestamp,
	}, nil
}

// InitiateAprTest initiates an APR test
func (s *APRServer) InitiateAprTest(ctx context.Context, req *aprpb.InitiateAprRequest) (*aprpb.InitiateAprResponse, error) {
	// Disable companion port if specified
	if req.CompanionPort != "" {
		err := s.services.APR.DisableCompanionPort(ctx, req.SerialNumber, req.CompanionPort)
		if err != nil {
			// Log warning but don't fail the test
			fmt.Printf("Warning: failed to disable companion port %s: %v\n", req.CompanionPort, err)
		}
	}

	// Start APR test using the centralized service
	result, err := s.services.APR.InitiateAPRTest(ctx, req.SerialNumber, req.Port)
	if err != nil {
		return nil, err
	}

	return &aprpb.InitiateAprResponse{
		Status:   result.Status,
		Response: result.Response,
	}, nil
}

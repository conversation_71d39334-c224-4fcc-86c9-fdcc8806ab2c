package grpc

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/EPIKio/myepikV2/edge/pkg/config"
	"github.com/EPIKio/myepikV2/edge/pkg/db"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/crypto/ssh"
)

type SSHConnection struct {
	Client    *ssh.Client
	Host      string
	ExpiresAt time.Time
	LastUsed  time.Time
	mu        sync.RWMutex
}

type SSHConnectionManager struct {
	connections map[string]*SSHConnection
	mu          sync.RWMutex
	config      func(host string) *ssh.ClientConfig
	ttl         time.Duration
}

type SSHConnectionStatus struct {
	Host      string    `bson:"host"`
	Status    string    `bson:"status"` // "connected", "disconnected", "error"
	CreatedAt time.Time `bson:"created_at"`
	ExpiresAt time.Time `bson:"expires_at"`
	LastUsed  time.Time `bson:"last_used"`
	Error     string    `bson:"error,omitempty"`
}

func NewSSHConnectionManager() *SSHConnectionManager {
	manager := &SSHConnectionManager{
		connections: make(map[string]*SSHConnection),
		config: func(host string) *ssh.ClientConfig {
			thridOctat := strings.Split(host, ".")[2]
			username := "automationuser"
			keyPath := config.AppConfig.AutomationKey

			key, _ := os.ReadFile(keyPath)
			signer, _ := ssh.ParsePrivateKey(key)

			auth := []ssh.AuthMethod{
				ssh.PublicKeys(signer),
			}
			if thridOctat < "20" {
				username = "root"
				auth = []ssh.AuthMethod{
					ssh.Password(config.AppConfig.V2RootPassword),
				}
			}
			config := &ssh.ClientConfig{
				User:            username, // Configure as needed
				Auth:            auth,
				HostKeyCallback: ssh.InsecureIgnoreHostKey(),
				Timeout:         30 * time.Second,
			}
			return config
		},
		ttl: 2 * time.Minute,
	}

	// Start cleanup goroutine
	go manager.cleanupExpiredConnections()

	return manager
}

func (m *SSHConnectionManager) GetConnection(host string) (*ssh.Client, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	conn, exists := m.connections[host]
	now := time.Now()

	// Check if connection exists and is still valid
	if exists && now.Before(conn.ExpiresAt) {
		conn.mu.RLock()
		client := conn.Client
		conn.mu.RUnlock()

		// Test if connection is still alive
		if m.isConnectionAlive(client) {
			// Extend TTL and update last used
			conn.mu.Lock()
			conn.ExpiresAt = now.Add(m.ttl)
			conn.LastUsed = now
			conn.mu.Unlock()

			m.updateConnectionStatus(host, "connected", "", conn.ExpiresAt, now)
			return client, nil
		}

		// Connection is dead, remove it
		conn.mu.Lock()
		if conn.Client != nil {
			conn.Client.Close()
		}
		conn.mu.Unlock()
		delete(m.connections, host)
	}
	log.Println("Creating new connection")
	log.Println("Host: ", m.config(host))
	log.Println("Host: ", host)
	// Create new connection
	client, err := ssh.Dial("tcp", fmt.Sprintf("%s:11027", host), m.config(host))
	if err != nil {
		m.updateConnectionStatus(host, "error", err.Error(), time.Time{}, now)
		return nil, fmt.Errorf("failed to connect to %s: %w", host, err)
	}

	// Cache the new connection
	expiresAt := now.Add(m.ttl)
	m.connections[host] = &SSHConnection{
		Client:    client,
		Host:      host,
		ExpiresAt: expiresAt,
		LastUsed:  now,
	}

	m.updateConnectionStatus(host, "connected", "", expiresAt, now)
	return client, nil
}

func (m *SSHConnectionManager) isConnectionAlive(client *ssh.Client) bool {
	if client == nil {
		return false
	}

	// Send a simple keepalive to test connection
	_, _, err := client.SendRequest("<EMAIL>", true, nil)
	return err == nil
}

func (m *SSHConnectionManager) updateConnectionStatus(host, status, errorMsg string, expiresAt, lastUsed time.Time) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	collection := db.DB.Collection("ssh_connections")

	update := bson.M{
		"$set": bson.M{
			"status":    status,
			"last_used": lastUsed,
			"error":     errorMsg,
		},
		"$setOnInsert": bson.M{
			"host":       host,
			"created_at": time.Now(),
		},
	}

	if !expiresAt.IsZero() {
		update["$set"].(bson.M)["expires_at"] = expiresAt
	}

	opts := options.Update().SetUpsert(true)
	_, err := collection.UpdateOne(ctx, bson.M{"host": host}, update, opts)
	if err != nil {
		// Log error but don't fail the SSH operation
		fmt.Printf("Failed to update SSH connection status: %v\n", err)
	}
}

func (m *SSHConnectionManager) cleanupExpiredConnections() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.mu.Lock()
		now := time.Now()

		for host, conn := range m.connections {
			if now.After(conn.ExpiresAt) {
				conn.mu.Lock()
				if conn.Client != nil {
					conn.Client.Close()
				}
				conn.mu.Unlock()

				delete(m.connections, host)
				m.updateConnectionStatus(host, "disconnected", "expired", time.Time{}, now)
			}
		}
		m.mu.Unlock()
	}
}

func (m *SSHConnectionManager) CloseConnection(host string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if conn, exists := m.connections[host]; exists {
		conn.mu.Lock()
		if conn.Client != nil {
			conn.Client.Close()
		}
		conn.mu.Unlock()

		delete(m.connections, host)
		m.updateConnectionStatus(host, "disconnected", "manually closed", time.Time{}, time.Now())
	}
}

func (m *SSHConnectionManager) CloseAll() {
	m.mu.Lock()
	defer m.mu.Unlock()

	for host, conn := range m.connections {
		conn.mu.Lock()
		if conn.Client != nil {
			conn.Client.Close()
		}
		conn.mu.Unlock()

		m.updateConnectionStatus(host, "disconnected", "shutdown", time.Time{}, time.Now())
	}

	m.connections = make(map[string]*SSHConnection)
}

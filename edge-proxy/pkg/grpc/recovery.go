package grpc

import (
	"context"
	"log"
	"runtime/debug"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// RecoveryInterceptor recovers from panics in unary RPC calls
func RecoveryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Panic recovered in %s: %v\nStack trace:\n%s", info.FullMethod, r, debug.Stack())
			err = status.Errorf(codes.Internal, "Internal server error: %v", r)
		}
	}()

	return handler(ctx, req)
}

// StreamRecoveryInterceptor recovers from panics in streaming RPC calls
func StreamRecoveryInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Panic recovered in stream %s: %v\nStack trace:\n%s", info.FullMethod, r, debug.Stack())
			// For streaming, we can't return an error after panic, so we log it
		}
	}()

	return handler(srv, ss)
}

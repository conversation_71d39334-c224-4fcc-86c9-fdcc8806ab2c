package grpc

import (
	"context"
	"fmt"
	"math"

	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
)

func calculateEpiIpByBoxPortNumber(portNumber int32) string {
	ataNumber := int(math.Ceil(float64(portNumber) / 2))
	epiIp := fmt.Sprintf("************%d", ataNumber)
	return epiIp
}
func calculateEpiPortByBoxPortNumber(portNumber int) int {
	if portNumber%2 == 0 {
		return 2
	}
	return 1
}

func (s *Server) EnableDisablePort(ctx context.Context, req *pb.EnableDisablePortRequest) (*pb.EnableDisablePortResponse, error) {
	result, err := s.services.EPI.EnableDisablePort(ctx, req.SerialNumber, req.Port, req.Enable)
	if err != nil {
		return nil, err
	}
	return &pb.EnableDisablePortResponse{
		Success: result.Success,
	}, nil
}

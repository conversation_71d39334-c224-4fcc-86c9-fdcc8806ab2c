package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
)

// CheckMacRequest represents a request to check if a MAC address exists
type CheckMacRequest struct {
	SerialNumber string `json:"serialNumber"`
	MacAddress   string `json:"macAddress"`
}

// CheckMacResponse represents the response for checking MAC address existence
type CheckMacResponse struct {
	Exists     bool   `json:"exists"`
	MacAddress string `json:"macAddress"`
	IpAddress  string `json:"ipAddress"`
}

// getStringFromMap safely extracts a string value from a nested map structure
func getStringFromMap(data map[string]any, parentKey, childKey string) string {
	if parent, ok := data[parentKey].(map[string]any); ok {
		if value, ok := parent[childKey].(string); ok {
			return value
		}
	}
	return "Data could not be fetched"
}

// Wraps the gRPC server with our service implementation.
func (s *Server) PowerSource(ctx context.Context, req *pb.DeviceRequest) (*pb.PowerSourceResponse, error) {
	type PowerSourceResponse struct {
		PowerSource string `json:"Power Source"`
	}
	var bodyObj PowerSourceResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/getPowerSource", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	return &pb.PowerSourceResponse{
		PowerSource: bodyObj.PowerSource,
	}, nil
}

func (s *Server) ActiveInterface(ctx context.Context, req *pb.DeviceRequest) (*pb.ActiveInterfaceResponse, error) {
	type ActiveInterfaceResponse struct {
		ActiveInterface string `json:"Active Interface"`
	}
	var bodyObj ActiveInterfaceResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/activeInterface", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	return &pb.ActiveInterfaceResponse{
		ActiveInterface: bodyObj.ActiveInterface,
	}, nil
}

func (s *Server) LanIp(ctx context.Context, req *pb.DeviceRequest) (*pb.LanIpResponse, error) {
	type LanIpResponse struct {
		If   string `json:"if"`
		CIDR string `json:"cidr"`
	}
	var bodyObj LanIpResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/net/eth0", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	return &pb.LanIpResponse{
		LanIp: bodyObj.CIDR,
	}, nil
}

func (s *Server) PublicIp(ctx context.Context, req *pb.DeviceRequest) (*pb.PublicIpResponse, error) {
	type PublicIpResponse struct {
		PublicIp string `json:"Public IP"`
		Error    string `json:"error"`
	}
	var bodyObj PublicIpResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/publicip", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.PublicIpResponse{
		PublicIp: bodyObj.PublicIp,
	}, nil
}

func (s *Server) SignalStrength(ctx context.Context, req *pb.DeviceRequest) (*pb.SignalStrengthResponse, error) {
	type SignalStrengthResponse struct {
		SignalStrength string `json:"Signal Strength"`
		Error          string
		Timestamp      string `json:"Time Stamp"`
	}
	var bodyObj SignalStrengthResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/ltesignalstrength", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.SignalStrengthResponse{
		SignalStrength: bodyObj.SignalStrength,
	}, nil
}

func (s *Server) SimStatus(ctx context.Context, req *pb.DeviceRequest) (*pb.SimStatusResponse, error) {
	type SimStatusResponse struct {
		CurrentSim string `json:"Current SIM"`
		Error      string
	}
	var bodyObj SimStatusResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/simStatus", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.SimStatusResponse{
		SimStatus: bodyObj.CurrentSim,
	}, nil
}

func (s *Server) ModemInfo(ctx context.Context, req *pb.DeviceRequest) (*pb.ModemInfoResponse, error) {
	type ModemInfoResponse struct {
		Error                    string `json:"error"`
		Response                 string
		Manufacturer_placeholder string `json:"manufacturer_placeholder"`
		Manufacturer_value       string `json:"manufacturer_value"`
		Model_placeholder        string `json:"model_placeholder"`
		Model_value              string `json:"model_value"`
		Sim_placeholder          string `json:"sim_placeholder"`
		Sim_value                string `json:"sim_value"`
		Imei_placeholder         string `json:"imei_placeholder"`
		Imei_value               string `json:"imei_value"`
		Carrier_placeholder      string `json:"carrier_placeholder"`
		Carrier_value            string `json:"carrier_value"`
		IpAddress_placeholder    string `json:"ipAddress_placeholder"`
		IpAddress_value          string `json:"ipAddress_value"`
	}
	var bodyObj ModemInfoResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/modeminfo", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	if bodyObj.Response != "" {
		return nil, fmt.Errorf(bodyObj.Response)
	}
	log.Printf("bodyObj: %+v", bodyObj)
	log.Println(bodyObj)
	log.Println("bodyObj")
	return &pb.ModemInfoResponse{
		ModemInfo: &pb.ModemInfo{
			ManufacturerPlaceholder: bodyObj.Manufacturer_placeholder,
			Manufacturer:            bodyObj.Manufacturer_value,
			ModelPlaceholder:        bodyObj.Model_placeholder,
			Model:                   bodyObj.Model_value,
			SimPlaceholder:          bodyObj.Sim_placeholder,
			Sim:                     bodyObj.Sim_value,
			ImeiPlaceholder:         bodyObj.Imei_placeholder,
			Imei:                    bodyObj.Imei_value,
			CarrierPlaceholder:      bodyObj.Carrier_placeholder,
			Carrier:                 bodyObj.Carrier_value,
			IpAddressPlaceholder:    bodyObj.IpAddress_placeholder,
			IpAddress:               bodyObj.IpAddress_value,
		},
	}, nil
}

func (s *Server) SensorData(ctx context.Context, req *pb.DeviceRequest) (*pb.SensorDataResponse, error) {
	type SensorDataResponse struct {
		Power string `json:"power"`
		Temp  string `json:"temperature"`
		Error string `json:"error"`
	}
	var bodyObj SensorDataResponse
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/sensorsdata", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.SensorDataResponse{
		Power: bodyObj.Power,
		Temp:  bodyObj.Temp,
	}, nil
}

func (s *Server) DeviceOnline(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceOnlineResponse, error) {
	_, _, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/cpu", "", nil)
	if err != nil {
		return nil, err
	}
	return &pb.DeviceOnlineResponse{
		Online: true,
	}, nil
}

func (s *Server) DcAvgPing(ctx context.Context, req *pb.DeviceRequest) (*pb.DcAvgPingResponse, error) {
	type DcAvgPingBody struct {
		AtPingAvg   string `json:"atPingAvg"`
		BestDC      string `json:"bestDC"`
		BestLatency string `json:"bestLatency"`
		ChPingAvg   string `json:"chPingAvg"`
		DlPingAvg   string `json:"dlPingAvg"`
		LaPingAvg   string `json:"laPingAvg"`
		NyPingAvg   string `json:"nyPingAvg"`
		TimeUpdated string `json:"timeUpdated"`
		Error       string `json:"error"`
	}
	var bodyObj DcAvgPingBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/dcavgping", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.DcAvgPingResponse{
		AtPingAvg:   bodyObj.AtPingAvg,
		BestDC:      bodyObj.BestDC,
		BestLatency: bodyObj.BestLatency,
		ChPingAvg:   bodyObj.ChPingAvg,
		DlPingAvg:   bodyObj.DlPingAvg,
		LaPingAvg:   bodyObj.LaPingAvg,
		NyPingAvg:   bodyObj.NyPingAvg,
		TimeUpdate:  bodyObj.TimeUpdated,
		Error:       bodyObj.Error,
	}, nil
}

func (s *Server) RegStatus(ctx context.Context, req *pb.DeviceRequest) (*pb.RegStatusResponse, error) {
	type RegisteredBody struct {
		Registered string `json:"Epik Box Registered"`
		Error      string `json:"error"`
	}
	var bodyObj RegisteredBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/regstatus", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	registered := false
	if bodyObj.Registered == "Yes" {
		registered = true
	}
	return &pb.RegStatusResponse{
		Registered: registered,
	}, nil
}

// ---------- Structs ----------
type OBiModel struct {
	RebootReq string `json:"-reboot_req"`
	Objects   []struct {
		Name       string `json:"-name"`
		Parameters []struct {
			Name  string `json:"-name"`
			Value struct {
				Current string `json:"-current"`
			} `json:"value"`
		} `json:"parameter"`
	} `json:"object"`
}

func parseOBiStatus(inputJSON map[string]any) (*pb.PortRegStatusResponse, error) {
	var model struct {
		Model OBiModel `json:"model"`
	}
	var output *pb.PortRegStatusResponse

	// Convert map to JSON bytes for unmarshaling
	inputJSONBytes, err := json.Marshal(inputJSON)
	if err != nil {
		return output, fmt.Errorf("failed to marshal input JSON: %v", err)
	}

	// Unmarshal input JSON
	if err := json.Unmarshal(inputJSONBytes, &model); err != nil {
		return output, fmt.Errorf("failed to unmarshal input JSON: %v", err)
	}

	// Initialize output with placeholders
	output = &pb.PortRegStatusResponse{
		WanInfo: &pb.WanInfo{
			PlaceHolder: "EPI WAN INFO",
		},
		Sp1ServiceStatus: &pb.Sp1ServiceStatus{},
		Sp2ServiceStatus: &pb.Sp2ServiceStatus{},
		ObiTalkServiceStatus: &pb.ObiTalkServiceStatus{
			PlaceHolder: "EPI connection status",
		},
	}

	// Process each object in the input
	for _, obj := range model.Model.Objects {
		switch obj.Name {
		case "WAN Status":
			for _, param := range obj.Parameters {
				switch param.Name {
				case "IPAddress":
					output.WanInfo.Ip = param.Value.Current
				case "SubnetMask":
					output.WanInfo.Subnet = param.Value.Current
				case "DefaultGateway":
					output.WanInfo.Gateway = param.Value.Current
				case "DNSServer1":
					output.WanInfo.Dns = param.Value.Current
				}
			}

		case "SP1 Service Status":
			for _, param := range obj.Parameters {
				switch param.Name {
				case "Status":
					output.Sp1ServiceStatus.Status = param.Value.Current
				case "CallState":
					output.Sp1ServiceStatus.CallState = param.Value.Current
				}
			}

		case "SP2 Service Status":
			for _, param := range obj.Parameters {
				switch param.Name {
				case "Status":
					output.Sp2ServiceStatus.Status = param.Value.Current
				case "CallState":
					output.Sp2ServiceStatus.CallState = param.Value.Current
				}
			}

		case "OBiTALK Service Status":
			for _, param := range obj.Parameters {
				if param.Name == "Status" {
					output.ObiTalkServiceStatus.Status = param.Value.Current
				}
			}
		}
	}

	return output, nil
}

func parseEsceneStatus(bodyObj map[string]any) (*pb.PortRegStatusResponse, error) {

	return &pb.PortRegStatusResponse{
		WanInfo: &pb.WanInfo{
			PlaceHolder: "EPI WAN INFO",
			Ip:          getStringFromMap(bodyObj, "wanInfo", "wanIpAddress"),
			Subnet:      getStringFromMap(bodyObj, "wanInfo", "wanSubnetMask"),
			Gateway:     getStringFromMap(bodyObj, "wanInfo", "wanDefaultGateway"),
			Dns:         getStringFromMap(bodyObj, "wanInfo", "wanDnsServer"),
		},
		Sp1ServiceStatus: &pb.Sp1ServiceStatus{
			Status:    getStringFromMap(bodyObj, "regInfo", "registrationStatusPort1"),
			CallState: "Data could not be fetched",
		},
		Sp2ServiceStatus: &pb.Sp2ServiceStatus{
			Status:    getStringFromMap(bodyObj, "regInfo", "registrationStatusPort2"),
			CallState: "Data could not be fetched",
		},
		ObiTalkServiceStatus: &pb.ObiTalkServiceStatus{
			PlaceHolder: "EPI connection status",
			Status:      getStringFromMap(bodyObj, "epiConn", "connectionStatusPort1"),
		},
	}, nil
}

func (s *Server) PortRegStatus(ctx context.Context, req *pb.EpiRequest) (*pb.PortRegStatusResponse, error) {
	var bodyObj map[string]any
	// Create the payload for POST request using MAC from request
	payload := map[string]string{
		"mac": req.MacAddress,
		// "mac": "00268B0DA31D",
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %v", err)
	}
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/epi/registration/", "POST", jsonPayload)
	if err != nil {
		log.Printf(" failed: %v", err)
		return nil, err
	}

	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	// Check for error in response
	if errorVal, exists := bodyObj["error"]; exists && errorVal != "" {
		return nil, fmt.Errorf("%v", errorVal)
	}

	// Check if MAC address starts with "002"
	var response *pb.PortRegStatusResponse
	if strings.HasPrefix(req.MacAddress, "002") {
		// Build the response with default values
		response, err = parseEsceneStatus(bodyObj)
		if err != nil {
			log.Fatalf("Error marshaling output: %v", err)
		}
	} else {
		output, err := parseOBiStatus(bodyObj)
		if err != nil {
			log.Fatalf("Error parsing OBi status: %v", err)
		}
		// Marshal the output to JSON
		if err != nil {
			log.Fatalf("Error marshaling output: %v", err)
		}
		response = output
	}
	return response, nil
}

func (s *Server) LiveEpis(ctx context.Context, req *pb.DeviceRequest) (*pb.LiveEpisResponse, error) {
	// log.Println("LiveEpis return ", req.SerialNumber)

	type LiveEpisBody struct {
		Epis map[string]string `json:"EPIs"`
	}

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/liveepis", "", nil)
	if err != nil {
		return nil, err
	}
	// Process the hardcoded JSON body instead of making API call
	// jsonBody := `{
	// 	"EPIs": {
	// 		"*************": "9c:ad:ef:46:cc:e7",
	// 		"*************": "9c:ad:ef:46:c8:85"
	// 	}
	// }`
	var bodyObj LiveEpisBody

	err = json.Unmarshal([]byte(body), &bodyObj)
	if err != nil {
		log.Println("Error parsing JSON:", err)
		return nil, err
	}

	// Now assign the parsed EPIs directly to the response
	liveEpisResp := &pb.LiveEpisResponse{
		Epis: bodyObj.Epis,
	}

	return liveEpisResp, nil
}

func (s *Server) CheckMacExists(ctx context.Context, req *CheckMacRequest) (*CheckMacResponse, error) {
	// First get the LiveEpis data
	deviceReq := &pb.DeviceRequest{
		SerialNumber: req.SerialNumber,
	}

	liveEpisResp, err := s.LiveEpis(ctx, deviceReq)
	if err != nil {
		return nil, fmt.Errorf("failed to get LiveEpis data: %v", err)
	}

	// Check if the target MAC exists in the response
	// targetMac := "9CADEF46CCE7" // example input
	targetMac := req.MacAddress
	var targetIp string
	exists := false

	for ip, mac := range liveEpisResp.Epis {
		// Remove colons and convert to uppercase for comparison
		normalizedMac := strings.ToUpper(strings.ReplaceAll(mac, ":", ""))

		if normalizedMac == targetMac {
			targetIp = ip
			exists = true
			break
		}
	}

	return &CheckMacResponse{
		Exists:     exists,
		MacAddress: targetMac,
		IpAddress:  targetIp,
	}, nil
}

func (s *Server) PortPhysicalStatus(ctx context.Context, req *pb.EpiRequest) (*pb.PortStatusResponse, error) {
	type PortStatus struct {
		Name           string `json:"Name"`
		State          string `json:"State"`
		LoopCurrent    string `json:"LoopCurrent"`
		Vbat           string `json:"Vbat"`
		TipRingVoltage string `json:"TipRingVoltage"`
		LastCallerInfo string `json:"LastCallerInfo"`
	}

	var portStatusResponse *pb.PortStatusResponse
	var portStatusArr []*pb.PortStatus

	// Call CheckMacExists function
	checkMacReq := &CheckMacRequest{
		SerialNumber: req.SerialNumber,
		MacAddress:   req.MacAddress, // Example MAC address
		// MacAddress:   "9c:ad:ef:46:cc:e7", // Example MAC address
	}
	checkMacResp, err := s.CheckMacExists(ctx, checkMacReq)
	if err != nil {
		log.Printf("CheckMacExists failed: %v", err)
		return portStatusResponse, nil
	} else {
		log.Printf("MAC exists: %v, MAC: %s, IP: %s", checkMacResp.Exists, checkMacResp.MacAddress, checkMacResp.IpAddress)
	}

	restbinEndpoint := checkMacResp.IpAddress

	url := fmt.Sprintf("/v2/epi/%s/physicalstatus", restbinEndpoint)
	log.Println("physicalstatus URL ", url)

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, url, "", nil)
	if err != nil {
		log.Printf("PortPhysicalStatus failed: ")
		return nil, err
	}

	// First decode the outer string to get the actual JSON
	var innerJSON string
	err = json.Unmarshal([]byte(body), &innerJSON)
	if err != nil {
		log.Fatalf("Error decoding outer string: %v", err)
	}

	// Decode the actual JSON array into Go struct
	var portStatuses []PortStatus
	err = json.Unmarshal([]byte(innerJSON), &portStatuses)
	if err != nil {
		log.Fatalf("Error decoding JSON array: %v", err)
	}

	for _, port := range portStatuses {
		portStatus := &pb.PortStatus{
			Name:           port.Name,
			State:          port.State,
			LoopCurrent:    port.LoopCurrent,
			Vbat:           port.Vbat,
			TipRingVoltage: port.TipRingVoltage,
			LastCallerInfo: port.LastCallerInfo,
		}
		portStatusArr = append(portStatusArr, portStatus)
	}

	portStatusResponse = &pb.PortStatusResponse{
		PortStatus: portStatusArr,
	}
	return portStatusResponse, nil
}

func (s *Server) WifiStatus(ctx context.Context, req *pb.DeviceRequest) (*pb.WifiStatusResponse, error) {
	type WifiStatusBody struct {
		Error     string `json:"error"`
		Error_msg string `json:"Error_Msg"`
		Gateway   string `json:"Gateway"`
		Ip        string `json:"IP"`
		Mode      string `json:"Mode"`
		Password  string `json:"Password"`
		SSID      string `json:"SSID"`
		Sec_mode  string `json:"Sec_Mode"`
		Status    string `json:"Status"`
		Subnet    string `json:"Subnet"`
	}

	var bodyObj WifiStatusBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/checkwifistatus", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}
	return &pb.WifiStatusResponse{
		ErrorMsg: bodyObj.Error_msg,
		Gateway:  bodyObj.Gateway,
		Ip:       bodyObj.Ip,
		Mode:     bodyObj.Mode,
		Password: bodyObj.Password,
		SSID:     bodyObj.SSID,
		SecMode:  bodyObj.Sec_mode,
		Status:   bodyObj.Status,
		Subnet:   bodyObj.Subnet,
	}, nil
}

func (s *Server) NetworkInfo(ctx context.Context, req *pb.DeviceRequest) (*pb.NetworkInfoResponse, error) {
	type NetworkInterface struct {
		Interface string `json:"interface"`
		Internet  string `json:"internet"`
		Icmp      string `json:"icmp"`
		Wg        string `json:"wg"`
	}

	type NetworkInfoBody struct {
		Dns        string             `json:"dns"`
		Error      string             `json:"error"`
		Interfaces []NetworkInterface `json:"interfaces"`
		Timestamp  string             `json:"timestamp"`
	}

	var bodyObj NetworkInfoBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/fetchconnectivitystats", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}
	if bodyObj.Error != "" {
		return nil, fmt.Errorf(bodyObj.Error)
	}

	// Convert []NetworkInterface to []*edgev1.NetworkInterfaceObj
	var pbInterfaces []*pb.NetworkInterfaceObj
	for _, iface := range bodyObj.Interfaces {
		pbInterfaces = append(pbInterfaces, &pb.NetworkInterfaceObj{
			Interface: iface.Interface,
			Internet:  iface.Internet,
			Icmp:      iface.Icmp,
			Wg:        iface.Wg,
		})
	}
	return &pb.NetworkInfoResponse{
		Dns:        bodyObj.Dns,
		Interfaces: pbInterfaces,
		Timestamp:  bodyObj.Timestamp,
	}, nil
}

func (s *Server) DeviceNightlyUpdateTime(ctx context.Context, req *pb.DeviceRequest) (*pb.DeviceNightlyUpdateTimeResponse, error) {
	type DeviceUpdateBody struct {
		Response string `json:"response"`
	}

	var bodyObj DeviceUpdateBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/deviceupdatetime", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	return &pb.DeviceNightlyUpdateTimeResponse{
		DeviceUpdateTime: bodyObj.Response,
	}, nil
}

func (s *Server) PortForwardList(ctx context.Context, req *pb.DeviceRequest) (*pb.PortForwardListResponse, error) {

	var portForwardObjs []struct {
		SrcIP        string `json:"srcIP"`
		SrcStartPort int32  `json:"srcStartPort"`
		SrcEndPort   int32  `json:"srcEndPort"`
		DstIP        string `json:"dstIP"`
		DstStartPort int32  `json:"dstStartPort"`
		DstEndPort   int32  `json:"dstEndPort"`
		Proto        string `json:"proto"`
	}

	// Call the proxy to fetch the port forward list
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/portForwardList", "", nil)
	if err != nil {
		return nil, err
	}
	// fmt.Println("body:", string(body))
	err = json.Unmarshal(body, &portForwardObjs)
	if err != nil {
		return nil, err
	}

	var pbPortForwardList []*pb.PortForwardObj
	for _, pf := range portForwardObjs {
		pbPortForwardList = append(pbPortForwardList, &pb.PortForwardObj{
			SrcIP:        pf.SrcIP,
			SrcStartPort: pf.SrcStartPort,
			SrcEndPort:   pf.SrcEndPort,
			DstIP:        pf.DstIP,
			DstStartPort: pf.DstStartPort,
			DstEndPort:   pf.DstEndPort,
			Proto:        pf.Proto,
		})
	}
	// fmt.Printf("DEBUG: pbPortForwardList after conversion: %#v\n", pbPortForwardList)
	return &pb.PortForwardListResponse{
		PortForwardList: pbPortForwardList,
	}, nil

}

func (s *Server) PriorityInterface(ctx context.Context, req *pb.DeviceRequest) (*pb.PriorityInterfaceResponse, error) {
	type ResponseBody struct {
		Interfaces string `json:"interfaces"`
	}

	var bodyObj ResponseBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/v2/isppriority", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse isppriority response: %w", err)
	}
	log.Println("PriorityInterface ", bodyObj.Interfaces)

	return &pb.PriorityInterfaceResponse{
		PriorityInterface: bodyObj.Interfaces,
	}, nil
}

func (s *Server) PrimarySim(ctx context.Context, req *pb.DeviceRequest) (*pb.PrimarySimResponse, error) {
	// log.Println("PrimarySim ")
	type ResponseBody struct {
		PrimarySim string `json:"Primary SIM"`
	}

	var bodyObj ResponseBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/getPrimarySim", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse isppriority response: %w", err)
	}

	return &pb.PrimarySimResponse{
		PrimarySim: bodyObj.PrimarySim,
	}, nil
}

func (s *Server) CurrentApn(ctx context.Context, req *pb.DeviceRequest) (*pb.CurrentApnResponse, error) {
	// log.Println("CurrentApn ")
	type ResponseBody struct {
		Apn string `json:"apn"`
	}

	var bodyObj ResponseBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/v2/getAPN", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse CurrentApn response: %w", err)
	}

	return &pb.CurrentApnResponse{
		Apn: bodyObj.Apn,
	}, nil
}

func (s *Server) EpikUpdateStatus(ctx context.Context, req *pb.DeviceRequest) (*pb.EpikUpdateStatusResponse, error) {
	// log.Println("EpikUpdateStatus ")
	type ResponseBody struct {
		Response string `json:"response"`
	}

	var bodyObj ResponseBody
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/nightlyupdatestatus", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse EpikUpdateStatus response: %w", err)
	}

	return &pb.EpikUpdateStatusResponse{
		Status: bodyObj.Response == "enabled",
	}, nil
}

type CdmInfo struct {
	Status       string
	LastCdmCheck string
	Duration     string
}

func getBoxCdmStatus(serialNumber string) (*CdmInfo, error) {
	// log.Println("getBoxCdmStatus", serialNumber)
	cdmURL := "https://cdm.epikadmin.com/req_boxStatus"
	form := make(url.Values)
	form.Set("sn", serialNumber)

	httpReq, err := http.NewRequest("POST", cdmURL, strings.NewReader(form.Encode()))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var cdmInfo CdmInfo
	responseStr := string(body)
	if strings.Contains(responseStr, "failure") {
		log.Println("No CDM info received from https://cdm.epikadmin.com/req_boxStatus API")
		return nil, nil
	}

	parts := strings.Split(responseStr, ",")
	if len(parts) < 3 {
		log.Println("Invalid response format:", responseStr)
		return nil, nil
	}
	status := strings.TrimSpace(parts[0])
	secondsStr := strings.TrimSpace(parts[1])
	lastCdmChecked := strings.TrimSpace(parts[2])

	cdmInfo.Status = status
	cdmInfo.LastCdmCheck = lastCdmChecked

	if secondsStr != "err" {
		seconds, err := strconv.Atoi(secondsStr)
		if err == nil {
			hours := seconds / 3600
			minutes := (seconds % 3600) / 60
			remainingSeconds := seconds % 60
			cdmInfo.Duration = fmt.Sprintf("%d:%02d:%02d", hours, minutes, remainingSeconds)
		}
	}
	log.Println("getBoxCdmStatus", cdmInfo)
	return &cdmInfo, nil
}
func (s *Server) SystemInfo(ctx context.Context, req *pb.DeviceRequest) (*pb.SystemInfoResponse, error) {
	// log.Println("SystemInfo ")
	type SystemInfoRaw map[string]any
	var raw SystemInfoRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/sysinfotablastlog", "", nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, &raw); err != nil {
		return nil, fmt.Errorf("failed to parse SystemInfo response: %w", err)
	}

	// Compose the output map[string]string for pb.SystemInfoResponse.Fields
	result := make(map[string]string)

	// Helper to marshal sub-objects as JSON strings
	marshal := func(v any) string {
		b, _ := json.Marshal(v)
		return string(b)
	}

	// Top-level string fields
	// Add all these keys and data in NetworkInfoDetail Object
	networkInfoDetail := make(map[string]any)
	// List of keys to include in NetworkInfoDetail
	for _, k := range []string{
		"arp",
		"eth0Config",
		"eth0Stats",
		"4GIP",
		"linkStatus",
		"vpnIP",
		"icmp",
		"publicIp",
		"modemCount",
		"priCardStatus",
		"fwVersion",
		"activeInterface",
		"registrationStatus",
		"4GIP",
		"wgAccess",
		"powerSource",
		"epis",
		"lteSgnalStrength",
		"dcAvgPing",
	} {
		if v, ok := raw[k]; ok {
			if k == "4GIP" {
				networkInfoDetail["ip4G"] = v
			} else if k == "vpnIP" {
				networkInfoDetail["tunnelIP"] = v
			} else if k == "publicIp" {
				networkInfoDetail["edgeIP"] = v
			} else if k == "wgAccess" {
				networkInfoDetail["tunnelAccess"] = v
			} else if k == "linkStatus" {
				// Example: {"EPI Link": "UP", "Eth0/2": "DOWN"} -> {"epiLink": "UP", "eth02": "DOWN"}
				if m, ok := v.(map[string]any); ok {
					camel := make(map[string]any)
					for key, val := range m {
						switch key {
						case "EPI Link":
							camel["epiLink"] = val
						case "Eth0/2":
							camel["eth02"] = val
						default:
							camel[key] = val
						}
					}
					networkInfoDetail["linkStatus"] = camel
				} else {
					networkInfoDetail["linkStatus"] = v
				}

			} else if k == "epis" {
				networkInfoDetail["epiInfo"] = v
			} else {
				networkInfoDetail[k] = v
			}
		}
	}

	// get Box CDM Status
	cdmInfo, err := getBoxCdmStatus(req.SerialNumber)
	if err != nil {
		log.Printf("getBoxCdmStatus error: %v", err)
		// Do not include cdmBoxStatus if error, continue returning sysinfo without it
	}
	if cdmInfo != nil {
		networkInfoDetail["cdmBoxStatus"] = cdmInfo
	}

	if len(networkInfoDetail) > 0 {
		result["NetworkInfoDetail"] = marshal(networkInfoDetail)
	}
	for _, k := range []string{
		"cdmStatus", "deviceFw", "error", "failoverStatus", "failoverUpdateStatus",
		"restbinStatus", "restbinVersion", "time", "watchGuardStatus",
	} {
		if v, ok := raw[k]; ok {
			if str, ok := v.(string); ok {
				result[k] = str
			}
		}
	}

	// diskIo: array of objects
	if v, ok := raw["diskIo"]; ok {
		result["diskIo"] = marshal(v)
	}
	// diskUsage: object
	if v, ok := raw["diskUsage"]; ok {
		result["diskUsage"] = marshal(v)
	}
	// loadavg: object
	if v, ok := raw["loadavg"]; ok {
		result["loadavg"] = marshal(v)
	}
	// uptime: string or object
	if v, ok := raw["uptime"]; ok {
		switch t := v.(type) {
		case string:
			result["uptime"] = marshal(map[string]string{"uptime": t})
		case map[string]any:
			result["uptime"] = marshal(t)
		}
	}
	// wifiInfo: object or string
	if v, ok := raw["wifiInfo"]; ok {
		result["wifiInfo"] = marshal(v)
	}
	// boxFirmware: object
	if v, ok := raw["boxFirmware"]; ok {
		result["boxFirmware"] = marshal(v)
	}
	// lteSignalStrength: object
	if v, ok := raw["lteSignalStrength"]; ok {
		result["lteSignalStrength"] = marshal(v)
	}
	// networkInfo: object
	if v, ok := raw["networkInfo"]; ok {
		result["networkInfo"] = marshal(v)
	}
	// cpu: object
	if v, ok := raw["cpuStats"]; ok {
		result["cpu"] = marshal(v)
	}
	// memory: object
	if v, ok := raw["memoryStats"]; ok {
		result["memory"] = marshal(v)
	}
	// network: array of objects
	if v, ok := raw["networkStats"]; ok {
		result["network"] = marshal(v)
	}
	// portStatuses: object
	if v, ok := raw["portStatus"]; ok {
		result["portStatuses"] = marshal(v)
	}

	// log.Println("SystemInfo result", result)
	return &pb.SystemInfoResponse{
		SysInfo: result,
	}, nil
}

func (s *Server) DCConnectionStats(ctx context.Context, req *pb.DeviceRequest) (*pb.DCConnectionStatsResponse, error) {
	// log.Println("DCConnectionStats ")
	type DCConnectionStatsRaw map[string]any
	var raw DCConnectionStatsRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/fetchepikdcconnstats", "", nil)
	if err != nil {
		return nil, err
	}
	// Check if body contains an "error" field and handle it
	var errCheck map[string]any
	if err := json.Unmarshal(body, &errCheck); err == nil {
		if errMsg, ok := errCheck["error"]; ok {
			return nil, fmt.Errorf("DCConnectionStats error: %v", errMsg)
		}
	}
	if err := json.Unmarshal(body, &raw); err != nil {
		return nil, fmt.Errorf("failed to parse DCConnectionStats response: %w", err)
	}

	result := make(map[string]string)
	for k, v := range raw {
		if k == "Timestamp" {
			// Timestamp is a string, store as-is
			if ts, ok := v.(string); ok {
				result["Timestamp"] = ts
			}
			continue
		}
		b, _ := json.Marshal(v)
		result[k] = string(b)
	}

	log.Println("DCConnectionStats result", result)
	return &pb.DCConnectionStatsResponse{
		DcConnectionStats: result,
	}, nil

}
func (s *Server) DnsCheck(ctx context.Context, req *pb.DeviceRequest) (*pb.DnsCheckResponse, error) {
	log.Println("DnsCheck ")
	type DnsCheckRaw struct {
		DNS   bool   `json:"response"`
		Error string `json:"error"`
	}
	var bodyObj DnsCheckRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/dnscheck", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	log.Println("DnsCheck result", bodyObj.DNS)
	return &pb.DnsCheckResponse{
		Dns: bodyObj.DNS,
	}, nil

}

func (s *Server) VSwitchTab(ctx context.Context, req *pb.DeviceRequest) (*pb.VSwitchTabResponse, error) {
	// log.Println("VSwitchTab called")
	type PortInfoRaw struct {
		Port      string `json:"port"`
		CalledId  string `json:"calledId"`
		Recording string `json:"recording"`
		TrunkType string `json:"trunkType"`
	}
	type VSwitchTabRaw struct {
		Registered                 bool          `json:"registered"`
		RegisterationConfigCreated bool          `json:"registerationConfigCreated"`
		PortsInfo                  []PortInfoRaw `json:"portsInfo"`
		PortsConfigCreated         []string      `json:"portsConfigCreated"`
	}

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/vswitchtab", "", nil)
	if err != nil {
		return nil, err
	}
	var bodyObj VSwitchTabRaw
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, fmt.Errorf("failed to parse VSwitchTab response: %w", err)
	}

	// Map portsInfo to []*pb.PortInfo
	var portsInfo []*pb.PortInfo
	for _, p := range bodyObj.PortsInfo {
		portsInfo = append(portsInfo, &pb.PortInfo{
			Port:      p.Port,
			CalledId:  p.CalledId,
			Recording: p.Recording,
			TrunkType: p.TrunkType,
		})
	}

	return &pb.VSwitchTabResponse{
		Registered:                 bodyObj.Registered,
		RegisterationConfigCreated: bodyObj.RegisterationConfigCreated,
		PortsInfo:                  portsInfo,
		PortsConfigCreated:         bodyObj.PortsConfigCreated,
	}, nil

}

func (s *Server) LteAnalyzer(ctx context.Context, req *pb.DeviceRequest) (*pb.LteAnalyzerResponse, error) {
	log.Println("LteAnalyzer ")
	type LteAnalyzerRaw struct {
		Busy bool `json:"busy"`
	}
	var bodyObj LteAnalyzerRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/lteanalyzer", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	log.Println("LteAnalyzer result", bodyObj.Busy)
	return &pb.LteAnalyzerResponse{
		Busy: bodyObj.Busy,
	}, nil

}
func (s *Server) SpeedTest(ctx context.Context, req *pb.DeviceRequest) (*pb.SpeedTestResponse, error) {
	// Create a longer timeout context specifically for speedtest operations
	// Speedtest can take up to 30 seconds, so we need a longer timeout
	speedtestCtx, cancel := context.WithTimeout(ctx, 90*time.Second)
	defer cancel()

	type SpeedTestRaw struct {
		DownloadSpeed string `json:"DownloadSpeed"`
		UploadSpeed   string `json:"UploadSpeed"`
		Latency       string `json:"Latency"`
		Jitter        string `json:"Jitter"`
	}
	var bodyObj SpeedTestRaw

	var protocol string
	if req.Payload == "voice" {
		protocol = "udp"
	} else {
		protocol = "tcp"
	}

	payload := map[string]string{
		"protocol": protocol,
		"port":     "5151",
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %v", err)
	}

	// Make the proxy request
	_, body, _, err := s.proxyServer.ProxyRequest(speedtestCtx, req.SerialNumber, "/speedtest", "POST", jsonPayload)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	return &pb.SpeedTestResponse{
		DownloadSpeed: bodyObj.DownloadSpeed,
		UploadSpeed:   bodyObj.UploadSpeed,
		Latency:       bodyObj.Latency,
		Jitter:        bodyObj.Jitter,
	}, nil

}

func (s *Server) InitLtePerf(ctx context.Context, req *pb.DeviceRequest) (*pb.InitLtePerfResponse, error) {
	// log.Println("InitLtePerf ")
	type InitLtePerfRaw struct {
		Response string `json:"response"`
	}
	var bodyObj InitLtePerfRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/initlteperfcheck", "", nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	return &pb.InitLtePerfResponse{
		Initiated: bodyObj.Response,
	}, nil
}

func (s *Server) FetchLtePerf(ctx context.Context, req *pb.DeviceRequest) (*pb.FetchLtePerfResponse, error) {
	// log.Println("FetchLtePerf ")

	type SimPingInfoRaw struct {
		Error      string  `json:"Error"`
		Jitter     float64 `json:"Jitter"`
		PacketLoss float64 `json:"PacketLoss"`
		PingAvg    float64 `json:"PingAvg"`
		SIM        int32   `json:"SIM"`
	}
	type FetchLtePerfRaw struct {
		SimsInfo  []SimPingInfoRaw `json:"SimsInfo"`
		TimeStamp string           `json:"TimeStamp"`
	}
	var bodyObj FetchLtePerfRaw

	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, "/fetchlteperf", "", nil)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &bodyObj)
	if err != nil {
		return nil, err
	}

	// Convert []SimPingInfoRaw to []*pb.SimPingInfo
	var simsInfo []*pb.SimPingInfo
	for _, sim := range bodyObj.SimsInfo {
		simsInfo = append(simsInfo, &pb.SimPingInfo{
			Error:      sim.Error,
			Jitter:     sim.Jitter,
			PacketLoss: sim.PacketLoss,
			PingAvg:    sim.PingAvg,
			SIM:        sim.SIM,
		})
	}

	return &pb.FetchLtePerfResponse{
		SimsInfo:  simsInfo,
		TimeStamp: bodyObj.TimeStamp,
	}, nil
}
func (s *Server) PortConfigValues(ctx context.Context, req *pb.EpiRequest) (*pb.PortConfigValuesResponse, error) {
	// log.Println("PortConfigValues ")

	type PortConfigValuesRaw struct {
		OnhookVolts          string `json:"OnhookVolts"`
		OffhookCurrent       string `json:"OffhookCurrent"`
		DtmfDetectLength     string `json:"DtmfDetectLength"`
		DtmfDetectGap        string `json:"DtmfDetectGap"`
		TxGain               string `json:"TxGain"`
		RxGain               string `json:"RxGain"`
		DtmfMethod           string `json:"DtmfMethod"`
		DtmfPlaybackLevel    string `json:"DtmfPlaybackLevel"`
		RingVoltage          string `json:"RingVoltage"`
		DigitMapShortTimer   string `json:"DigitMapShortTimer"`
		CpcDuration          string `json:"CpcDuration"`
		CpcDelayTime         string `json:"CpcDelayTime"`
		JitterBufferType     string `json:"JitterBufferType"`
		JitterBufferMinDeley string `json:"JitterBufferMinDeley"`
		JitterBufferMaxDeley string `json:"JitterBufferMaxDeley"`
		T38Enabled           string `json:"T38Enabled"`
		ModemMode            string `json:"ModemMode"`
		VadEnable            string `json:"VadEnable"`
		ThreeWayCalling      string `json:"ThreeWayCalling"`
	}

	type SilenceDetectSensitivityRaw struct {
		SilenceDetectSensitivity string `json:"silenceDetectSensitivity"`
	}
	port := req.Port

	// Call CheckMacExists function
	checkMacReq := &CheckMacRequest{
		SerialNumber: req.SerialNumber,
		MacAddress:   req.MacAddress, // Example MAC address
		// MacAddress:   "9c:ad:ef:46:cc:e7", // Example MAC address
	}
	checkMacResp, err := s.CheckMacExists(ctx, checkMacReq)
	if err != nil {
		log.Printf("CheckMacExists failed: %v", err)
		return nil, err
	} else {
		log.Printf("MAC exists: %v, MAC: %s, IP: %s", checkMacResp.Exists, checkMacResp.MacAddress, checkMacResp.IpAddress)
	}

	restbinEndpoint := checkMacResp.IpAddress
	if restbinEndpoint == "" {
		return nil, fmt.Errorf("restbin endpoint not found")
	}
	requestUrl := fmt.Sprintf("/epi/%s/%s/portConfig", restbinEndpoint, port)
	log.Println("PortConfigValues URL ", requestUrl)

	var bodyObj PortConfigValuesRaw
	_, body, _, err := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, requestUrl, "", nil)
	if err != nil {
		return nil, err
	}

	// Step 1: API returns a JSON string wrapped in quotes
	var outer string
	if err := json.Unmarshal(body, &outer); err != nil {
		return nil, err
	}

	// Step 2: Now unmarshal the inner JSON string into your struct
	// var bodyObj PortConfigValuesRaw
	if err := json.Unmarshal([]byte(outer), &bodyObj); err != nil {
		return nil, err
	}

	var resObj SilenceDetectSensitivityRaw
	requestUrl = fmt.Sprintf("/epi/%s/%s/fetchsds", restbinEndpoint, port)
	_, body1, _, err1 := s.proxyServer.ProxyRequest(ctx, req.SerialNumber, requestUrl, "", nil)
	if err1 != nil {
		resObj.SilenceDetectSensitivity = "Unable to fetch Silence Detect Sensivity"
	} else {
		// Step 1: API returns a JSON string wrapped in quotes
		if err := json.Unmarshal(body1, &resObj); err != nil {
			resObj.SilenceDetectSensitivity = "Unable to fetch Silence Detect Sensivity"
		}
	}

	log.Println("SilenceDetectSensitivityRaw response", resObj.SilenceDetectSensitivity)

	return &pb.PortConfigValuesResponse{
		OnhookVolts:              bodyObj.OnhookVolts,
		OffhookCurrent:           bodyObj.OffhookCurrent,
		DtmfDetectLength:         bodyObj.DtmfDetectLength,
		DtmfDetectGap:            bodyObj.DtmfDetectGap,
		TxGain:                   bodyObj.TxGain,
		RxGain:                   bodyObj.RxGain,
		DtmfMethod:               bodyObj.DtmfMethod,
		DtmfPlaybackLevel:        bodyObj.DtmfPlaybackLevel,
		RingVoltage:              bodyObj.RingVoltage,
		DigitMapShortTimer:       bodyObj.DigitMapShortTimer,
		CpcDuration:              bodyObj.CpcDuration,
		CpcDelayTime:             bodyObj.CpcDelayTime,
		JitterBufferType:         bodyObj.JitterBufferType,
		JitterBufferMinDeley:     bodyObj.JitterBufferMinDeley,
		JitterBufferMaxDeley:     bodyObj.JitterBufferMaxDeley,
		T38Enabled:               bodyObj.T38Enabled,
		ModemMode:                bodyObj.ModemMode,
		VadEnable:                bodyObj.VadEnable,
		ThreeWayCalling:          bodyObj.ThreeWayCalling,
		SilenceDetectSensitivity: resObj.SilenceDetectSensitivity,
	}, nil
}

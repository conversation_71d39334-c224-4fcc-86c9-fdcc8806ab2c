package grpc

import (
	"bufio"
	"context"
	"io"
	"log"
	"os/exec"
	"sync"

	pb "github.com/EPIKio/myepikV2/edge/proto/edge/v1"
	"google.golang.org/grpc"
)

func (s *Server) PingBox(stream grpc.BidiStreamingServer[pb.PingMessage, pb.PingMessage]) error {
	var (
		cmd        *exec.Cmd
		stdout     io.ReadCloser
		cancel     context.CancelFunc
		readerDone = make(chan struct{})
		readerOnce sync.Once
		running    bool
		mu         sync.Mutex
	)

	stopPing := func() {
		mu.Lock()
		defer mu.Unlock()
		if cancel != nil {
			cancel()
		}
		if cmd != nil {
			<-readerDone
			_ = cmd.Wait()
			cmd = nil
			cancel = nil
			running = false
		}
	}

	defer stopPing()

	for {
		in := new(pb.PingMessage)
		if err := stream.RecvMsg(in); err == io.EOF {
			break
		} else if err != nil {
			log.Println("Recv error:", err)
			break
		}

		switch payload := in.Payload.(type) {
		case *pb.PingMessage_Command:
			cmdMsg := payload.Command

			if cmdMsg.Stop {
				log.Println("Received stop command")
				stopPing()
				continue
			}

			if cmdMsg.SerialNumber != "" && !running {
				log.Println("Starting ping for:", cmdMsg.SerialNumber)

				ctx, c := context.WithCancel(context.Background())
				cancel = c

				var vpnAddr string
				var err error

				if cmdMsg.Ip != "" {
					vpnAddr = cmdMsg.Ip
				} else {
					vpnAddr, err = s.proxyServer.GetBoxVpnAddress(ctx, cmdMsg.SerialNumber)
					if err != nil {
						log.Println("Failed to get VPN address:", err)
						return err
					}
				}
				log.Println("VPN address:", vpnAddr)

				cmd = exec.CommandContext(ctx, "ping", vpnAddr)
				stdout, err = cmd.StdoutPipe()
				if err != nil {
					return err
				}
				if err := cmd.Start(); err != nil {
					return err
				}

				running = true

				go func() {
					defer func() {
						readerOnce.Do(func() { close(readerDone) })
					}()

					scanner := bufio.NewScanner(stdout)
					for scanner.Scan() {
						line := scanner.Text()
						log.Println("Sending:", line)
						_ = stream.SendMsg(&pb.PingMessage{
							Payload: &pb.PingMessage_Line{
								Line: &pb.PingLine{Line: line},
							},
						})
					}

					if err := scanner.Err(); err != nil {
						log.Println("Scanner error:", err)
					}

					// notify completion
					_ = stream.SendMsg(&pb.PingMessage{
						Payload: &pb.PingMessage_Line{
							Line: &pb.PingLine{Completed: true},
						},
					})
				}()
			}
		}
	}

	return nil
}

# APR (Alarm Panel Relay) Test System

## Overview

The APR test system provides centralized functionality for managing 10-minute APR test sessions on edge devices. It supports both gRPC and command queue-based operations with automatic companion port management.

**New Architecture**: The system has been restructured into a clean services architecture with separate APR and EPI services that can be used independently or together.

## User Journey

1. **Configuration**: Technician configures APR on a specific phone number/port
2. **Test Initiation**: System runs a 10-minute test to verify alarm signals work properly
3. **Companion Port Management**: If specified, companion port is disabled during test
4. **Test Monitoring**: System monitors test progress and handles pass/fail/timeout scenarios
5. **Completion**: If test passes, APR goes live; if fails, APR is disabled and must be reconfigured

## Architecture

### New Services Structure

The system is now organized into a clean services architecture:

```
pkg/services/
├── service.go          # Main services factory
├── apr/
│   ├── apr_service.go  # APR functionality
│   └── apr_types.go    # APR data types
└── epi/
    ├── epi_service.go  # EPI port control
    └── epi_types.go    # EPI data types
```

### Core Components

1. **Services Factory** (`pkg/services/service.go`)
   - Creates and manages all service instances
   - Handles dependency injection between services
   - Single entry point for service initialization

2. **APR Service** (`pkg/services/apr/apr_service.go`)
   - Centralized APR functionality
   - Test session management with 10-minute timeout
   - Uses EPI service for companion port management
   - MongoDB persistence for test history

3. **EPI Service** (`pkg/services/epi/epi_service.go`)
   - Centralized EPI port control functionality
   - Port enable/disable operations
   - IP and port calculation logic

4. **gRPC Integration** (`pkg/grpc/apr.go`)
   - Direct gRPC methods for APR operations
   - Uses centralized services via dependency injection
   - Supports toggle, status, test results, and test management

5. **Command Queue Integration** (`pkg/command/methods/apr.go`)
   - Queue-based APR test execution
   - Supports start/status/stop actions via command payload
   - Uses centralized services for consistency

## Key Features

### ✅ **10-Minute Test Sessions**
- Automatic timeout after 10 minutes
- Real-time monitoring every 30 seconds
- Pass/fail detection based on device response
- Prevents multiple tests on same box:port

### ✅ **Companion Port Management**
- Automatically disables companion port during test
- Re-enables companion port after test completion/timeout
- Uses existing EPI functionality for port control

### ✅ **Centralized Functions**
- Single APR service used by both gRPC and command queue
- Consistent behavior across all interfaces
- Shared HTTP client and database connections

### ✅ **Test History & Persistence**
- MongoDB storage for all test sessions
- Test history retrieval by box/port
- Status tracking throughout test lifecycle

## API Usage

### gRPC Methods

```go
// Toggle APR on/off
rpc ToggleAPR(APRToggleRequest) returns (APRToggleResponse);

// Get APR status
rpc APRStatus(APRStatusRequest) returns (APRStatusResponse);

// Get test results
rpc APRTestResults(APRTestResultRequest) returns (APRTestResultResponse);

// Start APR test (10-minute session)
rpc StartAPRTest(APRTestStartRequest) returns (APRTestStartResponse);

// Get test status
rpc GetAPRTestStatus(APRTestStartRequest) returns (APRTestStartResponse);

// Stop active test
rpc StopAPRTest(APRTestStartRequest) returns (APRTestStartResponse);
```

### Command Queue Usage

```json
{
  "command": "apr",
  "payload": {
    "port": "1",
    "companion_port": "2",
    "action": "start"
  }
}
```

**Actions:**
- `"start"` - Start 10-minute APR test
- `"status"` - Get current test status
- `"stop"` - Stop active test

## Implementation Details

### Test Session Flow

1. **Start Test**:
   - Check no existing test on box:port
   - Disable companion port if specified
   - Call `/aprtest/{port}` on device
   - Store session in MongoDB
   - Start 10-minute monitoring

2. **Monitor Test**:
   - Check `/aprtestresults/{port}` every 30 seconds
   - Detect pass/fail/timeout conditions
   - Update session status in real-time

3. **Complete Test**:
   - Re-enable companion port
   - Update final status in MongoDB
   - Clean up active session

### Database Schema

**MongoDB Collection: `apr_test_sessions`**
```json
{
  "_id": "ObjectId",
  "box_id": "string",
  "port": "string", 
  "companion_port": "string",
  "start_time": "ISODate",
  "end_time": "ISODate",
  "status": "running|passed|failed|timeout|cancelled",
  "result": "string"
}
```

### Restbin Endpoints Used

- `/aprtest/{port}` - Start APR test
- `/aprtestresults/{port}` - Get test results
- `/aprstatus/{port}` - Get APR status
- `/aprtoggle/{port}/{enable|disable}` - Toggle APR
- `/epi/{ip}/{port}/{enable|disable}` - Control companion port

## Error Handling

- **Concurrent Tests**: Prevents multiple tests on same box:port
- **Companion Port Failures**: Logs warnings but doesn't fail test
- **Device Communication**: Retries and graceful error handling
- **Test Timeout**: Automatic cleanup after 10 minutes
- **Application Restart**: Recovers active tests from MongoDB

## Services Usage

### Creating Services

```go
import "github.com/EPIKio/myepikV2/edge/pkg/services"

// Create HTTP client
httpClient := boxhttp.NewDefaultClient()

// Create all services
allServices := services.NewServices(httpClient)

// Use individual services
aprResult, err := allServices.APR.ToggleAPR(ctx, "BOX001", "1", true)
epiResult, err := allServices.EPI.EnableDisablePort(ctx, "BOX001", 2, false)
```

### Service Dependencies

- **APR Service** depends on **EPI Service** for companion port management
- **EPI Service** is independent and can be used standalone
- Both services use the same HTTP client for device communication

## Integration Points

### With Existing Systems

1. **HTTP Client**: Uses existing `boxes.EdgeDeviceClient`
2. **Database**: Uses existing MongoDB connection from `pkg/db`
3. **Command Queue**: Integrates with existing command processing
4. **gRPC Service**: Uses services via dependency injection

### Configuration

No additional configuration required - uses existing:
- MongoDB connection
- Redis connection (for command queue)
- HTTP client settings

## Testing

```bash
# Build and test
go build ./cmd/edge

# Start APR test via gRPC
grpcurl -plaintext -d '{
  "serial_number": "BOX001",
  "port": "1", 
  "companion_port": "2"
}' localhost:50051 edge.v1.EdgeDeviceProxy/StartAPRTest

# Start APR test via command queue
curl -X POST localhost:8080/webhook/box-online \
  -d '{"box_id": "BOX001"}'

# Queue APR test command
redis-cli ZADD "queue:commands:BOX001" 1 "cmd-123"
redis-cli SET "command:cmd-123" '{"id":"cmd-123","box_id":"BOX001","command":"apr","payload":"{\"port\":\"1\",\"companion_port\":\"2\",\"action\":\"start\"}"}'
```

## Production Considerations

- **Monitoring**: Track test success rates and failure patterns
- **Alerting**: Alert on test failures or timeouts
- **Capacity**: Limit concurrent tests per box to prevent resource issues
- **Logging**: Comprehensive logging for troubleshooting
- **Recovery**: Automatic cleanup of stale test sessions on startup

The APR test system provides a robust, centralized solution for managing alarm panel relay testing with proper error handling, monitoring, and integration with existing infrastructure.

package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	server "github.com/EPIKio/myepikV2/edge/pkg/app"
	"github.com/EPIKio/myepikV2/edge/pkg/boxes"
	boxhttp "github.com/EPIKio/myepikV2/edge/pkg/boxes/http"
	"github.com/EPIKio/myepikV2/edge/pkg/config"
	"github.com/EPIKio/myepikV2/edge/pkg/db"
)

func main() {
	// Create base context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		cancel()
	}()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	db.Connect(ctx, cfg.MongoURI)

	// Create MongoDB repository with context
	boxRepo, err := boxes.NewBoxRepository(ctx)
	if err != nil {
		log.Fatalf("Failed to create box repository: %v", err)
	}

	// Create HTTP client (not used directly but may be needed for future extensions)
	_ = boxhttp.NewDefaultClient()

	// Create server with all dependencies
	srv, err := server.New(server.Config{
		Port:          cfg.ServerPort,
		BoxRepository: boxRepo,
	})
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Run server
	errChan := make(chan error, 1)
	go func() {
		errChan <- srv.Run()
	}()

	// Wait for either context cancellation or server error
	select {
	case <-ctx.Done():
		// Initiate graceful shutdown
		log.Println("Shutting down gracefully...")
		srv.Stop()
		db.Client.Disconnect(context.Background())
	case err := <-errChan:
		if err != nil {
			log.Fatalf("Server error: %v", err)
		}
	}
}

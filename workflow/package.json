{"name": "temporal-dsl-interpreter", "version": "0.1.0", "private": true, "scripts": {"build": "tsc --build", "build.watch": "tsc --build --watch", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "eslint .", "start": "ts-node src/worker.ts", "start.watch": "nodemon src/worker.ts", "start.serviceDownWorker": "ts-node src/workers/serviceDownWorker.ts", "start.serviceDownFlow": "ts-node src/client.ts", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "nodemonConfig": {"execMap": {"ts": "ts-node"}, "ext": "ts", "watch": ["src"]}, "dependencies": {"@temporalio/activity": "^1.11.8", "@temporalio/client": "^1.11.8", "@temporalio/worker": "^1.11.8", "@temporalio/workflow": "^1.11.8", "@tsconfig/node18": "^18.2.4", "@types/mongodb": "^4.0.6", "axios": "^1.10.0", "dotenv": "^16.5.0", "express": "^4.18.2", "fast-xml-parser": "^5.2.5", "js-yaml": "^4.1.0", "mongodb": "^6.17.0", "opossum": "^9.0.0", "pg": "^8.16.0", "prom-client": "^15.1.3", "typescript": "^5.6.3", "zod": "^3.25.67"}, "devDependencies": {"@types/express": "^4.17.21", "@types/js-yaml": "^4.0.5", "@types/node": "^22.9.1", "@types/opossum": "^8.1.8", "@types/pg": "^8.15.4", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-deprecation": "^3.0.0", "nodemon": "^3.1.7", "prettier": "^3.4.2", "ts-node": "^10.9.2", "vitest": "^2.1.9"}}
import { proxyActivities, log, executeChild } from '@temporalio/workflow';

export interface ServiceDownActivities {
  get_noc_tickets: () => Promise<any>;
  setup_noc_flow_for_ticket: (ticket_id: string) => Promise<any>;
  get_ticket_data: (ticket_id: string) => Promise<any>;
  get_device_ip: (context: any) => Promise<any>;
  fetch_ata_xml: (context: any, ataIndex: number) => Promise<any>;
  fix_data: (context: any, ataIndex: number) => Promise<any>;
  updateContextHistory: (executionId: number, history: Record<string, any>) => Promise<any>;
  updateContext: (workflowInstance: any, context: any) => Promise<any>;
}

const {
  get_noc_tickets,
  setup_noc_flow_for_ticket,
  get_ticket_data,
  get_device_ip,
  fetch_ata_xml,
  fix_data,
  updateContextHistory,
  updateContext,
} = proxyActivities<ServiceDownActivities>({
  startToCloseTimeout: '15 minutes',
  retry: {
    initialInterval: '1 minute',
    maximumInterval: '10 minutes',
    maximumAttempts: 3,
  },
});

export interface ServiceDownWorkflowInput {
  devices: Array<{ rowNum: number; serialNumber: string; ticketId?: string }>;
  scheduleInterval?: string;
}

export async function serviceDownWorkflow() {
  log.info('Starting service down monitoring workflow');

  const tickets = await get_noc_tickets();

  await Promise.all(
    tickets.map(async (ticket: any) => {
      await executeChild(serviceDownChildFlow, {
        args: [ticket.id],
        workflowId: `serviceDownChildFlow-${ticket.id}`,
      });
    }),
  );
}

export async function serviceDownChildFlow(ticket_id: string) {
  log.info(`Starting service down child flow for ticket ${ticket_id}`);
  let workflowInstance: any;
  let context: any = {};

  try {
    const workflowInstanceStart = new Date();
    const {id, status} = await setup_noc_flow_for_ticket(ticket_id);
    if(status === 'Completed') {
      return {
        success: true,
      };
    }
    
    workflowInstance = id;

    const workflowInstanceEnd = new Date();

    await updateContextHistory(workflowInstance, {
      node_json_id: 'setup_noc_flow_for_ticket',
      node_name: 'setup_noc_flow_for_ticket',
      node_type: 'action',
      entry_time: workflowInstanceStart,
      exit_time: workflowInstanceEnd,
      event_status: 'success',
      context_before_processing: context,
      context_after_processing: { ...context, workflowInstance },
      action_outputs: { workflowInstance },
    });

    const ticketDataStart = new Date();
    const contextBeforeTicketData = { ...context };
    const ticketData = await get_ticket_data(ticket_id);
    context = { ...context, ...ticketData };
    const ticketDataEnd = new Date();

    await updateContextHistory(workflowInstance, {
      node_json_id: 'get_ticket_data',
      node_name: 'get_ticket_data',
      node_type: 'action',
      entry_time: ticketDataStart,
      exit_time: ticketDataEnd,
      event_status: 'success',
      context_before_processing: contextBeforeTicketData,
      context_after_processing: context,
      action_outputs: { ticketData },
    });

    const deviceIpStart = new Date();
    const contextBeforeDeviceIp = { ...context };
    const deviceIpResult = await get_device_ip(context);
    context = { ...context, ...deviceIpResult };
    const deviceIpEnd = new Date();

    await updateContextHistory(workflowInstance, {
      node_json_id: 'get_device_ip',
      node_name: 'get_device_ip',
      node_type: 'action',
      entry_time: deviceIpStart,
      exit_time: deviceIpEnd,
      event_status: 'success',
      context_before_processing: contextBeforeDeviceIp,
      context_after_processing: context,
      action_outputs: { deviceIpResult },
    });

    for (let i = 0; i < context.numberOfATA; i++) {
      const fetchAtaXmlStart = new Date();
      const contextBeforeFetchAtaXml = { ...context };
      const ataXmlResult = await fetch_ata_xml(context, i);
      context = { ...context, ...ataXmlResult };
      const fetchAtaXmlEnd = new Date();

      await updateContextHistory(workflowInstance, {
        node_json_id: `fetch_ata_xml_${i}`,
        node_name: 'fetch_ata_xml',
        node_type: 'action',
        entry_time: fetchAtaXmlStart,
        exit_time: fetchAtaXmlEnd,
        event_status: 'success',
        context_before_processing: contextBeforeFetchAtaXml,
        context_after_processing: context,
        action_outputs: { ataXmlResult },
      });

      const fixDataStart = new Date();
      const contextBeforeFixData = { ...context };
      const fixDataResult = await fix_data(context, i);
      context = { ...context, ...fixDataResult };
      const fixDataEnd = new Date();

      await updateContextHistory(workflowInstance, {
        node_json_id: `fix_data_${i}`,
        node_name: 'fix_data',
        node_type: 'action',
        entry_time: fixDataStart,
        exit_time: fixDataEnd,
        event_status: 'success',
        context_before_processing: contextBeforeFixData,
        context_after_processing: context,
        action_outputs: { fixDataResult },
      });

      if (fixDataResult?.restart) {
        const ataXmlRetryStart = new Date();
        const contextBeforeAtaXmlRetry = { ...context };
        const ataXmlRetryResult = await fetch_ata_xml(context, i);
        context = { ...context, ...ataXmlRetryResult };
        const ataXmlRetryEnd = new Date();

        await updateContextHistory(workflowInstance, {
          node_json_id: `fetch_ata_xml_retry_${i}`,
          node_name: 'fetch_ata_xml_retry',
          node_type: 'action',
          entry_time: ataXmlRetryStart,
          exit_time: ataXmlRetryEnd,
          event_status: 'success',
          context_before_processing: contextBeforeAtaXmlRetry,
          context_after_processing: context,
          action_outputs: { ataXmlRetryResult },
        });

        const fixDataRetryStart = new Date();
        const contextBeforeFixDataRetry = { ...context };
        const fixDataRetryResult = await fix_data(context, i);
        context = { ...context, ...fixDataRetryResult };
        const fixDataRetryEnd = new Date();

        await updateContextHistory(workflowInstance, {
          node_json_id: `fix_data_retry_${i}`,
          node_name: 'fix_data_retry',
          node_type: 'action',
          entry_time: fixDataRetryStart,
          exit_time: fixDataRetryEnd,
          event_status: 'success',
          context_before_processing: contextBeforeFixDataRetry,
          context_after_processing: context,
          action_outputs: { fixDataRetryResult },
        });
      }
    }

    await updateContext(workflowInstance, {
      ticket: ticket_id,
      workflow_instance: workflowInstance,
      final_context: context,
      status: 'Completed',
      end_time: new Date(),
    });

    return {
      success: true,
    };
  } catch (error: any) {
    log.error(`Error in service down child flow for ticket ${ticket_id}: ${error}`);
    
    if (workflowInstance) {
      await updateContextHistory(workflowInstance, {
        node_json_id: 'service_down_child_flow_error',
        node_name: 'service_down_child_flow',
        node_type: 'workflow',
        entry_time: new Date(),
        exit_time: new Date(),
        event_status: 'error',
        context_before_processing: context,
        context_after_processing: context,
        action_outputs: { error: error.message },
        details: error.stack,
      });
    }

    await updateContext(workflowInstance, {
      ticket: ticket_id,
      workflow_instance: workflowInstance,
      final_context: context,
      status: 'Failed',
      end_time: new Date(),
    });

    throw error;
  }
}

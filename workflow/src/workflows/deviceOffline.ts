import { proxyActivities } from '@temporalio/workflow'

const { get_noc_tickets, setup_noc_flow_for_ticket } = proxyActivities({
    startToCloseTimeout: '24 hours',
    retry: {
        maximumAttempts: 3
    }
})

export async function deviceOffline(): Promise<void> {
    const nocTickets = await get_noc_tickets()
    for (const ticket of nocTickets) {
        const workflowInstance = await setup_noc_flow_for_ticket(ticket.id)
        console.log(`Setup workflow for ticket ${ticket.id} with instance ${workflowInstance}`)
    }
}
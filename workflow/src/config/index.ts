import * as dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const configSchema = z.object({
  POSTGRES_HOST: z.string(),
  POSTGRES_PORT: z.string().transform(Number),
  POSTGRES_USER: z.string(),
  POSTGRES_PASSWORD: z.string(),
  POSTGRES_DB: z.string(),
  MONGO_URI: z.string(),
  MONGO_DB: z.string(),
  CW_API_KEY: z.string(),
  CW_CLIENT_ID: z.string(),
  TEMPORAL_URL: z.string(),
});

const parsed = configSchema.safeParse(process.env);

if (!parsed.success) {
  console.error('❌ Invalid environment variables:', parsed.error.format());
  process.exit(1);
}

export const config = {
  postgres: {
    host: parsed.data.POSTGRES_HOST,
    port: parsed.data.POSTGRES_PORT,
    user: parsed.data.POSTGRES_USER,
    password: parsed.data.POSTGRES_PASSWORD,
    database: parsed.data.POSTGRES_DB,
  },
  mongo: {
    uri: parsed.data.MONGO_URI,
    db: parsed.data.MONGO_DB,
  },
  cw: {
    apiKey: parsed.data.CW_API_KEY,
    clientId: parsed.data.CW_CLIENT_ID,
  },
  temporal: {
    url: parsed.data.TEMPORAL_URL,
  },
};

export type AppConfig = typeof config;
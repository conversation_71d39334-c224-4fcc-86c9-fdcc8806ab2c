import axios, { AxiosInstance, AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios';
import CircuitBreaker from 'opossum';
import {
  httpRequestCounter,
  httpRequestDuration,
  circuitBreakerState,
} from './metrics';

class ApiError extends Error {
    public status?: number;
    public responseBody?: any;
    public url?: string;
    public method?: string;
  
    constructor(message: string, props: Partial<ApiError> = {}) {
      super(message);
      Object.assign(this, props);
    }
  }

const logger = {
    info: console.log,
    warn: console.warn,
    error: (...args: any[]) => {
      console.error('[API ERROR]', ...args);
    },
};

type ApiClientOptions = {
  baseURL: string;
  timeoutMs?: number;
  retries?: number;
  headers?: Record<string, string>;
  traceIdHeader?: string;
};

export function createApiClient(options: ApiClientOptions): AxiosInstance {
  const {
    baseURL,
    timeoutMs = 10000,
    headers = {},
    traceIdHeader = 'x-trace-id',
  } = options;

  const instance = axios.create({
    baseURL,
    timeout: timeoutMs,
    headers,
  });

  instance.interceptors.request.use((config) => {
    const traceId = generateTraceId();
    if (traceIdHeader) {
      config.headers![traceIdHeader] = traceId;
    }
    (config as any).metadata = {
      startTime: new Date(),
      traceId,
    };
    return config;
  });

  instance.interceptors.response.use(
    (response) => {
      logMetrics(response.config, response.status);
      return response;
    },
    (error: AxiosError) => {
      const config = error.config;
      const status = error.response?.status || 0;
      logMetrics(config as AxiosRequestConfig, status);
      const apiError = new ApiError(error.message, {
        status,
        url: config?.url,
        method: config?.method,
        responseBody: error.response?.data,
      });
      logger.error(`[API ERROR] ${config?.method?.toUpperCase()} ${config?.url}`, {
        status,
        traceId: (config as any)?.metadata?.traceId,
      });
      throw apiError;
    }
  );

  return wrapWithCircuitBreaker(instance);
}

function logMetrics(config: AxiosRequestConfig, status: number) {
  const meta = (config as any).metadata || {};
  const duration =
    (new Date().getTime() - meta.startTime?.getTime()) / 1000 || 0;

  const labels = {
    method: (config.method || 'unknown').toUpperCase(),
    endpoint: config.url || 'unknown',
    status: String(status),
  };

  httpRequestCounter.inc(labels);
  httpRequestDuration.observe(labels, duration);
}

function wrapWithCircuitBreaker(instance: AxiosInstance): AxiosInstance {
  const breaker = new CircuitBreaker(
    (config: AxiosRequestConfig) => instance.request(config),
    {
      timeout: 10000,
      errorThresholdPercentage: 50,
      resetTimeout: 30000,
      rollingCountTimeout: 10000,
      rollingCountBuckets: 10,
    }
  );

  breaker.on('open', () => {
    circuitBreakerState.inc({ endpoint: 'global' });
    logger.warn(`[CIRCUIT BREAKER OPEN] API requests temporarily blocked`);
  });

  breaker.on('halfOpen', () => logger.info(`[CIRCUIT BREAKER HALF-OPEN] Testing service...`));
  breaker.on('close', () => logger.info(`[CIRCUIT BREAKER CLOSED] Service back to normal`));

  const wrapped = {} as AxiosInstance;

  ['get', 'post', 'put', 'patch', 'delete'].forEach((method) => {
    (wrapped as any)[method] = async (...args: any[]) => {
      const config =
        typeof args[1] === 'object'
          ? { method, url: args[0], ...args[1] }
          : { method, url: args[0] };

      return breaker.fire(config).then((res: any) => res);
    };
  });

  wrapped.request = async <T = any, R = AxiosResponse<T>>(config: AxiosRequestConfig): Promise<R> => {
    return breaker.fire(config) as Promise<R>;
  };

  return wrapped;
}

function generateTraceId(): string {
  return Math.random().toString(36).substring(2, 10) + Date.now().toString(36);
}

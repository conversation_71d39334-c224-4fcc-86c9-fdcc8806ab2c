type PollUntilOptions<T> = {
    client: import('axios').AxiosInstance;
    url: string;
    method?: 'get' | 'post';
    intervalMs?: number;
    timeoutMs?: number;
    requestData?: any;
    shouldStop: (data: T) => boolean;
  };
  
  export async function pollUntil<T = any>({
    client,
    url,
    method = 'get',
    intervalMs = 60000,
    timeoutMs = 10 * 60 * 1000, 
    requestData,
    shouldStop,
  }: PollUntilOptions<T>): Promise<T> {
    const start = Date.now();
  
    while (true) {
      try {
        const res = await client.request<T>({
          url,
          method,
          data: requestData,
        });
  
        if (shouldStop(res.data)) {
          return res.data;
        }
      } catch (err) {
        console.error(`Polling error:`, (err as Error).message);
      }
  
      if (Date.now() - start >= timeoutMs) {
        throw new Error(`Polling timed out after ${timeoutMs / 1000} seconds.`);
      }
  
      await new Promise((resolve) => setTimeout(resolve, intervalMs));
    }
  }
  
import { getPostgresClient } from '../clients/postgres';
import { getMongoClient } from '../clients/mongodb';
import { MongoDBClient } from '../clients/mongodb';

export interface WorkflowContextData {
  [key: string]: any;
}

export interface WorkflowContextOptions {
  executionId?: number;
  workflowId?: number;
  nodeId?: string;
  nodeName?: string;
  nodeType?: string;
}

export class WorkflowContext {
  private contextData: WorkflowContextData;
  private options: WorkflowContextOptions;
  private pg: ReturnType<typeof getPostgresClient>;
  private mongo: MongoDBClient | null = null;

  constructor(initialContext: WorkflowContextData = {}, options: WorkflowContextOptions = {}) {
    this.contextData = { ...initialContext };
    this.options = options;
    this.pg = getPostgresClient();
  }

  public async initialize(): Promise<void> {
    // Initialize MongoDB client
    this.mongo = await getMongoClient();
  }

  public get(key: string): any {
    return this.contextData[key];
  }

  public getAll(): WorkflowContextData {
    return { ...this.contextData };
  }

  public async set(key: string, value: any): Promise<void> {
    this.contextData[key] = value;
    await this.logContextChange(key, value);
  }

  public async setMultiple(values: WorkflowContextData): Promise<void> {
    Object.entries(values).forEach(([key, value]) => {
      this.contextData[key] = value;
    });
    await this.logContextChange('multiple', values);
  }

  private async logContextChange(key: string, value: any): Promise<void> {
    if (!this.options.executionId || !this.mongo) return;

    try {
      // Log to MongoDB for quick access and analytics
      await this.mongo.insertOne('workflow_context_changes', {
        executionId: this.options.executionId,
        workflowId: this.options.workflowId,
        nodeId: this.options.nodeId,
        nodeName: this.options.nodeName,
        nodeType: this.options.nodeType,
        key,
        value,
        timestamp: new Date(),
        contextSnapshot: this.contextData
      });

      // Log to PostgreSQL for workflow execution history
      if (this.options.nodeId) {
        await this.pg.query(
          `INSERT INTO workflow_execution_history 
           (execution_id, node_json_id, node_name, node_type, event_status, context_after_processing)
           VALUES ($1, $2, $3, $4, $5, $6)
           ON CONFLICT (execution_id, node_json_id) 
           DO UPDATE SET 
           context_after_processing = $6,
           updated_at = CURRENT_TIMESTAMP`,
          [
            this.options.executionId,
            this.options.nodeId,
            this.options.nodeName,
            this.options.nodeType,
            'context_updated',
            this.contextData
          ]
        );
      }
    } catch (error) {
      console.error('Error logging context change:', error);
      // Don't throw the error to prevent disrupting the workflow
    }
  }

  public async evaluateCondition(condition: {
    variable: string;
    operator: string;
    value: any;
  }): Promise<boolean> {
    const { variable, operator, value } = condition;
    const actualValue = this.get(variable);

    switch (operator) {
      case '==':
        return actualValue == value; // eslint-disable-line eqeqeq
      case '!=':
        return actualValue != value; // eslint-disable-line eqeqeq
      case '>':
        return Number(actualValue) > Number(value);
      case '<':
        return Number(actualValue) < Number(value);
      case '>=':
        return Number(actualValue) >= Number(value);
      case '<=':
        return Number(actualValue) <= Number(value);
      case 'contains':
        return typeof actualValue === 'string' && actualValue.includes(value);
      case 'not contains':
        return typeof actualValue === 'string' && !actualValue.includes(value);
      default:
        console.warn(`Unsupported operator: ${operator}`);
        return false;
    }
  }

  public async saveExecutionState(): Promise<void> {
    if (!this.options.executionId) return;

    try {
      // Update the workflow execution record
      await this.pg.query(
        `UPDATE workflow_executions 
         SET final_context = $1,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = $2`,
        [this.contextData, this.options.executionId]
      );
    } catch (error) {
      console.error('Error saving execution state:', error);
    }
  }

  public async loadExecutionState(executionId: number): Promise<void> {
    try {
      const result = await this.pg.query(
        'SELECT final_context FROM workflow_executions WHERE id = $1',
        [executionId]
      );

      if (result.rows.length > 0 && result.rows[0].final_context) {
        this.contextData = result.rows[0].final_context;
        this.options.executionId = executionId;
      }
    } catch (error) {
      console.error('Error loading execution state:', error);
      throw error;
    }
  }
}

// Example usage:
/*
// Create a new workflow context
const context = new WorkflowContext(
  {
    ticketId: 'INC12345',
    serialNumber: 'SN98765',
    impact: 'Medium',
    severity: 'Medium',
    priority: 'Medium'
  },
  {
    executionId: 123,
    workflowId: 456,
    nodeId: 'action-123',
    nodeName: 'Run NOC Wizard',
    nodeType: 'action'
  }
);

// Initialize the context
await context.initialize();

// Set a single value
await context.set('last_run_status', 'ok');

// Set multiple values
await context.setMultiple({
  last_run_result: 'success',
  last_run_Id: 'wizard-' + Date.now()
});

// Get a value
const status = context.get('last_run_status');

// Evaluate a condition
const isOk = await context.evaluateCondition({
  variable: 'last_run_status',
  operator: '==',
  value: 'ok'
});

// Save the execution state
await context.saveExecutionState();

// Load a previous execution state
await context.loadExecutionState(123);
*/ 
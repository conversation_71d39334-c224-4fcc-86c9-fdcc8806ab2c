import { Counter, Histogram, Registry } from 'prom-client';

export const register = new Registry();

export const httpRequestCounter = new Counter({
  name: 'api_requests_total',
  help: 'Total API requests',
  labelNames: ['method', 'status', 'endpoint'],
});

export const httpRequestDuration = new Histogram({
  name: 'api_request_duration_seconds',
  help: 'Duration of API requests in seconds',
  labelNames: ['method', 'endpoint', 'status'],
  buckets: [0.1, 0.3, 0.5, 1, 2, 5],
});

export const circuitBreakerState = new Counter({
  name: 'circuit_breaker_tripped_total',
  help: 'Total times circuit breaker opened',
  labelNames: ['endpoint'],
});

export function getMetrics(): Promise<string> {
  return register.metrics();
}

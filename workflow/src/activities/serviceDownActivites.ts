import { XMLParser } from 'fast-xml-parser';
// import { ActivityOutput } from '../utils/activityWrapper';
import { createApiClient } from '../utils/apiClient';
import { log } from '@temporalio/workflow';

export interface DeviceCredentials {
  user: string;
  password: string;
}

export type ActivityOutput = any;

export interface EpikBox {
  serialNumber: string;
  Obis: string[];
  vpnAddress: string;
  datacenter: string;
}

export interface VoltageReadings {
  p1: number | null;
  p2: number | null;
  loop1: string | null;
  loop2: string | null;
  vbat1: string | null;
  vbat2: string | null;
}

export interface ProcessingResult {
  rowNum: number;
  serialNumber: string;
  ip: string | null;
  port: number;
  p1: number | null;
  p2: number | null;
  loop1: string | null;
  loop2: string | null;
  vbat1: string | null;
  vbat2: string | null;
  ticketId?: string;
}

const PORTS = [10250, 10251, 10252, 10253];
const ENDPOINT = '/live-data.xml';

export const createActivities = (db: any, mongo: any, config: any) => {
  async function get_noc_tickets(): Promise<ActivityOutput> {
    const tickets = await db.query(
      `SELECT id FROM noc_tickets WHERE status_name != 'Resolved' AND item_name = 'Service Down' AND source_name = 'Zabbix' AND created_at >= NOW() - INTERVAL '72 hours'`,
    );

    return tickets?.rows;
  }

  async function setup_noc_flow_for_ticket(ticket_id: string): Promise<ActivityOutput> {
    try {
      const serviceDownWorkflow = await db.query(`SELECT id from workflows_v2 WHERE name = 'Service Down'`);
      const workflowInstance = await db.query(
        `SELECT * FROM workflow_executions WHERE workflow_id = ${serviceDownWorkflow?.rows[0]?.id} AND ticket = ${ticket_id} AND type = 'noc' ORDER BY id DESC LIMIT 1`,
      );
      if (!workflowInstance?.rows?.length) {
        const workflowInstance = await db.query(
          `INSERT INTO workflow_executions (workflow_id, ticket, type, status, start_time) VALUES (${serviceDownWorkflow?.rows[0]?.id}, ${ticket_id}, 'noc', 'In Progress', NOW()) RETURNING id`,
        );
        return { id: workflowInstance?.rows[0]?.id, status: 'In Progress' };
      }

      return { id: workflowInstance?.rows[0]?.id, status: workflowInstance?.rows[0]?.status };
    } catch (error) {
      console.error('Error setting up NOC flow for ticket', error);
      throw error;
    }
  }

  async function get_ticket_data(ticket_id: string): Promise<ActivityOutput> {
    const ticket = await db.query(`SELECT * FROM noc_tickets WHERE id = ${ticket_id}`);
    if (!ticket?.rows?.length) {
      throw new Error('Ticket not found');
    }

    const epikBoxData = (await mongo.findOne('epikboxes', {
      serialNumber: ticket?.rows[0]?.serial_number,
    })) as EpikBox | null;
    if (!epikBoxData) {
      throw new Error('EpikBox data not found');
    }

    const context = {
      ticket_id: ticket?.rows[0]?.id,
      serialNumber: ticket?.rows[0]?.serial_number,
      vpnAddress: epikBoxData?.vpnAddress,
      dc: epikBoxData?.datacenter,
      numberOfATA: epikBoxData?.Obis?.length || 0,
    };

    return context;
  }

  async function get_device_ip(context: any): Promise<ActivityOutput> {
    const apiClient = createApiClient({
      baseURL: 'https://cdm.epikadmin.com',
    });

    const device = await apiClient.get(`/dev_ip?sn=${context.serialNumber}`);
    const ip = device?.data || null;
    if (!ip) {
      throw new Error('Device IP not found');
    }

    const updatedContext = {
      ...context,
      vpnAddress: ip,
    };

    return updatedContext;
  }

  function parseVoltages(xml: any) {
    try {
      const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: '@_',
      });
      const result = parser.parse(xml);
      console.log(result);
      let p1 = null;
      let p2 = null;
      let loop1 = null;
      let loop2 = null;
      let vbat1 = null;
      let vbat2 = null;

      const getValue = (node: any, tag: any, numeric = false) => {
        if (!node || !Array.isArray(node)) return null;

        for (const item of node) {
          if (item.N === tag) {
            const value = item.V;
            if (!value) return null;

            if (numeric) {
              try {
                return parseFloat(String(value).split(' ')[0]);
              } catch {
                return null;
              }
            }
            return String(value).trim();
          }
        }
        return null;
      };

      if (result && result?.ParameterList?.O) {
        const objects = result?.ParameterList?.O;

        for (const obj of objects) {
          console.log(obj);
          const section = obj.N?.trim();
          if (section === 'Port1 Status') {
            p1 = getValue(obj.P, 'TipRingVoltage', true);
            loop1 = getValue(obj.P, 'LoopCurrent');
            vbat1 = getValue(obj.P, 'VBAT');
          } else if (section === 'Port2 Status') {
            p2 = getValue(obj.P, 'TipRingVoltage', true);
            loop2 = getValue(obj.P, 'LoopCurrent');
            vbat2 = getValue(obj.P, 'VBAT');
          }
        }
      }

      console.debug('Parsed voltages', { p1, p2, loop1, loop2, vbat1, vbat2 });
      return { p1, p2, loop1, loop2, vbat1, vbat2 };
    } catch (error) {
      console.error('XML parse error', { error: error instanceof Error ? error.message : String(error) });
      return { p1: null, p2: null, loop1: null, loop2: null, vbat1: null, vbat2: null };
    }
  }

  async function fetch_ata_xml(context: any, ataIndex: number): Promise<ActivityOutput> {
    const apiClient = createApiClient({
      baseURL: `http://internal-proxy.default.svc.cluster.local`,
      headers: {
        Accept: 'text/xml',
        'Content-Type': 'text/xml',
        Authorization: `Basic ${Buffer.from('root:root').toString('base64')}`,
      },
    });

    const url = `?ip=${context.vpnAddress}&dc=${context.dc}&port=${PORTS[ataIndex]}&path=${ENDPOINT}`;
    let lastError: any;
    for (let attempt = 1; attempt <= 4; attempt++) {
      try {
        const response = await apiClient.get(url);
        const voltages = parseVoltages(response.data);
        const updatedContext = {
          ...context,
          [`${PORTS[ataIndex]}`]: voltages,
        };
        return updatedContext;
      } catch (error) {
        lastError = error;
        if (attempt < 4) {
          log.debug('XML fetch attempt failed, retrying', {
            attempt,
            url,
            error: error instanceof Error ? error.message : String(error),
          });
          await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
    throw lastError;
  }

  async function fix_data(context: any, ataIndex: number): Promise<ActivityOutput> {
    const cwApiClient = createApiClient({
      baseURL: 'https://api-na.myconnectwise.net/v4_6_release/apis/3.0',
      headers: {
        clientId: config.clientId,
        Authorization: `Basic ${config.apiKey}`,
        Accept: 'application/vnd.connectwise.com+json; version=2022.2',
      },
    });

    const voltages = context[`${PORTS[ataIndex]}`];
    const hasNAValues = Object.values(voltages).some((value: any) =>
      ['NA', '--', 'not applicable', '', 'N/A', ' ', '', 0, '0', null, undefined, '0.0', 0.0].includes(
        value?.split()[0],
      ),
    );
    if (!hasNAValues) {
      const ticketData = await cwApiClient.get(`/service/tickets/${context.ticket_id}`);

      console.log(ticketData);
      await cwApiClient.put(`/service/tickets/${context.ticket_id}`, {
        ...ticketData.data,
        status: {
          id: 493,
          name: 'Monitoring/Cleared',
        },
      });
      await cwApiClient.post(`/service/tickets/${context.ticket_id}/notes`, {
        text: `All port values normal: P1=${voltages.p1}, P2=${voltages.p2}, Loop1=${voltages.loop1}, Loop2=${voltages.loop2}, VBAT1=${voltages.vbat1}, VBAT2=${voltages.vbat2}`,
        internalAnalysisFlag: true,
      });

      const updatedContext = {
        ...context,
        restart: false,
        message: `All port values normal: P1=${voltages.p1}, P2=${voltages.p2}, Loop1=${voltages.loop1}, Loop2=${voltages.loop2}, VBAT1=${voltages.vbat1}, VBAT2=${voltages.vbat2}`,
      };

      return updatedContext;
    }

    const apiClient = createApiClient({
      baseURL: `http://${context.vpnAddress}:${PORTS[ataIndex]}`,
      headers: {
        'User-Agent': 'ServiceDownMonitor/1.0',
        Accept: 'text/xml',
        'Content-Type': 'text/xml',
        Authorization: `Basic ${Buffer.from('root:root').toString('base64')}`,
      },
    });

    await apiClient.post(`/result.html`, {
      ParameterList: 'PwrSave',
      Port: PORTS[ataIndex],
      SlicEnable: 'true',
    });

    await new Promise((resolve) => setTimeout(resolve, 10000));

    return {
      ...context,
      restart: true,
      message: `Restarting ATA ${PORTS[ataIndex]}`,
    };
  }

  return { get_noc_tickets, setup_noc_flow_for_ticket, get_ticket_data, get_device_ip, fetch_ata_xml, fix_data };
};

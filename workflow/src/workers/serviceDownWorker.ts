import { createActivities } from '../activities/serviceDownActivites';
import { getMongoClient } from '../clients/mongodb';
import { getPostgresClient } from '../clients/postgres';
import { config } from '../config';
import { NativeConnection, Worker } from '@temporalio/worker';
import { updateContextHistory, updateContext } from '../shared/workflowContext';


const postgresConfig = {
  user: config.postgres.user,
  host: config.postgres.host,
  database: config.postgres.database,
  password: config.postgres.password,
  port: config.postgres.port,
};

const mongoConfig = {
  uri: config.mongo.uri,
};

export async function runServiceDownWorker(): Promise<void> {
  const connection = await NativeConnection.connect({
    address: config.temporal.url,
  });
  const mongo = await getMongoClient(mongoConfig.uri);
  const db = await getPostgresClient(postgresConfig);

  const worker = await Worker.create({
    connection,
    workflowsPath: require.resolve('../workflows/serviceDownWorkflow'),
    activities: {
      ...createActivities(db, mongo, config.cw),
      updateContextHistory,
      updateContext,
    },
    taskQueue: 'service-down-monitoring',
    namespace: 'noc_automation',
    maxConcurrentActivityTaskExecutions: 10,
    maxConcurrentWorkflowTaskExecutions: 50,
  });

  console.log('Service Down Monitoring Worker started. Listening on task queue: service-down-monitoring');

  await worker.run();
}
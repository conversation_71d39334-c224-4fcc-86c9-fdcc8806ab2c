import { Pool, PoolClient, QueryResult, PoolConfig, QueryResultRow } from 'pg';
import { EventEmitter } from 'events';

export class PostgresClient extends EventEmitter {
  private static instance: PostgresClient;
  private pool: Pool;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private readonly maxReconnectAttempts: number = 5;
  private readonly reconnectInterval: number = 5000; // 5 seconds

  private constructor(config: PoolConfig) {
    super();
    this.pool = new Pool(config);
    this.setupEventListeners();
  }

  public static getInstance(config: PoolConfig): PostgresClient {
    if (!PostgresClient.instance) {
      PostgresClient.instance = new PostgresClient(config);
    }
    return PostgresClient.instance;
  }

  private setupEventListeners(): void {
    this.pool.on('connect', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connected');
    });

    this.pool.on('error', (err) => {
      this.isConnected = false;
      this.emit('error', err);
      void this.handleReconnect();
    });
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts);

    try {
      await new Promise(resolve => setTimeout(resolve, this.reconnectInterval));
      await this.pool.connect();
    } catch (error) {
      this.emit('reconnectError', error);
      void this.handleReconnect();
    }
  }

  public async query<T extends QueryResultRow = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
    try {
      const result = await this.pool.query(text, params) as any;
      return result;
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public async getClient(): Promise<PoolClient> {
    try {
      const client = await this.pool.connect();
      return client;
    } catch (error) {
      this.emit('clientError', error);
      throw error;
    }
  }

  public async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  public async close(): Promise<void> {
    try {
      await this.pool.end();
      this.isConnected = false;
      this.emit('closed');
    } catch (error) {
      this.emit('closeError', error);
      throw error;
    }
  }

  public isHealthy(): boolean {
    return this.isConnected;
  }
}

let pgInstance: PostgresClient | null = null;

export function getPostgresClient(config?: PoolConfig): PostgresClient {
  if (!pgInstance) {
    if (!config) {
      throw new Error('PostgreSQL configuration is required for initial connection');
    }
    pgInstance = PostgresClient.getInstance(config);
  }
  return pgInstance;
}

// Example usage:
/*
const dbConfig = {
  user: 'your_user',
  host: 'localhost',
  database: 'your_database',
  password: 'your_password',
  port: 5432,
};

const db = getPostgresClient(dbConfig);

// Example query
const result = await db.query('SELECT * FROM users WHERE id = $1', [1]);

// Example transaction
await db.transaction(async (client) => {
  await client.query('INSERT INTO users (name) VALUES ($1)', ['John']);
  await client.query('INSERT INTO profiles (user_id) VALUES ($1)', [1]);
});

// Health check
const isHealthy = await db.healthCheck();
*/

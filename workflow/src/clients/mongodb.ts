import {
  MongoClient,
  MongoClientOptions,
  Db,
  Collection,
  Document,
  Filter,
  UpdateFilter,
  FindOptions,
  InsertOneOptions,
  UpdateOptions,
  DeleteOptions,
  ClientSession,
  ObjectId,
  WithId
} from 'mongodb';
import { EventEmitter } from 'events';

export class MongoDBClient extends EventEmitter {
  private static instance: MongoDBClient;
  private client: MongoClient;
  private db: Db | null = null;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private readonly maxReconnectAttempts: number = 5;
  private readonly reconnectInterval: number = 5000;

  private constructor(private readonly uri: string, private readonly options: MongoClientOptions = {}) {
    super();
    this.client = new MongoClient(uri, {
      ...options,
      maxPoolSize: 10,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      connectTimeoutMS: 10000,
    });
    this.setupEventListeners();
  }

  public static getInstance(uri: string, options?: MongoClientOptions): MongoDBClient {
    if (!MongoDBClient.instance) {
      MongoDBClient.instance = new MongoDBClient(uri, options);
    }
    return MongoDBClient.instance;
  }

  private setupEventListeners(): void {
    this.client.on('connected', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connected');
    });

    this.client.on('error', (err) => {
      this.isConnected = false;
      this.emit('error', err);
      void this.handleReconnect();
    });

    this.client.on('close', () => {
      this.isConnected = false;
      this.emit('disconnected');
    });
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.emit('reconnecting', this.reconnectAttempts);

    try {
      await new Promise(resolve => setTimeout(resolve, this.reconnectInterval));
      await this.connect();
    } catch (error) {
      this.emit('reconnectError', error);
      void this.handleReconnect();
    }
  }

  public async connect(): Promise<void> {
    try {
      await this.client.connect();
      this.db = this.client.db();
      this.isConnected = true;
      this.emit('connected');
    } catch (error) {
      this.emit('connectionError', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.close();
      this.isConnected = false;
      this.db = null;
      this.emit('disconnected');
    } catch (error) {
      this.emit('disconnectionError', error);
      throw error;
    }
  }

  public getCollection<T extends Document = Document>(collectionName: string): Collection<T> {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    return this.db.collection<T>(collectionName);
  }

  public async findOne<T extends Document>(
    collectionName: string,
    filter: Filter<T>,
    options?: FindOptions
  ): Promise<T | null> {
    try {
      const collection = this.getCollection<T>(collectionName);
      return await collection.findOne(filter, options);
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public async find<T extends Document>(
    collectionName: string,
    filter: Filter<T>,
    options?: FindOptions
  ): Promise<WithId<T>[]> {
    try {
      const collection = this.getCollection<T>(collectionName);
      return await collection.find(filter, options).toArray();
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public async insertOne<T extends Document>(
    collectionName: string,
    document: import('mongodb').OptionalUnlessRequiredId<T>,
    options?: InsertOneOptions & { session?: ClientSession }
  ): Promise<string> {
    try {
      const collection = this.getCollection<T>(collectionName);
      const result = await collection.insertOne(document, options);
      return result.insertedId.toString();
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public async updateOne<T extends Document>(
    collectionName: string,
    filter: Filter<T>,
    update: UpdateFilter<T>,
    options?: UpdateOptions & { session?: ClientSession }
  ): Promise<boolean> {
    try {
      const collection = this.getCollection<T>(collectionName);
      const result = await collection.updateOne(filter, update, options);
      return result.modifiedCount > 0;
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public async deleteOne<T extends Document>(
    collectionName: string, 
    filter: Filter<T>,
    options?: DeleteOptions & { session?: ClientSession }
  ): Promise<boolean> {
    try {
      const collection = this.getCollection<T>(collectionName);
      const result = await collection.deleteOne(filter, options);
      return result.deletedCount > 0;
    } catch (error) {
      this.emit('queryError', error);
      throw error;
    }
  }

  public startSession(): ClientSession {
    return this.client.startSession();
  }

  public async withTransaction<T>(callback: (session: ClientSession) => Promise<T>): Promise<T> {
    const session = this.startSession();
    try {
      let result: T;
      await session.withTransaction(async () => {
        result = await callback(session);
      });
      return result!;
    } finally {
      await session.endSession();
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.client.db().command({ ping: 1 });
      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  public isHealthy(): boolean {
    return this.isConnected;
  }
}

let mongoInstance: MongoDBClient | null = null;

export async function getMongoClient(uri?: string, options?: MongoClientOptions): Promise<MongoDBClient> {
  if (!mongoInstance) {
    if (!uri) {
      throw new Error('MongoDB URI is required for initial connection');
    }
    mongoInstance = MongoDBClient.getInstance(uri, options);
    await mongoInstance.connect();
  }
  return mongoInstance;
}

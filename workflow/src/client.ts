import { ScheduleClient, Connection } from '@temporalio/client';
import { serviceDownWorkflow } from './workflows/serviceDownWorkflow';
import { randomUUID } from 'crypto';
import { config } from './config';

export async function runServiceDownFlow() {
  const connection = await Connection.connect({
    address: config.temporal.url,
  });
  const client = new ScheduleClient({ connection, namespace: 'noc_automation' });
  const handle = await client.create({
    scheduleId: 'service-down-monitoring-' + randomUUID(),
    spec: {
      intervals: [
        {
          every: '1 hour',
        }
      ]
    },
    action: {
      type: 'startWorkflow',
      workflowType: serviceDownWorkflow,
      args: [],
      taskQueue: 'service-down-monitoring',
    },
  });

  console.log(`Started schedule ${handle.scheduleId}`);
}



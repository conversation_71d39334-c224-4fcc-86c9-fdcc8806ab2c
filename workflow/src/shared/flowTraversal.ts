function getStartNode(flow: any) {
    return flow.nodes.find((node: any) => node.type === 'start');
}

function getEndNode(flow: any) {
    return flow.nodes.find((node: any) => node.type === 'end');
}

function getEdgesFromNode(flow: any, nodeId: string) {
    return flow.edges.filter((edge: any) => edge.source === nodeId);
}

function evaluateCondition(conditions: any, context: Record<string, any>) {
    if (!conditions || conditions.length === 0) {
        return true;
    }

    return conditions.every((condition: any) => {
        const { operator, variable, value } = condition;
        const variableValue = context[variable];

        switch (operator) {
            case '==':
                return variableValue === value;
            case '!=':
                return variableValue !== value;
            case '>':
                return variableValue > value;
            case '>=':
                return variableValue >= value;
            case '<':
                return variableValue < value;
            case '<=':
                return variableValue <= value;
            default:
                return false;
        }
    });
}

function getNextNode(currentNode: any, context: Record<string, any>, flow: any) {
    const edges = getEdgesFromNode(flow, currentNode.id);

    if(!edges.length) {
        return null;
    }


    if(currentNode.type === 'decision') {
        const conditions = currentNode.data.conditions;
        const passed = conditions.every((condition: any) => evaluateCondition(condition, context));
        const handle = String(passed);
        const match = edges.find((edge: any) => edge.targetHandle === handle);
        return match ? flow.nodes.find((node: any) => node.id === match.target) : null;
    }

    const match = edges.find((edge: any) => !edge.sourceHandle || edge.sourceHandle === "default");
    return match ? flow.nodes.find((node: any) => node.id === match.target) : null;
}

export { getStartNode, getEndNode, getEdgesFromNode, evaluateCondition, getNextNode };
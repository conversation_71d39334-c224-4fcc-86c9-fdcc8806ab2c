import { getPostgresClient } from "../clients/postgres";


async function getContextByTicket(ticketId: number) {
    try{
        const db = await getPostgresClient();
        const context = await db.query(`SELECT * FROM workflow_context WHERE ticket_id = $1 ORDER BY created_at DESC LIMIT 1`, [ticketId]);
        return context.rows[0];
    }catch(error){
        console.error(error);
        throw error;
    }
}

async function getContextById(contextId: number) {
    try{
        const db = await getPostgresClient();
        const context = await db.query(`SELECT * FROM workflow_context WHERE id = $1`, [contextId]);
        return context.rows[0];
    }catch(error){
        console.error(error);
        throw error;
    }
}

async function updateContext(contextId: number, context: Record<string, any>) {
    try{
        const db = await getPostgresClient();
        const updatedContext = await db.query(`UPDATE workflow_context SET context = context || $1::jsonb WHERE id = $2`, [context, contextId]);
        return updatedContext;
    }catch(error){
        console.error(error);
        throw error;
    }
}

async function updateContextHistory(executionId: number, history: Record<string, any>) {
    try{
        const db = await getPostgresClient();

        const { node_json_id, node_name, node_type, entry_time, exit_time, event_status, context_before_processing, context_after_processing, action_outputs, details } = history;
        const contextHistory = await db.query(`INSERT INTO workflow_execution_history 
            (execution_id, node_json_id, node_name, node_type, entry_time, exit_time, event_status, context_before_processing, context_after_processing, action_outputs, details) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`, 
            [executionId, node_json_id, node_name, node_type, entry_time, exit_time, event_status, context_before_processing, context_after_processing, action_outputs, details]);
        return contextHistory;
    }catch(error){
        console.error(error);
        throw error;
    }
}

export { getContextByTicket, getContextById, updateContext, updateContextHistory };
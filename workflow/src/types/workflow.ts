export interface WorkflowNode {
  id: string;
  type: string;
  data: {
    name: string;
    config: {
      type: 'start' | 'action' | 'decision' | 'wait' | 'end';
      actionType?: 'functions' | 'api' | 'script';
      action?: string;
      inputContext?: string[];
      outputContext?: string[];
      conditions?: Array<{
        variable: string;
        operator: string;
        value: any;
      }>;
      duration?: string;
      timeout?: number;
      retries?: number;
    };
  };
}

export interface WorkflowEdge {
  source: string;
  target: string;
  data: {
    type: 'input' | 'output' | 'true' | 'false';
    label: string;
    contextRef: string;
  };
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  version: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  context: Record<string, any>;
}

export interface WorkflowInstance {
  id: string;
  workflowId: string;
  batchId?: string;
  status: 'running' | 'completed' | 'failed';
  context: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowBatch {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed';
  createdAt: Date;
  updatedAt: Date;
} 
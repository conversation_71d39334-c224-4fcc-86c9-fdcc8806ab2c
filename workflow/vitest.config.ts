// import { defineConfig } from 'vitest'
// import { resolve } from 'path'

// export default defineConfig({
//   test: {
//     globals: true,
//     environment: 'node',
//     setupFiles: ['./src/test/setup.ts'],
//     silent: false,
//     coverage: {
//       provider: 'v8',
//       reporter: ['text', 'json', 'html'],
//       exclude: [
//         'node_modules/',
//         'src/test/',
//         '**/*.d.ts',
//         '**/*.config.*',
//         '**/coverage/**'
//       ]
//     }
//   },
//   resolve: {
//     alias: {
//       '@': resolve(__dirname, './src')
//     }
//   }
// }) 
import { MongoClient, type Db, ObjectId } from 'mongodb';
import { Readable } from 'stream';
import config from '../config';

class MongoRepository {
  private client?: MongoClient;
  private db!: Db;

  async connect(): Promise<void> {
    this.client = new MongoClient(config.MONGODB_URI);
    await this.client.connect();
    this.db = this.client.db();
  }

  collectionStream(collectionName: string): Readable {
    const cursor = this.db.collection(collectionName).find();
    return Readable.from(cursor);
  }

  async getNumbersData(): Promise<any[]> {
    const collection = this.db.collection('numbers');
    const cursor = collection.find();
    const data: any[] = [];
    for await (const doc of cursor) {
      data.push(doc);
    }
    return data;
  }

  /**
   * Buckets numbers based on their type and returns the count for each type
   * @returns {Promise<Record<string, number>>} An object with types as keys and counts as values
   */
  async getNumberCountsByType(companies: []): Promise<Record<string, number>> {
    const collection = this.db.collection('numbers');

    let filter: any = { deleted: false, type: { $ne: 'unassigned' } };

    if (companies.length > 0) {
      filter = { ...filter, company: { $in: companies } };
    }

    const pipeline = [
      {
        $match: filter
      },
      {
        $project: {
          type: 1
        }
      },
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 }
        }
      }
    ];
    
    const result = await collection.aggregate(pipeline).toArray();
    
    const typeCounts: Record<string, number> = {};
    result.forEach(item => {
      typeCounts[item._id || 'undefined'] = item.count;
    });
    
    return typeCounts;
  }

  async getEpikBoxesDetails(userId: string): Promise<any> {
    const user = await this.getUserById(userId);
    let filter = { deleted: false };

    if (!user) {
      throw new Error('User not found');
    }

    const collection = this.db.collection('epikboxes');

    if(user?.role === 'superAdmin') {
    const boxDetails = await Promise.all([
      collection.countDocuments({...filter}),
      collection.countDocuments({...filter, registered: true}),
      collection.countDocuments({...filter, registered: false}),
      collection.countDocuments({...filter, monitor: true }),
      collection.countDocuments({...filter, monitor: false })
    ]);

    return {
        totalBoxes: boxDetails[0],
        registeredBoxes: boxDetails[1],
        unRegisteredBoxes: boxDetails[2],
        monitoredBoxes: boxDetails[3],
        unMonitoredBoxes: boxDetails[4],
      }
    }

    let userCompanies = await this.getCompaniesByUserId(userId);

    userCompanies = [...userCompanies, new ObjectId(user?.company)];

    const boxDetails = await Promise.all([
      collection.countDocuments({...filter, assignedTo: {$in: userCompanies}}),
      collection.countDocuments({...filter, registered: true, assignedTo: {$in: userCompanies} }),
      collection.countDocuments({...filter, registered: false, assignedTo: {$in: userCompanies} }),
      collection.countDocuments({...filter, monitor: true, assignedTo: {$in: userCompanies} }),
      collection.countDocuments({...filter, monitor: false, assignedTo: {$in: userCompanies} })
    ]);

    return {
        totalBoxes: boxDetails[0],
        registeredBoxes: boxDetails[1],
        unRegisteredBoxes: boxDetails[2],
        monitoredBoxes: boxDetails[3],
        unMonitoredBoxes: boxDetails[4],
    }
  }

  async getUserById(userId: string): Promise<any> {
    const collection = this.db.collection('users');
    const user = await collection.findOne({ _id: new ObjectId(userId) });
    if (!user) {
      throw new Error('User not found');
    }
    return user;
  }

  async getCompaniesByUserId(userId: string): Promise<any> {
    const user = await this.getUserById(userId);
    
    if(user?.role === 'superAdmin') {
      return [];
    }

    if(["agent", "admin", "user"]?.includes(user?.role)) {
      return [new ObjectId(user?.company)];
    }

    if(["companyGroupAdmin", "quoteToolAdmin", "supportEngineer"].includes(user?.role)){
      const collection = this.db.collection('companygroups');

      const data = await collection.find({
          '$or': [
            {
              'groupSuperAdmins': {
                '$in': [
                  new ObjectId(user?._id)
                ]
              }
            }, {
              'groupManagers': {
                '$in': [
                  new ObjectId(user?._id)
                ]
              }
            }, {
              'supportEngineers': {
                '$in': [
                  new ObjectId(user?._id)
                ]
              }
            }
          ]
      }).toArray();

      let allManagedCompanies = data?.map(record => record.managedCompanies);
      allManagedCompanies = allManagedCompanies?.flat();
      allManagedCompanies.push(user?.company);

      return allManagedCompanies?.map((company: string) => new ObjectId(company)) || [];
    }

    if(user?.role === "enterpriseAdmin"){
      const collection = this.db.collection('enterprisegroups');

      let data = await collection.find({
      '$or': [
        {
          'memberUsers': {
            '$in': [
              new ObjectId(user?._id)
            ]
          }
        }
      ]
      }).toArray();

      let allManagedCompanies = data?.map(record => record.memberCompanies);
      allManagedCompanies = allManagedCompanies?.flat();
      allManagedCompanies.push(user?.company);

      return allManagedCompanies?.map((company: string) => new ObjectId(company)) || [];
    }

    return [new ObjectId(user?.company)];
  }
}

export default new MongoRepository();

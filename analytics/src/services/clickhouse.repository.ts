import { createClient, type ClickHouseClient } from '@clickhouse/client';
import https from 'https';
import { MetricsQueryParams, WhereCondition, LastValueQueryParams } from '../types';
import config from '../config';

const insecureAgent = new https.Agent({ rejectUnauthorized: false });

interface QueryBuilderOptions {
  select?: string[];
  from: string;
  where?: Array<{
    column: string;
    operator?: string;
    argType?: string;
    value: string | number | boolean | null;
    logicalOperator?: 'AND' | 'OR';
  }>;
  groupBy?: string[];
  orderBy?: Array<{
    column: string;
    direction?: 'ASC' | 'DESC';
  }>;
  limit?: number;
  offset?: number;
  having?: Array<{
    column: string;
    operator?: string;
    value: string | number | boolean;
    logicalOperator?: 'AND' | 'OR';
  }>;
  joins?: Array<{
    table: string;
    type?: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL';
    on: {
      leftColumn: string;
      rightColumn: string;
    };
  }>;
}

class ClickhouseRepository {
  private client: ClickHouseClient;
  constructor() {
    this.client = createClient({
      url: config.CLICKHOUSE_HOST,
      username: config.CLICKHOUSE_USER,
      password: config.CLICKHOUSE_PASSWORD,
      http_agent: insecureAgent,
    });
  }

  async connect(): Promise<void> {
    await this.client.ping();
  }

  async queryRows(sql: string): Promise<any> {
    const result = await this.client.query({ query: sql, format: 'JSONEachRow' });
    return result.json();
  }

  /**
   * Builds a SQL query based on the provided options
   * @param options - Query builder options
   * @returns SQL query string
   */
  buildQuery(options: QueryBuilderOptions): string {
    const selectClause = options.select?.length 
      ? `SELECT\n    ${options.select.join(',\n    ')}` 
      : 'SELECT *';
    
    const fromClause = `FROM ${options.from}`;
    
    const joinClauses = options.joins?.map(join => {
      const joinType = join.type || 'INNER';
      return `${joinType} JOIN ${join.table} ON ${join.on.leftColumn} = ${join.on.rightColumn}`;
    }).join('\n    ') || '';
    
    let whereClause = '';
    if (options.where?.length) {
      const conditions = options.where.map((condition, index) => {
        const operator = condition.operator || '=';
        const value = condition.value === null 
          ? 'NULL' 
          : condition.argType === 'string' && typeof condition.value === 'string'
            ? `'${condition.value.replace(/'/g, "''")}'` 
            : condition.value;
        
        const whereCondition = condition.value === null 
          ? `${condition.column} IS NULL` 
          : `${condition.column} ${operator} ${value}`;
        
        return index === 0 
          ? whereCondition 
          : `${condition.logicalOperator || 'AND'} ${whereCondition}`;
      }).join('\n    ');
      
      whereClause = `WHERE\n    ${conditions}`;
    }
    
    const groupByClause = options.groupBy?.length 
      ? `GROUP BY\n    ${options.groupBy.join(',\n    ')}` 
      : '';
    
    let havingClause = '';
    if (options.having?.length) {
      const conditions = options.having.map((condition, index) => {
        const operator = condition.operator || '=';
        const value = typeof condition.value === 'string'
          ? `'${condition.value.replace(/'/g, "''")}'` 
          : condition.value;
        
        const havingCondition = `${condition.column} ${operator} ${value}`;
        
        return index === 0 
          ? havingCondition 
          : `${condition.logicalOperator || 'AND'} ${havingCondition}`;
      }).join('\n    ');
      
      havingClause = `HAVING\n    ${conditions}`;
    }
    
    const orderByClause = options.orderBy?.length 
      ? `ORDER BY\n    ${options.orderBy.map(order => 
        `${order.column} ${order.direction || 'ASC'}`
      ).join(',\n    ')}` 
      : '';
    
    const limitClause = options.limit !== undefined ? `LIMIT ${options.limit}` : '';
    const offsetClause = options.offset !== undefined ? `OFFSET ${options.offset}` : '';
    
    return [
      selectClause,
      fromClause,
      joinClauses,
      whereClause,
      groupByClause,
      havingClause,
      orderByClause,
      limitClause,
      offsetClause
    ]
    .filter(Boolean)
    .join('\n');
  }

  async getMetricsData(params: MetricsQueryParams): Promise<any> {
    const { columns, serialNumber, startTimestamp, endTimestamp, whereConditions = [], compareColumns = [] } = params;

    // Build the SELECT clause
    const selectColumns = columns.join(',\n    ');

    const lagColumns = compareColumns.map(col => 
      `lagInFrame(${col}, 1) OVER (PARTITION BY serialNumber ORDER BY timestamp ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS prev_${col}`
    ).join(',\n    ');

    const compareConditions = compareColumns
      .map(col => `t.${col} != t.prev_${col}`)
      .join(' OR\n    ');

    // Build additional WHERE conditions
    // const additionalConditions = whereConditions
    //   .map(cond => `${cond.column} = '${cond.value}'`)
    //   .join(' AND\n    ');

    const sql = `SELECT
      ${selectColumns}
  FROM
      (
          SELECT
              *,
              ${lagColumns}
          FROM
              epikbox_data.epik_box_metrics
          WHERE
              serialNumber = '${serialNumber}'
              AND timestamp >= '${startTimestamp}'
          ORDER BY serialNumber, timestamp 
      ) AS t
  WHERE
      ${compareConditions};`;

  console.log(sql)
    return this.queryRows(sql);
  }

  async getLastUnchangedValue(params: LastValueQueryParams): Promise<any> {
    const { columns, serialNumber } = params;
    
    const selectColumns = columns.join(',\n    ');
    const lagColumns = columns.map(col => 
      `leadInFrame(${col}) OVER (ORDER BY timestamp) AS next_${col}`
    ).join(',\n    ');

    const compareConditions = columns
      .map(col => `${col} != next_${col}`)
      .join(' OR ');

    const sql = `
    SELECT timestamp, ${selectColumns}
    FROM (
      SELECT
        timestamp,
        ${selectColumns},
        ${lagColumns}
      FROM epikbox_data.epik_box_metrics
      WHERE serialNumber = '${serialNumber}'
      ORDER BY timestamp DESCs
    ) t
    WHERE (${compareConditions}) AND next_${columns[0]} IS NOT NULL
    ORDER BY timestamp DESC
    LIMIT 1;`;

    console.log('Last value change query:', sql);
    return this.queryRows(sql);
  }

  async getCdrSummary(companies?: []): Promise<any> {
    let monthlyDirectionQuery = {
      select: [
        "'traffic_by_month' AS section",
        "formatDateTime(toStartOfMonth(createdOn), '%b') AS month_abbr",
        "toStartOfMonth(createdOn) AS date_bucket",
        "NULL AS day_bucket",
        "countIf(direction = 'inbound') AS inbound_count",
        "countIf(direction = 'outbound') AS outbound_count",
        "NULL AS callType",
        "NULL AS total_rows",
        "NULL AS total_duration"
      ],
      from: 'epik_cdrs.cdrs',
      where: [
      {
        column: 'createdOn',
        operator: '>=',
        argType: 'expression',
        value: 'toStartOfMonth(now() - INTERVAL 5 MONTH)'
      }],
      groupBy: ['date_bucket', 'month_abbr']
    };

    if(companies?.length) {
      monthlyDirectionQuery.where.push({
        column: 'companyId',
        operator: 'IN',
        argType: 'expression',
        value: `(${companies.map(company => `'${company}'`).join(', ')})`
      });
    }

    const monthlyDirectionSummary = this.buildQuery(monthlyDirectionQuery);

    const dailyDirectionQuery = {
      select: [
        "'past_30_days' AS section",
        "NULL AS month_abbr",
        "today() AS date_bucket",
        "NULL AS day_bucket",
        "countIf(direction = 'inbound') AS inbound_count",
        "countIf(direction = 'outbound') AS outbound_count",
        "NULL AS callType",
        "NULL AS total_rows",
        "NULL AS total_duration"
      ],
      from: 'epik_cdrs.cdrs',
      where: [{
        column: 'createdOn',
        operator: '>=',
        argType: 'expression',
        value: 'today() - INTERVAL 30 DAY'
      }]
    };

    if(companies?.length) {
      dailyDirectionQuery.where.push({
        column: 'companyId',
        operator: 'IN',
        argType: 'expression',
        value: `(${companies.map(company => `'${company}'`).join(', ')})`
      });
    }

    const dailyDirectionSummary = this.buildQuery(dailyDirectionQuery);

    const dailyCallTypeQuery = {
      select: [
        "'line_over_time' AS section",
        "NULL AS month_abbr",
        "toDate(createdOn) AS date_bucket",
        "formatDateTime(createdOn, '%a') AS day_bucket",
        "NULL AS inbound_count",
        "NULL AS outbound_count",
        "callType",
        "count(*) AS total_rows",
        "NULL AS total_duration"
      ],
      from: 'epik_cdrs.cdrs',
      where: [{
        column: 'createdOn',
        operator: '>=',
        argType: 'expression',
        value: 'today() - INTERVAL 6 DAY'
      }],
      groupBy: ['callType', 'date_bucket', 'day_bucket']
    };

    if(companies?.length) {
      dailyCallTypeQuery.where.push({
        column: 'companyId',
        operator: 'IN',
        argType: 'expression',
        value: `(${companies.map(company => `'${company}'`).join(', ')})`
      });
    }

    const dailyCallTypeSummary = this.buildQuery(dailyCallTypeQuery);

    const dailyDirectionTypeQuery = {
      select: [
        "'daily_direction_type' AS section",
        "NULL AS month_abbr",
        "toDate(createdOn) AS date_bucket",
        "formatDateTime(createdOn, '%a') AS day_bucket",
        "NULL AS inbound_count",
        "NULL AS outbound_count",
        "direction AS callType",
        "count(*) AS total_rows",
        "NULL AS total_duration"
      ],
      from: 'epik_cdrs.cdrs',
      where: [{
        column: 'createdOn',
        operator: '>=',
        argType: 'expression',
        value: 'today() - INTERVAL 6 DAY'
      }],
      groupBy: ['direction', 'date_bucket', 'day_bucket']
    };

    if(companies?.length) {
      dailyDirectionTypeQuery.where.push({
        column: 'companyId',
        operator: 'IN',
        argType: 'expression',
        value: `(${companies.map(company => `'${company}'`).join(', ')})`
      });
    }

    const dailyDirectionType = this.buildQuery(dailyDirectionTypeQuery);

    const dailyDurationQuery = {
      select: [
      "'total_usage' AS section",
      "NULL AS month_abbr",
      "toDate(createdOn) AS date_bucket",
      "formatDateTime(createdOn, '%a') AS day_bucket",
      "NULL AS inbound_count",
      "NULL AS outbound_count",
      "NULL AS callType",
      "NULL AS total_rows",
      "sum(duration) / 60 AS total_duration"
      ],
      from: 'epik_cdrs.cdrs',
      where: [{
      column: 'createdOn',
      operator: '>=',
      argType: 'expression',
      value: 'today() - INTERVAL 6 DAY'
      }],
      groupBy: ['date_bucket', 'day_bucket']
    };

    if(companies?.length) {
      dailyDurationQuery.where.push({
        column: 'companyId',
        operator: 'IN',
        argType: 'expression',
        value: `(${companies.map(company => `'${company}'`).join(', ')})`
      });
    }

    const dailyDurationSummary = this.buildQuery(dailyDurationQuery);

    const fullQuery = `${monthlyDirectionSummary}\n\nUNION ALL\n\n${dailyDirectionSummary}\n\nUNION ALL\n\n${dailyCallTypeSummary}\n\nUNION ALL\n\n${dailyDirectionType}\n\nUNION ALL\n\n${dailyDurationSummary}\n\nORDER BY section, date_bucket;`;
    
    console.log('Executing CDR summary query:', fullQuery);
    
    return this.queryRows(fullQuery);
  }
}

export default new ClickhouseRepository();

# SIM API Endpoints Documentation

This document provides details on the endpoints used for SIM verification and
activation as part of the device configuration process, incorporating
discoveries from implementation and testing.

## Base URL

```
https://mnujjcwkpj.execute-api.us-east-1.amazonaws.com/Prod/api
```

## 1. Authentication Endpoint

Used to obtain a bearer token for subsequent API calls. Tokens are cached for
10 minutes to optimize performance.

### Request

**URL**: `/auth/login`

**Method**: `POST`

**Headers**:
```
Content-Type: application/json
```

**Body**:
```json
{
  "email": "YOUR_EMAIL_ADDRESS",
  "password": "YOUR_PASSWORD"
}
```

### Response

**Success (200 OK)**:
```json
{
  "data": {
    "token": "Bearer XYZ"  // Note: "Bearer" prefix is already included
  },
  "result": {
    "status": "success",
    "messages": [
      {
        "code": "Success",
        "gatewayMessage": "Request completed successfully."
      }
    ]
  }
}
```

**Error - Invalid Credentials (401 Unauthorized)**:
```json
{
  "result": {
    "status": "failure",
    "messages": [
      {
        "code": "Input_Invalid_Username_Password",
        "gatewayMessage": "Invalid username or password. Please try again.",
        "resolutionOwner": "Granite"
      }
    ]
  }
}
```

**Error - Malformed JSON (400 Bad Request)**:
```json
{
  "result": {
    "status": "failure",
    "messages": [
      {
        "code": "General_Request_Format_Invalid",
        "gatewayMessage": "The JSON in the request is not well formed. Please ensure that commas, colons, braces etc. are formatted properly.",
        "resolutionOwner": "Granite"
      }
    ]
  }
}
```

## 2. Subscriber Inquiry Endpoint

Used to query carrier systems about SIM card status and retrieve
associated information.

### Request

**URL**: `/subscriber-inquiry`

**Method**: `POST`

**Headers**:
```
Authorization: <CACHED_TOKEN>  // Use the full token from authentication response
Content-Type: application/json
```

**Body**:
```json
{
  "header": {
    "carrierKey": "VZW",  // "VZW", "TMO", or "POD19IOT"
    "callbackRequired": false
  },
  "requestDetail": [
    {
      "connection": {
        "iccid": "YOUR_SIM_ICCID"
      }
    }
  ]
}
```

### Response

**Success**:
```json
{
  "responseDetail": {
    "connection": {
      "iccid": "SIM_ICCID",
      "simType": 1
    },
    "subscriberStatus": "Active",  // Key field for determining activation
    // various other fields with subscriber and device info
  },
  "carrierKey": "VZW",
  "requestType": "SubscriberInquiry",
  "transactionDateTime": "2025-03-21T14:10:07",
  "transactionKey": "2644376",
  "result": {
    "status": "success"
  }
}
```

**Failure**:
```json
{
  "responseDetail": {
    "connection": {
      "iccid": "SIM_ICCID",
      "simType": 1
    }
  },
  "carrierKey": "VZW",
  "requestType": "SubscriberInquiry",
  "transactionDateTime": "2025-03-21T14:10:07",
  "transactionKey": "2644376",
  "result": {
    "status": "failure",
    "messages": [
      {
        "code": "Request_Failed_Resource_Not_Found",
        "gatewayMessage": "Resource not found - Invalid input. Please try again.",
        "resolutionOwner": "Granite"
      }
    ]
  }
}
```

## 3. Subscriber Activation Endpoint

Used to activate SIM cards through carrier systems.

### Request

**URL**: `/subscriber-activation`

**Method**: `POST`

**Headers**:
```
Authorization: <CACHED_TOKEN>
Content-Type: application/json
```

**Body**:
```json
{
  "header": {
    "carrierKey": "VZW",  // "VZW", "TMO", or "POD19IOT"
    "callbackRequired": false
  },
  "requestDetail": [
    {
      "connection": {
        "iccid": "YOUR_SIM_ICCID"
      }
      // Additional activation parameters may be required based on carrier
    }
  ]
}
```

### Response

**Success**:
```json
{
  "responseDetail": {
    "activationStatus": "Success",  // Key field for determining success
    "connection": {
      "iccid": "SIM_ICCID"
    }
    // Additional activation details
  },
  "carrierKey": "VZW",
  "requestType": "SubscriberActivation",
  "transactionDateTime": "2025-03-21T14:10:07",
  "transactionKey": "2644377",
  "result": {
    "status": "success"
  }
}
```

## Carrier Key Detection and Logic

### Automatic Carrier Detection

The system uses proper ICCID parsing to determine the correct carrier based
on ITU-T standards. ICCIDs follow the format:

  `89` (industry) + `CC` (country) + `NNN` (network/provider)
    + `XXXXXXXXX` (subscriber ID) + `C` (check digit).

**US Carriers (Country Code 01):**
- **AT&T (`POD19IOT`)**: Provider codes `310`, `410`, `030`, `150` (IoT)
- **Verizon (`VZW`)**: Provider codes `004`, `012` (IoT)
- **T-Mobile (`TMO`)**: Provider codes `260`, `240` (IoT)

**Alternative US Formats:**
- **T-Mobile/Sprint Legacy**: Country code `31` (any provider code)
- **Verizon Alternative**: Country code `14` (any provider code)

**Canadian Carriers (Country Code 302):**
- **Telus**: Provider code `220` (falls back to `VZW` for API compatibility)

**Validation Rules:**
- ICCID must be 18-22 digits
- Must start with `89` (telecommunications industry identifier)
- Must contain only numeric characters
- Invalid ICCIDs default to `VZW` for fallback compatibility

### Multi-Carrier Checking Strategy

For SIM activation verification, the system uses an optimized approach:

1. **Primary Check**: Query the carrier determined by proper ICCID parsing first
2. **Fallback Check**: For VZW-detected SIMs, also check POD19IOT if the primary
   check fails (handles edge cases where Verizon SIMs might be provisioned
   through AT&T systems)
3. **Early Success**: Return `true` as soon as any carrier reports the SIM
   as active

This approach reduces API calls while ensuring comprehensive coverage across
carriers, with intelligent carrier detection replacing the previous
length-based heuristic.

## Error Handling

### HTTP Status Codes

- **400 Bad Request**: Invalid request format or parameters - logged but
  execution continues
- **401/403 Unauthorized/Forbidden**: Authentication failure - throws error
  and stops execution
- **Other errors**: Logged and execution continues with next carrier

### Token Management

- Tokens are cached for 10 minutes to minimize authentication requests
- Authentication is performed automatically before each carrier query
- Token expiry is checked before each use and refreshed as needed

## Implementation Best Practices

### Performance Optimization

1. **Token Caching**: Reuse authentication tokens for 10 minutes
2. **Smart Carrier Detection**: Check most likely carrier first
3. **Early Termination**: Stop checking additional carriers once activation
   is confirmed
4. **Error Resilience**: Continue checking other carriers even if one fails

### Logging and Debugging

The implementation includes comprehensive logging:
- Full request/response logging for both success and error cases
- Specific error handling for different HTTP status codes
- Detailed error messages for troubleshooting

### Usage Example

```javascript
// Check if a SIM is activated across all relevant carriers
const isActivated = await isSimActivated(sim);

// Check activation status with a specific carrier
const isActiveWithVerizon = await isSimActivatedWithCarrier(sim, 'VZW');

// Activate a SIM with a specific carrier
const activationSuccess = await activateSim(sim, 'VZW', additionalParams);
```

## Key Discoveries from Implementation

1. **Proper ICCID Parsing**: Implemented ITU-T standard ICCID parsing using
   industry (89) + country + provider codes for accurate carrier detection,
   replacing unreliable length-based heuristics
2. **Comprehensive Carrier Mapping**: Support for all major US carriers
   (AT&T, Verizon, T-Mobile) including IoT variants and legacy Sprint codes,
   plus Canadian Telus support
3. **Cross-Carrier Compatibility**: Some Verizon-detected SIMs may actually
   be AT&T, requiring fallback checking for edge cases
4. **Token Efficiency**: Caching tokens for 10 minutes significantly reduces
   authentication overhead
5. **Error Tolerance**: 400 errors should be logged but not stop the process,
   as they often indicate SIM not found with that specific carrier
6. **Response Structure Consistency**: Both inquiry and activation endpoints
   follow similar response patterns with `responseDetail` containing the key
   status information
7. **Input Validation**: Robust validation ensures ICCIDs meet telecom
   standards before processing

## Migration Notes

When updating from the original documentation:
- Add the subscriber activation endpoint
- Update carrier detection logic to reflect ICCID length-based approach
- Include token caching implementation details
- Document the multi-carrier checking strategy
- Add comprehensive error handling patterns

import mongoose, { Schema } from "mongoose";

const { ObjectId, Mixed } = Schema.Types;

// Carrier gateway API call for SIM activation.
//
const qcSimActivationSchema = new mongoose.Schema(
  {
    device: { type: ObjectId, ref: "EpikBox" },
    config: { type: ObjectId, ref: "QcDeviceConfig" },
    slot: { type: Number, enum: [1, 2] },
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'inactive', 'completed', 'failed', 'suspended'],
      default: 'pending'
    },
    startedAt: { type: Date, required: false },
    completedAt: { type: Date, required: false },
    request: Schema.Types.Mixed,
    response: Schema.Types.Mixed,
    success: { type: Boolean, default: false },
    reason: String,
  },
  {
    timestamps: {
      createdAt: true,
      updatedAt: false,
    },
  }
);

qcSimActivationSchema.index({ config: 1, createdAt: -1 });
qcSimActivationSchema.index({ device: 1, slot: 1, createdAt: -1 });
qcSimActivationSchema.index({ device: 1, status: 1, createdAt: -1 });

const QcSimActivationModel = mongoose.model('QcSimActivation', qcSimActivationSchema);

export default QcSimActivationModel;

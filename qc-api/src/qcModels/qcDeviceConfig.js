import mongoose, { Schema } from "mongoose";

const { ObjectId } = Schema.Types;

// Device configuration info for QC only.
//
const qcDeviceConfigSchema = new mongoose.Schema(
  {
    device: { type: ObjectId, ref: "EpikBox" },
    user: { type: ObjectId, ref: "User" },
    plan: {
      type: String,
      enum: ["standard", "public", "private"]
    },
    region: {
      type: String,
      required: true,
      validate: {
        validator: function (value) {
          return /^\d{5}$/.test(value);
        },
        message: "Region ZIP code must be 5 digits",
      },
    },
    autoTest: {
      type: Boolean,
      default: false
    },
  },
  {
    timestamps: {
      createdAt: true,
      updatedAt: false,
    },
  }
);

// Get latest attempt for a device
qcDeviceConfigSchema.index({ device: 1, createdAt: -1 });

const QcDeviceConfigModel = mongoose.model('QcDeviceConfig', qcDeviceConfigSchema);

export default QcDeviceConfigModel;

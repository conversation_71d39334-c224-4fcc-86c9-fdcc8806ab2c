/**
 * ICCID Carrier Detection Module
 *
 * Implements ITU-T standard ICCID parsing to determine carrier keys
 * for SIM API endpoints. Throws descriptive errors for unknown patterns
 * to facilitate adding new carrier support.
 */

export const CARRIER_KEYS = {
  ATT: 'POD19IOT',
  VERIZON: 'VZW',
  TMOBILE: 'TMO'
};

/**
 * Parse and validate ICCID structure
 * @param {string} iccid - The ICCID to parse
 * @returns {object} Parsed ICCID components
 * @throws {Error} For invalid ICCID format
 */
export function parseICCID(iccid) {
  if (!iccid || typeof iccid !== 'string') {
    throw new Error('ICCID must be a non-empty string');
  }

  const clean = iccid.replace(/\s+/g, '');

  // Basic validation
  if (clean.length < 18 || clean.length > 22) {
    throw new Error(`Invalid ICCID length: ${clean.length} digits (must be 18-22)`);
  }

  if (!/^\d+$/.test(clean)) {
    throw new Error(`Invalid ICCID format: contains non-numeric characters`);
  }

  // Must start with 89 (telecom industry)
  if (!clean.startsWith('89')) {
    throw new Error(`Invalid ICCID industry code: must start with 89 (telecom)`);
  }

  const industryCode = clean.substring(0, 2);
  const countryCode = clean.substring(2, 4);
  const providerCode = clean.substring(4, 7);

  // Handle Canadian format (3-digit country code)
  if (clean.substring(2, 5) === '302') {
    return {
      industryCode,
      countryCode: '302',
      providerCode: clean.substring(5, 8),
      format: 'canadian',
      clean
    };
  }

  return {
    industryCode,
    countryCode,
    providerCode,
    format: 'standard',
    clean
  };
}

/**
 * Determine carrier key from ICCID
 * @param {string} iccid - The ICCID to analyze
 * @returns {string} Carrier key (POD19IOT, VZW, TMO)
 * @throws {Error} For unknown carrier patterns
 */
export function toCarrierKey(iccid) {
  const parsed = parseICCID(iccid);
  const { countryCode, providerCode, clean } = parsed;

  // US carriers (country code 01)
  if (countryCode === '01') {
    // AT&T family
    if (['310', '410', '030', '150'].includes(providerCode)) {
      return CARRIER_KEYS.ATT;
    }

    // Verizon family
    if (['004', '012'].includes(providerCode)) {
      return CARRIER_KEYS.VERIZON;
    }

    // T-Mobile family
    if (['260', '240'].includes(providerCode)) {
      return CARRIER_KEYS.TMOBILE;
    }

    // Unknown US provider
    throw new Error(`Unknown US carrier provider code: ${providerCode} in ICCID: ${iccid}`);
  }

  // T-Mobile (Sprint legacy, country code 31)
  if (countryCode === '31') {
    return CARRIER_KEYS.TMOBILE;
  }

  // Verizon alternative (country code 14)
  if (countryCode === '14') {
    return CARRIER_KEYS.VERIZON;
  }

  // Canadian carriers (country code 302)
  if (countryCode === '302') {
    const canadianProvider = providerCode;
    if (canadianProvider === '220') {
      throw new Error(`Canadian Telus SIM detected but no carrier key available: ${iccid}`);
    }
    throw new Error(`Unknown Canadian carrier provider code: ${canadianProvider} in ICCID: ${iccid}`);
  }

  // Completely unknown format
  throw new Error(`Unknown ICCID country/format: Country code ${countryCode} in ICCID: ${iccid}`);
}

/**
 * Get carrier information including display name and color
 * @param {string} iccid - The ICCID to analyze
 * @returns {object} Carrier information
 */
export function getCarrierInfo(iccid) {
  const carrierKey = toCarrierKey(iccid);
  const parsed = parseICCID(iccid);

  const carrierMap = {
    [CARRIER_KEYS.ATT]: {
      key: CARRIER_KEYS.ATT,
      name: 'AT&T',
      isIoT: ['150'].includes(parsed.providerCode)
    },
    [CARRIER_KEYS.VERIZON]: {
      key: CARRIER_KEYS.VERIZON,
      name: 'Verizon',
      isIoT: ['012'].includes(parsed.providerCode)
    },
    [CARRIER_KEYS.TMOBILE]: {
      key: CARRIER_KEYS.TMOBILE,
      name: 'T-Mobile',
      isIoT: ['240'].includes(parsed.providerCode),
      isSprint: parsed.countryCode === '31'
    }
  };

  const info = carrierMap[carrierKey];
  if (!info) {
    throw new Error(`Unknown carrier key: ${carrierKey}`);
  }

  // Enhance name for special cases
  let displayName = info.name;
  if (info.isIoT) {
    displayName += ' IoT';
  } else if (info.isSprint) {
    displayName += ' (Sprint)';
  }

  return {
    ...info,
    displayName,
    country: parsed.countryCode === '302' ? 'CA' : 'US',
    format: parsed.format
  };
}

/**
 * Validate if ICCID is supported by our carrier detection
 * @param {string} iccid - The ICCID to validate
 * @returns {boolean} True if supported, false otherwise
 */
export function isICCIDSupported(iccid) {
  try {
    toCarrierKey(iccid);
    return true;
  } catch (error) {
    return false;
  }
}

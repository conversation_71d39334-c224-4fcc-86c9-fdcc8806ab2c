
import {
  parseICCID,
  toCarrier<PERSON>ey,
  getCarrierInfo,
  isICCIDSupported,
  CARRIER_KEYS
} from './simCarriers.js';

describe('parseICCID', () => {
  it('should parse valid US ICCID correctly', () => {
    const result = parseICCID('8901310123456789012');
    expect(result).toEqual({
      industryCode: '89',
      countryCode: '01',
      providerCode: '310',
      format: 'standard',
      clean: '8901310123456789012'
    });
  });

  it('should parse Canadian ICCID correctly', () => {
    const result = parseICCID('89302220123456789012');
    expect(result).toEqual({
      industryCode: '89',
      countryCode: '302',
      providerCode: '220',
      format: 'canadian',
      clean: '89302220123456789012'
    });
  });

  it('should handle ICCID with spaces', () => {
    const result = parseICCID('89 01 310 123 456 789 012');
    expect(result.clean).toBe('8901310123456789012');
    expect(result.providerCode).toBe('310');
  });

  it('should throw error for null/undefined ICCID', () => {
    expect(() => parseICCID(null)).toThrow('ICCID must be a non-empty string');
    expect(() => parseICCID(undefined)).toThrow('ICCID must be a non-empty string');
    expect(() => parseICCID('')).toThrow('ICCID must be a non-empty string');
  });

  it('should throw error for invalid length', () => {
    expect(() => parseICCID('123456789')).toThrow('Invalid ICCID length: 9 digits (must be 18-22)');
    expect(() => parseICCID('12345678901234567890123')).toThrow('Invalid ICCID length: 23 digits (must be 18-22)');
  });

  it('should throw error for non-numeric characters', () => {
    expect(() => parseICCID('8901310abcdef789012')).toThrow('Invalid ICCID format: contains non-numeric characters');
  });

  it('should throw error for invalid industry code', () => {
    expect(() => parseICCID('1201310123456789012')).toThrow('Invalid ICCID industry code: must start with 89 (telecom)');
  });
});

describe('toCarrierKey', () => {
  describe('AT&T carriers', () => {
    it('should detect AT&T main (310)', () => {
      expect(toCarrierKey('8901310123456789012')).toBe(CARRIER_KEYS.ATT);
    });

    it('should detect AT&T (410)', () => {
      expect(toCarrierKey('8901410123456789012')).toBe(CARRIER_KEYS.ATT);
    });

    it('should detect AT&T (030)', () => {
      expect(toCarrierKey('8901030123456789012')).toBe(CARRIER_KEYS.ATT);
    });

    it('should detect AT&T IoT (150)', () => {
      expect(toCarrierKey('8901150123456789012')).toBe(CARRIER_KEYS.ATT);
    });
  });

  describe('Verizon carriers', () => {
    it('should detect Verizon main (004)', () => {
      expect(toCarrierKey('8901004123456789012')).toBe(CARRIER_KEYS.VERIZON);
    });

    it('should detect Verizon IoT (012)', () => {
      expect(toCarrierKey('8901012123456789012')).toBe(CARRIER_KEYS.VERIZON);
    });

    it('should detect Verizon alternative format (14)', () => {
      expect(toCarrierKey('8914800123456789012')).toBe(CARRIER_KEYS.VERIZON);
    });
  });

  describe('T-Mobile carriers', () => {
    it('should detect T-Mobile main (260)', () => {
      expect(toCarrierKey('8901260123456789012')).toBe(CARRIER_KEYS.TMOBILE);
    });

    it('should detect T-Mobile IoT (240)', () => {
      expect(toCarrierKey('8901240123456789012')).toBe(CARRIER_KEYS.TMOBILE);
    });

    it('should detect T-Mobile Sprint legacy (31)', () => {
      expect(toCarrierKey('8931000123456789012')).toBe(CARRIER_KEYS.TMOBILE);
    });
  });

  describe('Error cases', () => {
    it('should throw error for unknown US provider', () => {
      expect(() => toCarrierKey('8901999123456789012')).toThrow('Unknown US carrier provider code: 999');
    });

    it('should throw error for Canadian Telus', () => {
      expect(() => toCarrierKey('89302220123456789012')).toThrow('Canadian Telus SIM detected but no carrier key available');
    });

    it('should throw error for unknown Canadian provider', () => {
      expect(() => toCarrierKey('89302999123456789012')).toThrow('Unknown Canadian carrier provider code: 999');
    });

    it('should throw error for unknown country code', () => {
      expect(() => toCarrierKey('8999123456789012345')).toThrow('Unknown ICCID country/format: Country code 99');
    });

    it('should propagate parseICCID errors', () => {
      expect(() => toCarrierKey('invalid')).toThrow('Invalid ICCID length');
    });
  });
});

describe('getCarrierInfo', () => {
  it('should return complete AT&T info', () => {
    const info = getCarrierInfo('8901310123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.ATT,
      name: 'AT&T',
      displayName: 'AT&T',
      color: '#555555',
      isIoT: false,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return AT&T IoT info', () => {
    const info = getCarrierInfo('8901150123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.ATT,
      name: 'AT&T',
      displayName: 'AT&T IoT',
      color: '#555555',
      isIoT: true,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return Verizon info', () => {
    const info = getCarrierInfo('8901004123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.VERIZON,
      name: 'Verizon',
      displayName: 'Verizon',
      color: '#CC9988',
      isIoT: false,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return Verizon IoT info', () => {
    const info = getCarrierInfo('8901012123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.VERIZON,
      name: 'Verizon',
      displayName: 'Verizon IoT',
      color: '#CC9988',
      isIoT: true,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return T-Mobile info', () => {
    const info = getCarrierInfo('8901260123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.TMOBILE,
      name: 'T-Mobile',
      displayName: 'T-Mobile',
      color: '#C4889F',
      isIoT: false,
      isSprint: false,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return T-Mobile IoT info', () => {
    const info = getCarrierInfo('8901240123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.TMOBILE,
      name: 'T-Mobile',
      displayName: 'T-Mobile IoT',
      color: '#C4889F',
      isIoT: true,
      isSprint: false,
      country: 'US',
      format: 'standard'
    });
  });

  it('should return T-Mobile Sprint info', () => {
    const info = getCarrierInfo('8931000123456789012');
    expect(info).toEqual({
      key: CARRIER_KEYS.TMOBILE,
      name: 'T-Mobile',
      displayName: 'T-Mobile (Sprint)',
      color: '#C4889F',
      isIoT: false,
      isSprint: true,
      country: 'US',
      format: 'standard'
    });
  });
});

describe('isICCIDSupported', () => {
  it('should return true for supported ICCIDs', () => {
    expect(isICCIDSupported('8901310123456789012')).toBe(true);
    expect(isICCIDSupported('8901004123456789012')).toBe(true);
    expect(isICCIDSupported('8901260123456789012')).toBe(true);
    expect(isICCIDSupported('8931000123456789012')).toBe(true);
  });

  it('should return false for unsupported ICCIDs', () => {
    expect(isICCIDSupported('8901999123456789012')).toBe(false);
    expect(isICCIDSupported('89302220123456789012')).toBe(false);
    expect(isICCIDSupported('invalid')).toBe(false);
    expect(isICCIDSupported(null)).toBe(false);
  });
});

describe('Edge cases and real-world scenarios', () => {
  it('should handle different ICCID lengths within valid range', () => {
    expect(toCarrierKey('890131012345678901')).toBe(CARRIER_KEYS.ATT); // 18 digits
    expect(toCarrierKey('8901310123456789012')).toBe(CARRIER_KEYS.ATT); // 19 digits
    expect(toCarrierKey('89013101234567890123')).toBe(CARRIER_KEYS.ATT); // 20 digits
    expect(toCarrierKey('890131012345678901234')).toBe(CARRIER_KEYS.ATT); // 21 digits
    expect(toCarrierKey('8901310123456789012345')).toBe(CARRIER_KEYS.ATT); // 22 digits
  });

  it('should handle mixed whitespace', () => {
    expect(toCarrierKey('89 01310 123456789012')).toBe(CARRIER_KEYS.ATT);
    expect(toCarrierKey('89\t01\n310\r123456789012')).toBe(CARRIER_KEYS.ATT);
  });

  it('should provide descriptive error messages', () => {
    expect(() => toCarrierKey('8901888123456789012')).toThrow(/Unknown US carrier provider code: 888.*8901888123456789012/);
    expect(() => toCarrierKey('8977123456789012345')).toThrow(/Unknown ICCID country\/format: Country code 77.*8977123456789012345/);
  });
});

describe('Integration with existing codebase', () => {
  it('should be compatible with existing CARRIER_KEYS usage', () => {
    expect(CARRIER_KEYS.ATT).toBe('POD19IOT');
    expect(CARRIER_KEYS.VERIZON).toBe('VZW');
    expect(CARRIER_KEYS.TMOBILE).toBe('TMO');
  });

  it('should work as drop-in replacement for original toCarrierKey', () => {
    // Test cases that would have worked with original length-based logic
    const tmobileICCID = '8931000123456789012'; // 19 digits, Sprint legacy
    const verizonICCID = '8901004123456789012'; // 19 digits, but actually Verizon

    expect(toCarrierKey(tmobileICCID)).toBe('TMO');
    expect(toCarrierKey(verizonICCID)).toBe('VZW');
  });
});

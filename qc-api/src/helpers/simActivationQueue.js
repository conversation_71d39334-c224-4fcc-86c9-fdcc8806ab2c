import Queue from 'better-queue';
import { processDeviceActivation } from './SimActivationWorker.js';

// Create SIM activation queue
const simActivationQueue = new Queue(async (job, cb) => {
  try {
    console.log('Processing SIM activation job:', job.deviceId);

    const result = await processDeviceActivation({
      deviceId: job.deviceId,
      configId: job.configId,
      plan: job.plan,
      region: job.region,
      user: job.user,
      token: job.token, // Pass token for auto-test functionality
      env: job.env
    });

    console.log('SIM activation completed:', result.success);
    cb(null, result);
  } catch (error) {
    console.error('SIM activation failed:', error.message);
    cb(error);
  }
}, {
  concurrent: 10,
  retries: 2,
  retryDelay: 30000,
});

// Add queue event listeners for monitoring
simActivationQueue.on('task_finish', (taskId, result) => {
  console.log(`SIM activation completed for task ${taskId}`);
});

simActivationQueue.on('task_failed', (taskId, err) => {
  console.error(`SIM activation failed for task ${taskId}:`, err.message);
});

/**
 * Queue a SIM activation job
 * @param {Object} activationData - The activation parameters
 * @param {string} activationData.deviceId - Device ID
 * @param {string} activationData.configId - Config ID (optional)
 * @param {string} activationData.plan - SIM plan
 * @param {string} activationData.region - Region (optional)
 * @param {string} activationData.user - User ID
 * @param {string} activationData.token - Auth token (optional)
 * @param {Object} activationData.env - SIM API environment config
 * @returns {string} Job ID
 */
export const queueSimActivation = (activationData) => {
  console.log('=== QUEUE FUNCTION: Received data ===', {
    hasEnv: !!activationData.env,
    apiUrl: activationData.env?.apiUrl ? `${activationData.env.apiUrl.substring(0, 20)}...` : 'UNDEFINED',
    username: activationData.env?.username || 'UNDEFINED',
    passwordSet: !!activationData.env?.password,
    passwordLength: activationData.env?.password ? activationData.env.password.length : 0,
    hasToken: !!activationData.token
  });

  const jobId = simActivationQueue.push(activationData);
  console.log(`Queued SIM activation job for device:`, activationData.deviceId);
  return jobId;
};

/**
 * Get queue statistics
 * @returns {Object} Queue stats
 */
export const getQueueStats = () => {
  return {
    length: simActivationQueue.length,
    running: simActivationQueue.running,
  };
};

export default simActivationQueue;

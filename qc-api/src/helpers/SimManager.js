/**
 * SIM Manager Class
 * Handles SIM activation, status checking, and authentication
 */

import axios from 'axios';
import { getCarrierInfo } from './simCarriers.js';

export default class SimManager {
  constructor({ apiUrl, username, password }) {
    this.apiUrl = apiUrl;
    this.username = username;
    this.password = password;

    // Token cache
    this.tokenCache = {
      token: null,
      expiry: 0
    };

    // Add instance ID for logging multiple concurrent managers
    this.instanceId = Math.random().toString(36).substr(2, 6);
    this.deviceInfo = null; // Will be set later via setDeviceInfo()
    console.log(`SimManager instance created: ${this.instanceId}`);
  }

  /**
   * Set device information for better logging context
   * Should be called after instantiation but before activation
   */
  setDeviceInfo({ deviceId, serialNumber, slot }) {
    this.deviceInfo = { deviceId, serialNumber, slot };
    console.log(`[${this.instanceId}] Device context set: Device ${deviceId}, Serial ${serialNumber}, SIM ${slot}`);
  }

  /**
   * Get logging prefix with device context
   */
  getLogPrefix() {
    if (this.deviceInfo) {
      return `[${this.instanceId}|${this.deviceInfo.serialNumber}|SIM${this.deviceInfo.slot}]`;
    }
    return `[${this.instanceId}]`;
  }

  /**
   * Authenticate with the SIM API and cache the token
   */
  async authenticate() {
    const now = Date.now();
    if (!this.tokenCache.token || this.tokenCache.expiry <= now) {
      const authStart = Date.now();
      try {
        console.log(`${this.getLogPrefix()} Starting authentication...`);

        // ADD DETAILED DEBUG LOGGING
        const authPayload = {
          email: this.username,
          password: this.password,
        };

        console.log(`${this.getLogPrefix()} Auth details:`, {
          url: `${this.apiUrl}/auth/login`,
          email: authPayload.email,
          passwordLength: authPayload.password ? authPayload.password.length : 0,
          // Check for common issues
          emailHasSpace: authPayload.email?.includes(' '),
          passwordHasSpace: authPayload.password?.includes(' '),
          passwordStartsWith: authPayload.password ? authPayload.password.substring(0, 2) : 'NONE',
          passwordEndsWith: authPayload.password ? authPayload.password.substring(authPayload.password.length - 2) : 'NONE',
        });

        const response = await axios.post(`${this.apiUrl}/auth/login`, authPayload);

        this.tokenCache.token = response.data.data.token;
        this.tokenCache.expiry = now + 10 * 60 * 1000; // 10 minutes

        const authDuration = Date.now() - authStart;
        console.log(`${this.getLogPrefix()} Authentication successful in ${authDuration}ms`);
      } catch (error) {
        const authDuration = Date.now() - authStart;
        console.error(`${this.getLogPrefix()} Authentication failed after ${authDuration}ms:`, {
          errorMessage: error.message,
          httpStatus: error.response?.status,
          httpStatusText: error.response?.statusText,
          responseData: error.response?.data ? JSON.stringify(error.response.data, null, 2) : 'none'
        });
        throw new Error(`Authentication failed: ${error.message}`);
      }
    } else {
      console.log(`${this.getLogPrefix()} Using cached authentication token (expires in ${Math.floor((this.tokenCache.expiry - now) / 1000)}s)`);
    }
  }

  /**
   * Check SIM status - Modified to handle 404 errors gracefully
   */
  async checkSimStatus(iccid, carrierCode) {
    const statusStart = Date.now();
    console.log(`${this.getLogPrefix()} Checking SIM status for carrier ${carrierCode}`);

    try {
      const response = await axios.post(`${this.apiUrl}/subscriber-inquiry`, {
        header: {
          carrierKey: carrierCode,
          callbackRequired: false,
        },
        requestDetail: [{
          connection: { iccid }
        }]
      }, {
        headers: {
          'Authorization': this.tokenCache.token,
          'Content-Type': 'application/json'
        }
      });

      const statusDuration = Date.now() - statusStart;

      // Handle both response formats: responseDetail as object or array
      const statusDetails = Array.isArray(response?.data?.responseDetail)
        ? response.data.responseDetail[0]
        : response.data.responseDetail;

      const isActive = statusDetails?.subscriberStatus === 'Active';

      console.log(`${this.getLogPrefix()} SIM status check completed in ${statusDuration}ms:`, {
        carrier: carrierCode,
        subscriberStatus: statusDetails?.subscriberStatus || 'Unknown',
        isActive,
        hasStaticIP: !!statusDetails?.staticIP,
        hasMSISDN: !!statusDetails?.connection?.msisdn
      });

      return {
        isActive,
        statusDetails,
        fullResponse: response.data,
        querySuccessful: true
      };
    } catch (error) {
      const statusDuration = Date.now() - statusStart;

      // Handle 404 specifically - SIM might not be provisioned yet but could still be activated
      if (error.response?.status === 404) {
        console.log(`${this.getLogPrefix()} SIM not found in system (404) after ${statusDuration}ms - normal for unprovisioned SIMs`);
        return {
          isActive: false,
          statusDetails: null,
          fullResponse: null,
          querySuccessful: false,
          notFound: true,
          error: 'SIM not found (404)'
        };
      }

      // Log other errors with details
      console.error(`${this.getLogPrefix()} SIM status check failed after ${statusDuration}ms:`, {
        carrier: carrierCode,
        errorMessage: error.message,
        httpStatus: error.response?.status,
        httpStatusText: error.response?.statusText,
        responseData: error.response?.data ? JSON.stringify(error.response.data, null, 2) : 'none'
      });

      // For other errors, still return a structured response but mark as failed
      return {
        isActive: false,
        statusDetails: null,
        fullResponse: null,
        querySuccessful: false,
        error: error.message
      };
    }
  }

  /**
   * Prepare carrier-specific activation parameters
   */
  prepareActivationParams({ iccid, imei, region, plan, carrierInfo }) {
    console.log(`${this.getLogPrefix()} Preparing activation params for ${carrierInfo.key}:`, {
      carrier: carrierInfo.key,
      carrierName: carrierInfo.name,
      plan,
      region: region || 'none',
      hasIMEI: !!imei
    });

    const params = {
      connection: { iccid }
    };

    // Add IMEI if provided
    if (imei) {
      params.connection.imei = imei;
    }

    // Set carrier-specific parameters
    switch (carrierInfo.key) {
      case "POD19IOT":
        if (carrierInfo.countryCode === "CA") {
          params.plan = "Granite POD19 - IoT - LTE/SMS MO/MT_11166 USCANMX";
        } else {
          params.plan = "Granite POD19 - IoT - 0MB NA Tb6";
          params.communicationPlan = "Granite POD19 - IoT - LTE/SMS MO/MT_11166 AT&T US";
        }
        console.log(`${this.getLogPrefix()} POD19IOT plan selected: ${params.plan}`);
        break;

      case "VZW":
        if (plan === "public") {
          params.plan = "EPIK-FWA-STATIC";
        } else {
          params.plan = "M2M 51694 STATIC";
        }
        const zipCode = this.getZipCodeForRegion(region);
        if (zipCode) {
          params.zipCode = zipCode;
          console.log(`${this.getLogPrefix()} Verizon region mapped: ${region} -> ${zipCode}`);
        }
        params.vzwSku = "VZW120003650022";
        params.request = "Single";
        console.log(`${this.getLogPrefix()} Verizon plan selected: ${params.plan} with ZIP: ${zipCode || 'none'}`);
        break;

      case "TMO":
        params.plan = "Granite Failover Internet Access";
        params.ngp = "GEO";
        params.products = ["SMSO", "WHSTIPV4"];
        params.request = "Single";
        console.log(`${this.getLogPrefix()} T-Mobile plan selected: ${params.plan}`);
        break;

      case "TELUS":
        params.communicationPlan = "pse.telus.iot";
        console.log(`${this.getLogPrefix()} TELUS plan selected: ${params.communicationPlan}`);
        break;

      default:
        console.warn(`${this.getLogPrefix()} Unknown carrier code: ${carrierInfo.key} - using default plan`);
        params.plan = "DEFAULT";
    }

    return params;
  }

  /**
   * Map region to ZIP code for Verizon
   * Accepts either:
   * - 5-digit ZIP code strings (passed through)
   * - Region city keys (mapped to ZIP codes)
   * - Invalid/non-5-digit values (treated as null)
   */
  getZipCodeForRegion(region) {
    if (!region || typeof region !== 'string') {
      return null;
    }

    // If it's already a 5-digit ZIP code, use it
    if (/^\d{5}$/.test(region)) {
      return region;
    }

    // Map region city keys to ZIP codes
    const regionMap = {
      'los_angeles': '90222',
      'dallas': '75201',
      'chicago': '60629',
      'boston': '02124'
    };

    // If it's a known region key, return the mapped ZIP
    if (regionMap[region]) {
      return regionMap[region];
    }

    // For anything else (like "other", invalid strings), return null
    console.warn(`${this.getLogPrefix()} Invalid region '${region}' - treating as no region specified`);
    return null;
  }

  /**
   * Send activation request
   */
  async sendActivationRequest(iccid, carrierCode, params) {
    const activationStart = Date.now();
    console.log(`${this.getLogPrefix()} Sending activation request to carrier ${carrierCode}...`);

    try {
      const requestBody = {
        header: {
          carrierKey: carrierCode,
          callbackRequired: true, // Use async activation with polling
        },
        requestDetail: [params]
      };

      const response = await axios.post(`${this.apiUrl}/activate`, requestBody, {
        headers: {
          'Authorization': this.tokenCache.token,
          'Content-Type': 'application/json'
        },
        validateStatus: () => true // Accept any status to analyze response
      });

      const activationDuration = Date.now() - activationStart;

      // Check for success (202 Accepted is expected for async)
      if (response.status === 202 && response.data?.result?.status === "success") {
        console.log(`${this.getLogPrefix()} Activation request accepted in ${activationDuration}ms:`, {
          carrier: carrierCode,
          transactionKey: response.data.transactionKey,
          httpStatus: response.status
        });

        return {
          success: true,
          transactionKey: response.data.transactionKey,
          data: response.data
        };
      } else {
        const errorMsg = response.data?.message || response.data?.error || `Status: ${response.status}`;

        console.error(`${this.getLogPrefix()} Activation request failed in ${activationDuration}ms:`, {
          carrier: carrierCode,
          httpStatus: response.status,
          httpStatusText: response.statusText,
          errorMessage: errorMsg,
          responseData: response.data ? JSON.stringify(response.data, null, 2) : 'none'
        });

        return {
          success: false,
          error: errorMsg,
          data: response.data
        };
      }
    } catch (error) {
      const activationDuration = Date.now() - activationStart;

      console.error(`${this.getLogPrefix()} Activation request error in ${activationDuration}ms:`, {
        carrier: carrierCode,
        errorMessage: error.message,
        errorCode: error.code,
        httpStatus: error.response?.status,
        httpStatusText: error.response?.statusText,
        responseData: error.response?.data ? JSON.stringify(error.response.data, null, 2) : 'none'
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Poll for activation status using transaction key with improved timing and 404 handling
   */
  async pollForActivation(iccid, carrierCode, transactionKey, maxAttempts = 20) {
    // Improved timing strategy based on real-world observations:
    // - Most activations complete between 30-60 seconds
    // - 404 errors are normal in the first 1-2 polls for unprovisioned SIMs
    // - Status often goes: 404 -> "Unknown" -> "Active"
    const getIntervalForAttempt = (attempt) => {
      if (attempt <= 2) return 10000;       // 10s x 2 = 20s (expect 404s here)
      if (attempt <= 5) return 15000;       // 15s x 3 = 45s more (65s total - prime activation window)
      if (attempt <= 12) return 10000;      // 10s x 7 = 70s more (135s total)
      return 20000;                         // 20s for extended waits
    };

    console.log(`${this.getLogPrefix()} Starting activation polling for carrier ${carrierCode} (max ${maxAttempts} attempts)`);
    const pollingStart = Date.now();

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const attemptStart = Date.now();

      try {
        console.log(`${this.getLogPrefix()} Polling attempt ${attempt}/${maxAttempts} for carrier ${carrierCode}`);

        const response = await axios.post(`${this.apiUrl}/subscriber-inquiry`, {
          header: {
            carrierKey: carrierCode,
            callbackRequired: false,
            transactionKey,
          },
          requestDetail: [{
            connection: { iccid }
          }]
        }, {
          headers: {
            'Authorization': this.tokenCache.token,
            'Content-Type': 'application/json'
          }
        });

        const attemptDuration = Date.now() - attemptStart;

        if (response.data?.result?.status === "success") {
          // Handle both response formats: responseDetail as object or array
          const statusDetails = Array.isArray(response.data.responseDetail)
            ? response.data.responseDetail[0]
            : response.data.responseDetail;

          const subscriberStatus = statusDetails?.subscriberStatus;

          console.log(`${this.getLogPrefix()} Polling response in ${attemptDuration}ms - Status: ${subscriberStatus || 'Unknown'}`);

          if (subscriberStatus === "Active") {
            const totalPollingTime = Date.now() - pollingStart;
            console.log(`${this.getLogPrefix()} SIM activation completed! Total polling time: ${totalPollingTime}ms over ${attempt} attempts`);

            return {
              success: true,
              subscriberStatus,
              staticIP: statusDetails.staticIP || null,
              apn: statusDetails.apn || null,
              planInfo: statusDetails.planInfo || null,
              msisdn: statusDetails.connection?.msisdn || null,
              statusDetails: response.data
            };
          }

          // Continue polling if not active yet
          if (subscriberStatus === "Activating" || subscriberStatus === "Pending") {
            console.log(`${this.getLogPrefix()} Activation in progress (${subscriberStatus})...`);
          } else if (subscriberStatus && subscriberStatus !== "Active") {
            console.log(`${this.getLogPrefix()} Current status: ${subscriberStatus} - continuing to poll...`);
          }
        } else {
          console.log(`${this.getLogPrefix()} API response indicates failure or unexpected format in ${attemptDuration}ms`);
          console.log(`${this.getLogPrefix()} Result status:`, response.data?.result?.status);

          // Check if there's an error message
          if (response.data?.result?.message) {
            console.log(`${this.getLogPrefix()} Error message:`, response.data.result.message);
          }
        }
      } catch (error) {
        const attemptDuration = Date.now() - attemptStart;

        // Handle 404 errors gracefully during polling - they're expected early in the process
        if (error.response?.status === 404) {
          if (attempt <= 3) {
            console.log(`${this.getLogPrefix()} 404 on attempt ${attempt} in ${attemptDuration}ms - SIM still being provisioned (normal)`);
          } else {
            console.warn(`${this.getLogPrefix()} 404 on attempt ${attempt} in ${attemptDuration}ms - SIM provisioning taking longer than expected`);
          }
        } else {
          console.warn(`${this.getLogPrefix()} Polling error (attempt ${attempt}) in ${attemptDuration}ms:`, {
            errorMessage: error.message,
            errorCode: error.code,
            httpStatus: error.response?.status,
            httpStatusText: error.response?.statusText
          });
        }

        if (error.response?.data) {
          console.log(`${this.getLogPrefix()} Error response data:`, JSON.stringify(error.response.data, null, 2));
        }
      }

      // Wait before next attempt (except on last attempt)
      if (attempt < maxAttempts) {
        const intervalMs = getIntervalForAttempt(attempt);
        console.log(`${this.getLogPrefix()} Waiting ${intervalMs/1000} seconds before next attempt...`);
        await new Promise(resolve => setTimeout(resolve, intervalMs));
      }
    }

    const totalPollingTime = Date.now() - pollingStart;
    console.error(`${this.getLogPrefix()} Activation polling timed out after ${totalPollingTime}ms (${maxAttempts} attempts)`);

    return {
      success: false,
      error: "Activation polling timed out"
    };
  }

  /**
   * Main activation function - Updated with 404 handling
   */
  async activateSIM({ iccid, imei, region, plan }) {
    const activationStart = Date.now();

    try {
      // Validate inputs
      if (!iccid) {
        throw new Error("ICCID is required");
      }

      // Determine carrier from ICCID
      const carrierInfo = getCarrierInfo(iccid);
      if (!carrierInfo) {
        throw new Error(`Invalid ICCID or unsupported carrier: ${iccid}`);
      }

      console.log(`${this.getLogPrefix()} Processing ${carrierInfo.displayName || carrierInfo.name} SIM: ${iccid}`);

      // Authenticate
      await this.authenticate();

      // MODIFIED DECISION TREE: Check current status but proceed with activation on 404
      console.log(`${this.getLogPrefix()} Checking current SIM status...`);
      const currentStatus = await this.checkSimStatus(iccid, carrierInfo.key);

      // Handle different status check outcomes
      if (currentStatus.querySuccessful && currentStatus.isActive) {
        // SIM IS ALREADY ACTIVE - Return existing activation
        console.log(`${this.getLogPrefix()} SIM is already activated`);
        /*
        return {
          success: true,
          alreadyActive: true,
          staticIP: currentStatus.statusDetails?.staticIP || null,
          msisdn: currentStatus.statusDetails?.connection?.msisdn || null,
          subscriberStatus: currentStatus.statusDetails?.subscriberStatus,
          statusDetails: currentStatus.fullResponse
        };
        */
      } else {
        // SIM IS NOT ACTIVE OR NOT FOUND (including 404) - Proceed with activation
        if (currentStatus.notFound) {
          console.log(`${this.getLogPrefix()} SIM not found in system (404) - proceeding with activation anyway...`);
        } else if (!currentStatus.querySuccessful) {
          console.log(`${this.getLogPrefix()} Status check failed (${currentStatus.error}) - proceeding with activation anyway...`);
        } else {
          console.log(`${this.getLogPrefix()} SIM is not active - proceeding with activation...`);
        }

        // Prepare activation parameters
        const activationParams = this.prepareActivationParams({
          iccid,
          imei,
          region,
          plan,
          carrierInfo
        });

        // Send activation request
        const activationResult = await this.sendActivationRequest(
          iccid,
          carrierInfo.key,
          activationParams
        );

        if (!activationResult.success) {
          const error = new Error(activationResult.error);
          error.activationResponse = activationResult.data;
          error.initialStatusCheck = {
            querySuccessful: currentStatus.querySuccessful,
            notFound: currentStatus.notFound,
            error: currentStatus.error
          };
          throw error;
        }

        console.log(`${this.getLogPrefix()} Activation request accepted. Transaction key: ${activationResult.transactionKey}`);

        // Poll for completion
        const pollingResult = await this.pollForActivation(
          iccid,
          carrierInfo.key,
          activationResult.transactionKey
        );

        if (!pollingResult.success) {
          const error = new Error(pollingResult.error);
          error.transactionKey = activationResult.transactionKey;
          error.activationResponse = activationResult.data;
          error.initialStatusCheck = {
            querySuccessful: currentStatus.querySuccessful,
            notFound: currentStatus.notFound,
            error: currentStatus.error
          };
          throw error;
        }

        const totalActivationTime = Date.now() - activationStart;
        console.log(`${this.getLogPrefix()} SIM activation completed successfully in ${totalActivationTime}ms`);

        return {
          success: true,
          staticIP: pollingResult.staticIP,
          apn: pollingResult.apn,
          msisdn: pollingResult.msisdn,
          subscriberStatus: pollingResult.subscriberStatus,
          planInfo: pollingResult.planInfo,
          transactionKey: activationResult.transactionKey,
          statusDetails: pollingResult.statusDetails,
          wasNotFoundInitially: currentStatus.notFound
        };
      }

    } catch (error) {
      const totalActivationTime = Date.now() - activationStart;
      console.error(`${this.getLogPrefix()} SIM activation failed after ${totalActivationTime}ms:`, {
        errorMessage: error.message,
        errorName: error.name,
        errorCode: error.code
      });

      // Create SIMError for consistent error handling
      const simError = new Error(error.message);
      simError.name = 'SIMError';
      simError.code = 'ACTIVATION_FAILED';

      // Preserve additional error context
      if (error.activationResponse) {
        simError.activationResponse = error.activationResponse;
      }
      if (error.transactionKey) {
        simError.transactionKey = error.transactionKey;
      }
      if (error.initialStatusCheck) {
        simError.initialStatusCheck = error.initialStatusCheck;
      }

      throw simError;
    }
  }
}

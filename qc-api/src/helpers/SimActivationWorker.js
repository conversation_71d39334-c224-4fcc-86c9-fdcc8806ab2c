/**
 * SIM Activation Worker
 *
 * Handles the business logic for device SIM activation and auto-testing
 */

import SimManager from './SimManager.js';
import { getCarrierInfo } from './simCarriers.js';
import simActivationController from '../controllers/SimActivationController.js';
import deviceConfigController from '../controllers/DeviceConfigController.js';
import { getDevice } from "../controllers/qcControllers.js";
import { updateEpikBoxSimData } from "../controllers/epikBoxSimController.js";
import { runTest, getDeviceDetails, moveDeviceToStage } from '../controllers/qcControllers.js';

/**
 * Process device activation with dual SIM support and auto-testing
 *
 * @param {Object} params - Activation parameters
 * @param {string} params.deviceId - Device ID
 * @param {string} params.configId - Config ID (optional, will create if not provided)
 * @param {string} params.plan - Communication plan
 * @param {string} params.region - Region for Verizon SIMs (applies to both slots if applicable)
 * @param {string} params.user - User ID who initiated the activation
 * @param {string} params.token - Auth token for test runner (optional)
 * @param {Object} params.env - SIM API environment config
 * @returns {Promise<Object>} Activation result
 */
async function processDeviceActivation({ deviceId, configId, plan, region, user, token, env }) {
  console.log('processDeviceActivation called with:', {
    deviceId,
    configId: configId || 'NOT PROVIDED',
    plan,
    region: region || 'NOT PROVIDED',
    user,
    hasToken: !!token,
    hasEnv: !!env,
    envKeys: env ? Object.keys(env) : []
  });

  let sim1Record = null;
  let sim2Record = null;
  let autoTestResult = null;

  try {
    // Validate env early
    if (!env || !env.apiUrl || !env.username || !env.password) {
      throw new Error('Missing SIM API credentials in env object');
    }

    // Get device details from database
    const device = await getDevice(deviceId);
    if (!device) {
      throw new Error('Device not found');
    }

    // Extract required fields
    const imei = device.imei;

    // Clean SIM data and update DB if needed
    const sims = await ensureCleanSimData(device);

    // Validate device data structure
    if (!imei) {
      throw new Error('Device has no IMEI');
    }

    if (!sims) {
      throw new Error('Device has no SIM data');
    }

    if (!Array.isArray(sims)) {
      throw new Error('Device SIM data is invalid format');
    }

    if (sims.length !== 2) {
      throw new Error('Device must have exactly 2 SIM slots');
    }

    if (!sims[0] || typeof sims[0] !== 'string') {
      throw new Error('Device has invalid SIM 1 data');
    }

    if (!sims[1] || typeof sims[1] !== 'string') {
      throw new Error('Device has invalid SIM 2 data');
    }

    const sim1Iccid = sims[0];
    const sim2Iccid = sims[1];

    // Validate SIM carrier requirements
    let sim1CarrierInfo, sim2CarrierInfo;
    try {
      sim1CarrierInfo = getCarrierInfo(sim1Iccid);
      sim2CarrierInfo = getCarrierInfo(sim2Iccid);

      // Verizon region validation for SIM 1
      if (sim1CarrierInfo.key === 'VZW' && !region) {
        throw new Error('Verizon SIM 1 requires region selection');
      }

      // SIM 2 validation - now accepts any carrier
      console.log(`SIM 2 carrier detected: ${sim2CarrierInfo.name} (${sim2CarrierInfo.key})`);

      // Verizon region validation for SIM 2 if applicable
      if (sim2CarrierInfo.key === 'VZW' && !region) {
        throw new Error('Verizon SIM 2 requires region selection');
      }

      // Only warn about unused region if NEITHER SIM is Verizon
      if (region && sim1CarrierInfo.key !== 'VZW' && sim2CarrierInfo.key !== 'VZW') {
        console.warn(`Region '${region}' provided but no Verizon SIMs detected - will be ignored`);
      }

    } catch (carrierError) {
      throw new Error(`Invalid SIM format: ${carrierError.message}`);
    }

    // If no configId is passed, create a new config
    if (!configId) {
      const configResult = await deviceConfigController.createConfig({
        device: deviceId,
        plan: plan,
        ...(region && { region }),
        user,
      });

      if (!configResult.success) {
        throw new Error(`Failed to create device config: ${configResult.message}`);
      }

      configId = configResult.data._id;
    }

    // Create initial activation records (Phase 1)
    const sim1RequestData = {
      iccid: sim1Iccid,
      imei,
      ...(region && { region }),
      plan
    };

    sim1Record = await simActivationController.createActivation({
      device: deviceId,
      config: configId,
      slot: 1,
      request: sim1RequestData,
      success: false  // Will be updated after activation
    });

    const sim2RequestData = {
      iccid: sim2Iccid,
      imei,
      ...(sim2CarrierInfo.key === 'VZW' && region && { region }),
      plan
    };

    sim2Record = await simActivationController.createActivation({
      device: deviceId,
      config: configId,
      slot: 2,
      request: sim2RequestData,
      success: false
    });

    // NOW we've passed all pre-validation - mark as started (Phase 1.5)
    await simActivationController.markAsStarted(sim1Record.data._id);
    await simActivationController.markAsStarted(sim2Record.data._id);

    // Prepare activation configuration
    const activationConfig = {
      serialNumber: device.serial,
      imei,
      sim1: {
        iccid: sim1Iccid,
        region: region || null
      },
      sim2: {
        iccid: sim2Iccid,
        region: sim2CarrierInfo.key === 'VZW' ? region || null : null  // Only use region for Verizon SIMs
      },
      plan,
      env,
    };

    // Execute device activation (Phase 2) - SimManager instances created inside
    console.log(`Starting dual SIM activation for device ${deviceId}:`, {
      sim1: { carrier: getCarrierInfo(sim1Iccid).name, iccid: sim1Iccid },
      sim2: { carrier: getCarrierInfo(sim2Iccid).name, iccid: sim2Iccid },
      plan,
      region: region || 'none'
    });

    const activationStart = Date.now();
    const activationResult = await activateDevice(activationConfig);
    const activationDuration = Date.now() - activationStart;

    console.log(`Dual SIM activation completed in ${activationDuration}ms:`, {
      device: deviceId,
      sim1Success: activationResult.sim1.success,
      sim2Success: activationResult.sim2.success,
      overallSuccess: activationResult.sim1.success && activationResult.sim2.success
    });

    // Update activation records with results (Phase 3)
    await simActivationController.updateActivationResult(sim1Record.data._id, {
      response: activationResult.sim1.details,
      success: activationResult.sim1.success,
      reason: activationResult.sim1.success ? null : activationResult.sim1.message
    });

    await simActivationController.updateActivationResult(sim2Record.data._id, {
      response: activationResult.sim2.details,
      success: activationResult.sim2.success,
      reason: activationResult.sim2.success ? null : activationResult.sim2.message
    });

    // Mark activations as completed (Phase 3.5)
    await simActivationController.markAsCompleted(sim1Record.data._id, activationResult.sim1);
    await simActivationController.markAsCompleted(sim2Record.data._id, activationResult.sim2);

    // Determine overall success
    const overallSuccess = activationResult.sim1.success && activationResult.sim2.success;

    // **NEW: Handle auto-testing if both SIMs activated successfully (Phase 4)**
    if (overallSuccess) {
      autoTestResult = await handleAutoTesting(deviceId, configId, user, token);
    }

    // Build response
    const response = {
      success: overallSuccess,
      device: deviceId,
      serialNumber: device.serial,
      activations: {
        sim1: {
          success: activationResult.sim1.success,
          message: activationResult.sim1.message,
          recordId: sim1Record.data._id,
          staticIP: activationResult.sim1.details?.staticIP || null,
          msisdn: activationResult.sim1.details?.msisdn || null
        },
        sim2: {
          success: activationResult.sim2.success,
          message: activationResult.sim2.message,
          recordId: sim2Record.data._id,
          staticIP: activationResult.sim2.details?.staticIP || null,
          msisdn: activationResult.sim2.details?.msisdn || null
        }
      },
      // Include auto-test results if they exist
      ...(autoTestResult && { autoTest: autoTestResult })
    };

    // Log results
    console.log('Device activation completed:', {
      device: deviceId,
      serialNumber: device.serial,
      success: overallSuccess,
      sim1Success: activationResult.sim1.success,
      sim2Success: activationResult.sim2.success,
      autoTestTriggered: !!autoTestResult
    });

    return response;

  } catch (error) {
    console.error('Device activation failed:', error.message);

    // Handle records that were created but activation never started
    if (sim1Record && sim2Record) {
      try {
        // Check current status of records to determine appropriate action
        const sim1Current = await simActivationController.getLatestActivationForDeviceSlot(deviceId, 1);
        const sim2Current = await simActivationController.getLatestActivationForDeviceSlot(deviceId, 2);

        const sim1Status = sim1Current.data?.status;
        const sim2Status = sim2Current.data?.status;

        // If records are still 'pending', mark as 'inactive' (never attempted)
        if (sim1Status === 'pending') {
          await simActivationController.markAsInactive(sim1Record.data._id, `Pre-activation failure: ${error.message}`);
        }
        // If records are 'in_progress', mark as 'failed' (attempted but failed)
        else if (sim1Status === 'in_progress') {
          await simActivationController.markAsCompleted(sim1Record.data._id, {
            success: false,
            message: `Activation failed: ${error.message}`,
            details: { error: error.message }
          });
        }

        // Same for SIM 2
        if (sim2Status === 'pending') {
          await simActivationController.markAsInactive(sim2Record.data._id, `Pre-activation failure: ${error.message}`);
        }
        else if (sim2Status === 'in_progress') {
          await simActivationController.markAsCompleted(sim2Record.data._id, {
            success: false,
            message: `Activation failed: ${error.message}`,
            details: { error: error.message }
          });
        }

      } catch (cleanupError) {
        console.error('Failed to cleanup activation records:', cleanupError.message);
        // Don't throw cleanup errors, just log them
      }
    }

    // Re-throw the original error
    throw error;
  }
}

/**
 * Handle auto-testing logic after successful SIM activation
 * @param {string} deviceId - Device ID
 * @param {string} configId - Config ID
 * @param {string} user - User ID
 * @param {string} token - Auth token (optional)
 * @returns {Promise<Object|null>} Auto-test result or null if not enabled
 */
async function handleAutoTesting(deviceId, configId, user, token) {
  try {
    console.log(`Checking auto-test configuration for device ${deviceId}`);

    // Get the device configuration to check autoTest flag
    const configResult = await deviceConfigController.getLatestConfigForDevice(deviceId);

    if (!configResult.success || !configResult.data) {
      console.log(`No configuration found for device ${deviceId}, skipping auto-test`);
      return null;
    }

    const config = configResult.data;

    // Check if autoTest is enabled
    if (!config.autoTest) {
      console.log(`Auto-test disabled for device ${deviceId}, skipping`);
      return null;
    }

    console.log(`Auto-test enabled for device ${deviceId}, proceeding with sequential stage move then test`);

    const autoTestResult = {
      enabled: true,
      stageMove: { success: false },
      testStart: { success: false }
    };

    // STEP 1: Move device to software stage first
    try {
      const stageResult = await moveDeviceToStage({
        idOrSerial: deviceId,
        stage: 'software', // Using 'software' stage name
        user: user
      });

      console.log(`Device ${deviceId} successfully moved to software stage:`, stageResult);
      autoTestResult.stageMove = {
        success: true,
        result: stageResult
      };
    } catch (stageError) {
      console.error(`Failed to move device ${deviceId} to software stage:`, stageError.message);
      autoTestResult.stageMove = {
        success: false,
        error: stageError.message
      };

      // If stage move fails, don't attempt to start test
      console.log(`Skipping test start for device ${deviceId} due to stage move failure`);
      return autoTestResult;
    }

    // STEP 2: Only start test if stage move was successful
    if (autoTestResult.stageMove.success) {
      try {
        console.log(`Stage move completed successfully, now starting test for device ${deviceId}`);

        // Get device details for the test
        const device = await getDeviceDetails(deviceId);

        const test = await runTest({
          device,
          user: user,
          token: token || null
        });

        console.log(`Test successfully started for device ${deviceId}:`, test);
        autoTestResult.testStart = {
          success: true,
          testId: test.id || test._id,
          result: test
        };
      } catch (testError) {
        console.error(`Failed to start test for device ${deviceId}:`, testError.message);
        autoTestResult.testStart = {
          success: false,
          error: testError.message
        };
      }
    }

    return autoTestResult;

  } catch (error) {
    console.error(`Error in handleAutoTesting for device ${deviceId}:`, error.message);
    return {
      enabled: true,
      error: error.message,
      stageMove: { success: false },
      testStart: { success: false }
    };
  }
}

/**
 * Helper function to activate device with dual SIM support
 * Creates separate SimManager instances to avoid token cache conflicts
 * @param {Object} config - Device activation configuration
 * @returns {Promise<Object>} Activation results for both SIMs
 */
async function activateDevice({ serialNumber, imei, sim1, sim2, plan, env }) {
  console.log(`Creating SimManager instances for sequential activation`);

  const sim1Manager = new SimManager(env);
  const sim2Manager = new SimManager(env);

  // Set device context
  sim1Manager.setDeviceInfo({
    deviceId: serialNumber,
    serialNumber,
    slot: 1
  });

  sim2Manager.setDeviceInfo({
    deviceId: serialNumber,
    serialNumber,
    slot: 2
  });

  console.log(`Starting sequential SIM activations:`, {
    sim1: { iccid: sim1.iccid, carrier: getCarrierInfo(sim1.iccid).key, region: sim1.region },
    sim2: { iccid: sim2.iccid, carrier: getCarrierInfo(sim2.iccid).key, region: sim2.region }
  });

  // Track timing
  const overallStart = Date.now();
  let sim1Result, sim2Result;

  // Track individual durations
  let sim1Duration = 0;
  let sim2Duration = 0;

  // ACTIVATE SIM 1
  try {
    console.log('=== ACTIVATING SIM 1 ===');
    const sim1Start = Date.now();

    const result = await sim1Manager.activateSIM({
      iccid: sim1.iccid,
      imei,
      plan,
      ...(sim1.region && { region: sim1.region })
    });

    sim1Duration = Date.now() - sim1Start;  // Store duration
    console.log(`SIM 1 activation completed in ${sim1Duration}ms`);

    sim1Result = {
      status: 'fulfilled',
      value: result
    };
  } catch (error) {
    sim1Duration = Date.now() - overallStart;  // Store duration
    console.error(`SIM 1 activation failed after ${sim1Duration}ms:`, error.message);

    sim1Result = {
      status: 'rejected',
      reason: error
    };
  }

  // Small delay between activations
  console.log('Waiting 1 second before SIM 2 activation...');
  await new Promise(resolve => setTimeout(resolve, 1000));

  // ACTIVATE SIM 2
  try {
    console.log('=== ACTIVATING SIM 2 ===');
    const sim2Start = Date.now();

    const result = await sim2Manager.activateSIM({
      iccid: sim2.iccid,
      imei,
      plan,
      ...(sim2.region && { region: sim2.region })
    });

    sim2Duration = Date.now() - sim2Start;  // Store duration
    console.log(`SIM 2 activation completed in ${sim2Duration}ms`);

    sim2Result = {
      status: 'fulfilled',
      value: result
    };
  } catch (error) {
    sim2Duration = Date.now() - overallStart;  // Store duration
    console.error(`SIM 2 activation failed after ${sim2Duration}ms:`, error.message);

    sim2Result = {
      status: 'rejected',
      reason: error
    };
  }

  const totalDuration = Date.now() - overallStart;
  console.log(`Total sequential activation time: ${totalDuration}ms`);

  // Handles either the response from an activation, or the response telling
  // us that the SIM has already been activated.
  //
  const formatResult = (result, simName, duration) => {
    if (result.status === 'fulfilled') {
      const value = result.value || {};

      console.log(`${simName} activation successful:`, {
        duration: `${duration}ms`,
        staticIP: value.staticIP || 'none',        // ← Now safe
        msisdn: value.msisdn || 'none',            // ← Now safe
        subscriberStatus: value.subscriberStatus   // ← Now safe
      });

      return {
        success: true,
        message: `${simName} activated successfully`,
        details: {
          // Use 'value' instead of 'result.value' throughout
          staticIP: value.staticIP || null,
          msisdn: value.msisdn || null,
          subscriberStatus: value.subscriberStatus || null,
          apn: value.apn || null,
          planInfo: value.planInfo || null,
          transactionKey: value.transactionKey || null,
          alreadyActive: value.alreadyActive || false,
          wasNotFoundInitially: value.wasNotFoundInitially || false,
          statusDetails: value.statusDetails || null
        }
      };
    } else {
      // For failures, capture the full error object and any response data
      const error = result.reason;

      // Enhanced error logging for carrier gateway insights
      console.error(`${simName} activation failed:`, {
        duration: `${duration}ms`,
        errorMessage: error.message,
        errorName: error.name,
        httpStatus: error.response?.status,
        httpStatusText: error.response?.statusText,
        apiErrorCode: error.response?.data?.code || error.response?.data?.errorCode,
        apiErrorMessage: error.response?.data?.message || error.response?.data?.error,
        carrierResponse: error.response?.data ? JSON.stringify(error.response.data, null, 2) : 'none'
      });

      // Check for specific error patterns that indicate carrier gateway issues
      if (error.response?.status === 429) {
        console.warn(`RATE LIMIT detected for ${simName} - carrier gateway throttling`);
      } else if (error.response?.status >= 500) {
        console.warn(`CARRIER GATEWAY ERROR for ${simName} - server-side issue (${error.response.status})`);
      } else if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND') {
        console.warn(`NETWORK ERROR for ${simName} - connectivity issue (${error.code})`);
      } else if (error.message.includes('timeout')) {
        console.warn(`TIMEOUT ERROR for ${simName} - carrier gateway slow response`);
      }

      // Try to extract the full response data (removing HTTP/Axios debris)
      let responseData = null;
      if (error.response?.data) {
        // This is likely an Axios error with response data
        responseData = error.response.data;
      } else if (error.data) {
        // Direct response data
        responseData = error.data;
      } else if (error.response) {
        // Full response object - extract just the data parts
        responseData = {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        };
      }

      return {
        success: false,
        message: `${simName} activation failed: ${result.reason.message}`,
        details: responseData || { error: result.reason.message }
      };
    }
  };

  return {
    sim1: formatResult(sim1Result, 'SIM 1', sim1Duration),
    sim2: formatResult(sim2Result, 'SIM 2', sim2Duration),
  };
}

async function updateDeviceSimData(deviceId, cleanedSims) {
  const [cleanedSim1, cleanedSim2] = cleanedSims;

  try {
    const result = await updateEpikBoxSimData(deviceId, {
      sim1: cleanedSim1,
      sim2: cleanedSim2
    });

    console.log(`SIM update result for device ${deviceId}:`, result.message);
    return result.modifiedCount > 0;

  } catch (error) {
    console.error(`Failed to update SIM data for device ${deviceId}:`, error.message);
    throw error;
  }
}

/**
 * Clean ICCID by removing trailing 'F' characters
 */
function cleanIccid(iccid) {
  if (!iccid || typeof iccid !== 'string') {
    return iccid;
  }
  return iccid.replace(/F+$/i, '');
}

/**
 * Clean device SIM data and update database if needed
 */
async function ensureCleanSimData(device) {
  const originalSim1 = device.sims[0];
  const originalSim2 = device.sims[1];

  // cleanIccid() does the F-stripping
  const cleanedSim1 = cleanIccid(originalSim1);
  const cleanedSim2 = cleanIccid(originalSim2);

  const needsUpdate = (originalSim1 !== cleanedSim1 || originalSim2 !== cleanedSim2);

  if (needsUpdate) {
    console.log(`Cleaning SIMs for device ${device.id}: [${originalSim1}, ${originalSim2}] -> [${cleanedSim1}, ${cleanedSim2}]`);

    // updateEpikBoxSimData() saves the cleaned values to DB
    await updateDeviceSimData(device.id, [cleanedSim1, cleanedSim2]);
  }

  return [cleanedSim1, cleanedSim2];
}

export {
  processDeviceActivation
};

import QcDeviceConfigModel from '../qcModels/qcDeviceConfig.js';

class DeviceConfigController {
  /**
   * Create a new QC device configuration record
   * @param {Object} configData - The configuration data
   * @param {string} configData.device - Device ObjectId
   * @param {string} configData.user - User ObjectId
   * @param {string} configData.plan - Plan type ('standard', 'public', 'private')
   * @param {string} configData.region - 5-digit ZIP code
   * @param {boolean} configData.autoTest - Whether to auto-run tests
   * @returns {Promise<Object>} The created configuration record
   * @throws {Error} Validation or database errors
   */
  async createConfig(configData) {
    try {
      const { device, user, plan, region, autoTest } = configData;

      // Validate required fields
      if (!device) {
        throw new Error('Device ID is required');
      }

      if (!user) {
        throw new Error('User ID is required');
      }

      // Plan is now REQUIRED
      if (!plan) {
        throw new Error('Plan is required');
      }

      // Validate plan enum
      const validPlans = ['standard', 'public', 'private'];
      if (plan && !validPlans.includes(plan)) {
        throw new Error(`Plan must be one of: ${validPlans.join(', ')}`);
      }

      // Create new configuration record
      const newConfig = new QcDeviceConfigModel({
        device,
        user,
        plan,
        region,
        autoTest
      });

      const savedConfig = await newConfig.save();

      return {
        success: true,
        data: savedConfig,
        message: 'QC device configuration created successfully'
      };

    } catch (error) {
      // Handle Mongoose validation errors
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map(err => err.message);
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Handle duplicate key errors
      if (error.code === 11000) {
        throw new Error('Duplicate configuration detected');
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get the latest configuration for a specific device
   * @param {string} deviceId - Device ObjectId
   * @returns {Promise<Object>} The latest configuration record or null
   */
  async getLatestConfigForDevice(deviceId) {
    try {
      if (!deviceId) {
        throw new Error('Device ID is required');
      }

      const latestConfig = await QcDeviceConfigModel
        .findOne({ device: deviceId })
        .sort({ createdAt: -1 })
        .populate('device')
        .populate('user');

      return {
        success: true,
        data: latestConfig,
        message: latestConfig ? 'Latest configuration found' : 'No configuration found for device'
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all configurations for a specific device (ordered by creation date)
   * @param {string} deviceId - Device ObjectId
   * @param {Object} options - Query options
   * @param {number} options.limit - Limit number of results
   * @param {number} options.skip - Skip number of results
   * @returns {Promise<Object>} Array of configuration records
   */
  async getConfigHistoryForDevice(deviceId, options = {}) {
    try {
      if (!deviceId) {
        throw new Error('Device ID is required');
      }

      const { limit = 10, skip = 0 } = options;

      const configs = await QcDeviceConfigModel
        .find({ device: deviceId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .populate('device')
        .populate('user');

      const total = await QcDeviceConfigModel.countDocuments({ device: deviceId });

      return {
        success: true,
        data: {
          configs,
          pagination: {
            total,
            limit,
            skip,
            hasMore: (skip + configs.length) < total
          }
        },
        message: `Found ${configs.length} configuration records`
      };

    } catch (error) {
      throw error;
    }
  }

  async getLatestConfigsForDevices(deviceIds) {
    try {
      if (!deviceIds || !deviceIds.length) {
        return { success: true, data: [] };
      }

      // Use aggregation to get the latest config for each device
      const latestConfigs = await QcDeviceConfigModel.aggregate([
        { $match: { device: { $in: deviceIds } } },
        { $sort: { device: 1, createdAt: -1 } },
        { $group: { _id: '$device', latestConfig: { $first: '$$ROOT' } } },
        { $replaceRoot: { newRoot: '$latestConfig' } }
      ]);
      return {
        success: true,
        data: latestConfigs,
        message: `Found ${latestConfigs.length} latest configurations`
      };

    } catch (error) {
      console.error('Error in getLatestConfigsForDevices:', error);
      throw error;
    }
  }
}

export default new DeviceConfigController();

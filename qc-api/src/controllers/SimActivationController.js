import QcSimActivationModel from '../qcModels/qcSimActivation.js';

class SimActivationController {
  /**
   * Create a new SIM activation record
   * @param {Object} activationData - The activation data
   * @param {string} activationData.device - Device ObjectId
   * @param {string} activationData.config - QcDeviceConfig ObjectId
   * @param {number} activationData.slot - SIM slot (1 or 2)
   * @param {Object} activationData.request - API request payload
   * @param {Object} activationData.response - API response
   * @param {boolean} activationData.success - Activation success status
   * @param {string} activationData.reason - Reason for failure (optional)
   * @returns {Promise<Object>} The created activation record
   * @throws {Error} Validation or database errors
   */
  async createActivation(activationData) {
    try {
      const { device, config, slot, request, response, success, reason } = activationData;

      // Validate required fields
      if (!device) {
        throw new Error('Device ID is required');
      }

      if (!config) {
        throw new Error('Config ID is required');
      }

      if (slot === undefined || slot === null) {
        throw new Error('SIM slot is required');
      }

      // Validate slot enum
      const validSlots = [1, 2];
      if (!validSlots.includes(slot)) {
        throw new Error(`Slot must be one of: ${validSlots.join(', ')}`);
      }

      // Create new activation record
      const newActivation = new QcSimActivationModel({
        device,
        config,
        slot,
        status: 'pending',
        request,
        response,
        success: success !== undefined ? success : false,
        reason
      });

      const savedActivation = await newActivation.save();

      return {
        success: true,
        data: savedActivation,
        message: 'SIM activation record created successfully'
      };

    } catch (error) {
      // Handle Mongoose validation errors
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map(err => err.message);
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      // Handle duplicate key errors
      if (error.code === 11000) {
        throw new Error('Duplicate activation record detected');
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Get the latest activation for a specific config
   * @param {string} configId - QcDeviceConfig ObjectId
   * @returns {Promise<Object>} The latest activation record or null
   */
  async getLatestActivationForConfig(configId) {
    try {
      if (!configId) {
        throw new Error('Config ID is required');
      }

      const latestActivation = await QcSimActivationModel
        .findOne({ config: configId })
        .sort({ createdAt: -1 })
        .populate('device')
        .populate('config');

      return {
        success: true,
        data: latestActivation,
        message: latestActivation ? 'Latest activation found' : 'No activation found for config'
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the latest activation for a specific device and slot
   * @param {string} deviceId - Device ObjectId
   * @param {number} slot - SIM slot (1 or 2)
   * @returns {Promise<Object>} The latest activation record or null
   */
  async getLatestActivationForDeviceSlot(deviceId, slot) {
    try {
      if (!deviceId) {
        throw new Error('Device ID is required');
      }

      if (slot === undefined || slot === null) {
        throw new Error('SIM slot is required');
      }

      const validSlots = [1, 2];
      if (!validSlots.includes(slot)) {
        throw new Error(`Slot must be one of: ${validSlots.join(', ')}`);
      }

      const latestActivation = await QcSimActivationModel
        .findOne({ device: deviceId, slot })
        .sort({ createdAt: -1 })
        .populate('device')
        .populate('config');

      return {
        success: true,
        data: latestActivation,
        message: latestActivation ? 'Latest activation found' : 'No activation found for device slot'
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get activation history for a specific config
   * @param {string} configId - QcDeviceConfig ObjectId
   * @param {Object} options - Query options
   * @param {number} options.limit - Limit number of results
   * @param {number} options.skip - Skip number of results
   * @returns {Promise<Object>} Array of activation records
   */
  async getActivationHistoryForConfig(configId, options = {}) {
    try {
      if (!configId) {
        throw new Error('Config ID is required');
      }

      const { limit = 10, skip = 0 } = options;

      const activations = await QcSimActivationModel
        .find({ config: configId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .populate('device')
        .populate('config');

      const total = await QcSimActivationModel.countDocuments({ config: configId });

      return {
        success: true,
        data: {
          activations,
          pagination: {
            total,
            limit,
            skip,
            hasMore: (skip + activations.length) < total
          }
        },
        message: `Found ${activations.length} activation records`
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get activation history for a specific device and slot
   * @param {string} deviceId - Device ObjectId
   * @param {number} slot - SIM slot (1 or 2)
   * @param {Object} options - Query options
   * @param {number} options.limit - Limit number of results
   * @param {number} options.skip - Skip number of results
   * @returns {Promise<Object>} Array of activation records
   */
  async getActivationHistoryForDeviceSlot(deviceId, slot, options = {}) {
    try {
      if (!deviceId) {
        throw new Error('Device ID is required');
      }

      if (slot === undefined || slot === null) {
        throw new Error('SIM slot is required');
      }

      const validSlots = [1, 2];
      if (!validSlots.includes(slot)) {
        throw new Error(`Slot must be one of: ${validSlots.join(', ')}`);
      }

      const { limit = 10, skip = 0 } = options;

      const activations = await QcSimActivationModel
        .find({ device: deviceId, slot })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .populate('device')
        .populate('deviceSim');

      const total = await QcSimActivationModel.countDocuments({ device: deviceId, slot });

      return {
        success: true,
        data: {
          activations,
          pagination: {
            total,
            limit,
            skip,
            hasMore: (skip + activations.length) < total
          }
        },
        message: `Found ${activations.length} activation records`
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get successful activations only for a device SIM
   * @param {string} deviceSimId - DeviceSim ObjectId
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Array of successful activation records
   */
  async getSuccessfulActivationsForDeviceSim(deviceSimId, options = {}) {
    try {
      if (!deviceSimId) {
        throw new Error('Device SIM ID is required');
      }

      const { limit = 10, skip = 0 } = options;

      const activations = await QcSimActivationModel
        .find({ deviceSim: deviceSimId, success: true })
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .populate('device')
        .populate('deviceSim');

      const total = await QcSimActivationModel.countDocuments({ deviceSim: deviceSimId, success: true });

      return {
        success: true,
        data: {
          activations,
          pagination: {
            total,
            limit,
            skip,
            hasMore: (skip + activations.length) < total
          }
        },
        message: `Found ${activations.length} successful activation records`
      };

    } catch (error) {
      throw error;
    }
  }

  async getLatestActivationsForDevices(deviceIds) {
    try {
      if (!deviceIds || !deviceIds.length) {
        return { success: true, data: [] };
      }

      // Get latest activations for each device/slot combination
      const latestActivations = await QcSimActivationModel.aggregate([
        {
          $match: { device: { $in: deviceIds } }
        },
        {
          $sort: { device: 1, slot: 1, createdAt: -1 }
        },
        {
          $group: {
            _id: { device: '$device', slot: '$slot' },
            latestActivation: { $first: '$$ROOT' }
          }
        },
        {
          $replaceRoot: { newRoot: '$latestActivation' }
        },
        {
          $lookup: {
            from: 'qcdeviceconfigs',
            localField: 'config',
            foreignField: '_id',
            as: 'config'
          }
        },
        {
          $unwind: { path: '$config', preserveNullAndEmptyArrays: true }
        }
      ]);

      return {
        success: true,
        data: latestActivations,
        message: `Found ${latestActivations.length} latest activations`
      };

    } catch (error) {
      throw error;
    }
  }


  /**
   * Mark activation as started
   */
  async markAsStarted(activationId) {
    try {
      const activation = await QcSimActivationModel.findByIdAndUpdate(
        activationId,
        {
          status: 'in_progress',
          startedAt: new Date()
        },
        { new: true }
      );

      if (!activation) {
        throw new Error('Activation record not found');
      }

      return {
        success: true,
        data: activation,
        message: 'Activation marked as started'
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Mark activation as completed
   */
  async markAsCompleted(activationId, result) {
    try {
      const updateData = {
        status: result.success ? 'completed' : 'failed',
        success: result.success,
        completedAt: new Date(),
        response: result.details || result.response
      };

      if (!result.success && result.message) {
        updateData.reason = result.message;
      }

      const activation = await QcSimActivationModel.findByIdAndUpdate(
        activationId,
        updateData,
        { new: true }
      );

      if (!activation) {
        throw new Error('Activation record not found');
      }

      return {
        success: true,
        data: activation,
        message: 'Activation result updated'
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Mark activation as inactive (stopped before attempting)
   */
  async markAsInactive(activationId, reason) {
    try {
      const activation = await QcSimActivationModel.findByIdAndUpdate(
        activationId,
        {
          status: 'inactive',
          success: false,
          reason: reason || 'Activation stopped before attempt',
          completedAt: new Date()
        },
        { new: true }
      );

      if (!activation) {
        throw new Error('Activation record not found');
      }

      return {
        success: true,
        data: activation,
        message: 'Activation marked as inactive'
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Mark activation as suspended (carrier intervention required)
   */
  async markAsSuspended(activationId, reason, statusDetails = null) {
    try {
      const updateData = {
        status: 'suspended',
        success: false,
        completedAt: new Date(),
        reason: reason || 'SIM activation stuck in Suspended status'
      };

      if (statusDetails) {
        updateData.response = statusDetails;
      }

      const activation = await QcSimActivationModel.findByIdAndUpdate(
        activationId,
        updateData,
        { new: true }
      );

      if (!activation) {
        throw new Error('Activation record not found');
      }

      return {
        success: true,
        data: activation,
        message: 'Activation marked as suspended'
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if device is busy with activation
   */
  async isDeviceBusy(deviceId) {
    try {
      const busyCount = await QcSimActivationModel.countDocuments({
        device: deviceId,
        status: { $in: ['pending', 'in_progress'] }
      });

      return busyCount > 0;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update activation with final result
   */
  async updateActivationResult(activationId, { response, success, reason }) {
    try {
      const updateData = {
        status: success ? 'completed' : 'failed',
        success,
        response,
        completedAt: new Date()
      };

      if (reason) {
        updateData.reason = reason;
      }

      const activation = await QcSimActivationModel.findByIdAndUpdate(
        activationId,
        updateData,
        { new: true }
      );

      if (!activation) {
        throw new Error('Activation record not found');
      }

      return {
        success: true,
        data: activation,
        message: 'Activation result updated'
      };
    } catch (error) {
      throw error;
    }
  }
}

export default new SimActivationController();

import QcSimActivationModel from '../qcModels/qcSimActivation.js';
import _QcDeviceConfigModel from '../qcModels/qcDeviceConfig.js'; // for Mongoose aggregation
import mongoose from 'mongoose';

class SimActivationReportController {
  /**
   * Generate comprehensive SIM activation report for a time interval
   * @param {Object} options - Report options
   * @param {Date} options.startDate - Start date for the report
   * @param {Date} options.endDate - End date for the report
   * @param {string[]} options.deviceIds - Optional array of device IDs to filter
   * @param {string[]} options.plans - Optional array of plans to filter ('standard', 'public', 'private')
   * @param {string[]} options.regions - Optional array of regions (ZIP codes) to filter
   * @param {number[]} options.slots - Optional array of slots to filter (1, 2)
   * @returns {Promise<Object>} Comprehensive activation report
   */
  async generateActivationReport(options = {}) {
    try {
      const {
        startDate,
        endDate,
        deviceIds,
        plans,
        regions,
        slots
      } = options;

      // Build match criteria
      const matchCriteria = {};

      if (startDate || endDate) {
        matchCriteria.createdAt = {};
        if (startDate) matchCriteria.createdAt.$gte = new Date(startDate);
        if (endDate) matchCriteria.createdAt.$lt = new Date(endDate);
      }

      if (deviceIds && deviceIds.length) {
        matchCriteria.device = { $in: deviceIds.map(id => new mongoose.Types.ObjectId(id)) };
      }

      if (slots && slots.length) {
        matchCriteria.slot = { $in: slots };
      }

      // Get all activations in the time period
      const activations = await QcSimActivationModel.aggregate([
        { $match: matchCriteria },
        {
          $lookup: {
            from: 'qcdeviceconfigs',
            localField: 'config',
            foreignField: '_id',
            as: 'configData'
          }
        },
        {
          $lookup: {
            from: 'epikboxes',
            localField: 'device',
            foreignField: '_id',
            as: 'deviceData'
          }
        },
        {
          $unwind: { path: '$configData', preserveNullAndEmptyArrays: true }
        },
        {
          $unwind: { path: '$deviceData', preserveNullAndEmptyArrays: true }
        },
        {
          $match: {
            ...(plans && plans.length ? { 'configData.plan': { $in: plans } } : {}),
            ...(regions && regions.length ? { 'configData.region': { $in: regions } } : {})
          }
        }
      ]);

      // Generate comprehensive report
      const report = await this._generateReportData(activations, options);

      return {
        success: true,
        data: report,
        message: `Generated activation report for ${activations.length} records`
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate detailed report data from activations
   */
  async _generateReportData(activations, options) {
    // Get stage progression data for failure analysis
    const stageAnalysis = await this._analyzeStageProgression(activations);

    const report = {
      summary: this._generateSummaryStats(activations),
      byPlan: this._generatePlanBreakdown(activations),
      byRegion: this._generateRegionBreakdown(activations),
      bySlot: this._generateSlotBreakdown(activations),
      byStatus: this._generateStatusBreakdown(activations),
      byDevice: this._generateDeviceBreakdown(activations),
      timeline: this._generateTimelineData(activations),
      failureAnalysis: this._generateFailureAnalysis(activations),
      stageProgression: stageAnalysis,
      filters: options,
      generatedAt: new Date()
    };

    return report;
  }

  /**
   * Generate overall summary statistics
   */
  _generateSummaryStats(activations) {
    const total = activations.length;
    const successful = activations.filter(a => a.success === true).length;
    const failed = activations.filter(a => a.success === false).length;
    const pending = activations.filter(a => a.status === 'pending').length;
    const inProgress = activations.filter(a => a.status === 'in_progress').length;
    const suspended = activations.filter(a => a.status === 'suspended').length;

    return {
      total,
      successful,
      failed,
      failureCount: failed, // Explicit failure count as requested
      pending,
      inProgress,
      suspended,
      successRate: total > 0 ? ((successful / total) * 100).toFixed(2) : 0,
      failureRate: total > 0 ? ((failed / total) * 100).toFixed(2) : 0,
      uniqueDevices: [...new Set(activations.map(a => a.device.toString()))].length,
      uniqueConfigs: [...new Set(activations.map(a => a.config?.toString()).filter(Boolean))].length
    };
  }

  /**
   * Generate breakdown by plan type
   */
  _generatePlanBreakdown(activations) {
    const breakdown = {};

    activations.forEach(activation => {
      const plan = activation.configData?.plan || 'unknown';

      if (!breakdown[plan]) {
        breakdown[plan] = {
          total: 0,
          successful: 0,
          failed: 0,
          pending: 0,
          inProgress: 0,
          suspended: 0
        };
      }

      breakdown[plan].total++;
      if (activation.success === true) breakdown[plan].successful++;
      if (activation.success === false) breakdown[plan].failed++;
      if (activation.status === 'pending') breakdown[plan].pending++;
      if (activation.status === 'in_progress') breakdown[plan].inProgress++;
      if (activation.status === 'suspended') breakdown[plan].suspended++;
    });

    // Calculate success rates
    Object.keys(breakdown).forEach(plan => {
      const data = breakdown[plan];
      data.successRate = data.total > 0 ? ((data.successful / data.total) * 100).toFixed(2) : 0;
      data.failureRate = data.total > 0 ? ((data.failed / data.total) * 100).toFixed(2) : 0;
    });

    return breakdown;
  }

  /**
   * Generate breakdown by region (ZIP code)
   */
  _generateRegionBreakdown(activations) {
    const breakdown = {};

    activations.forEach(activation => {
      const region = activation.configData?.region || 'unknown';

      if (!breakdown[region]) {
        breakdown[region] = {
          total: 0,
          successful: 0,
          failed: 0,
          pending: 0,
          inProgress: 0,
          suspended: 0
        };
      }

      breakdown[region].total++;
      if (activation.success === true) breakdown[region].successful++;
      if (activation.success === false) breakdown[region].failed++;
      if (activation.status === 'pending') breakdown[region].pending++;
      if (activation.status === 'in_progress') breakdown[region].inProgress++;
      if (activation.status === 'suspended') breakdown[region].suspended++;
    });

    // Calculate success rates and sort by total
    const sortedBreakdown = {};
    Object.keys(breakdown)
      .sort((a, b) => breakdown[b].total - breakdown[a].total)
      .forEach(region => {
        const data = breakdown[region];
        data.successRate = data.total > 0 ? ((data.successful / data.total) * 100).toFixed(2) : 0;
        data.failureRate = data.total > 0 ? ((data.failed / data.total) * 100).toFixed(2) : 0;
        sortedBreakdown[region] = data;
      });

    return sortedBreakdown;
  }

  /**
   * Generate breakdown by SIM slot
   */
  _generateSlotBreakdown(activations) {
    const breakdown = {};

    activations.forEach(activation => {
      const slot = activation.slot || 'unknown';

      if (!breakdown[slot]) {
        breakdown[slot] = {
          total: 0,
          successful: 0,
          failed: 0,
          pending: 0,
          inProgress: 0,
          suspended: 0
        };
      }

      breakdown[slot].total++;
      if (activation.success === true) breakdown[slot].successful++;
      if (activation.success === false) breakdown[slot].failed++;
      if (activation.status === 'pending') breakdown[slot].pending++;
      if (activation.status === 'in_progress') breakdown[slot].inProgress++;
      if (activation.status === 'suspended') breakdown[slot].suspended++;
    });

    // Calculate success rates
    Object.keys(breakdown).forEach(slot => {
      const data = breakdown[slot];
      data.successRate = data.total > 0 ? ((data.successful / data.total) * 100).toFixed(2) : 0;
      data.failureRate = data.total > 0 ? ((data.failed / data.total) * 100).toFixed(2) : 0;
    });

    return breakdown;
  }

  /**
   * Generate breakdown by activation status
   */
  _generateStatusBreakdown(activations) {
    const breakdown = {};

    activations.forEach(activation => {
      const status = activation.status || 'unknown';

      if (!breakdown[status]) {
        breakdown[status] = {
          count: 0,
          devices: new Set(),
          avgDuration: 0,
          durations: []
        };
      }

      breakdown[status].count++;
      breakdown[status].devices.add(activation.device.toString());

      // Calculate duration if completed
      if (activation.startedAt && activation.completedAt) {
        const duration = (new Date(activation.completedAt) - new Date(activation.startedAt)) / 1000;
        breakdown[status].durations.push(duration);
      }
    });

    // Calculate averages and convert Sets to counts
    Object.keys(breakdown).forEach(status => {
      const data = breakdown[status];
      data.uniqueDevices = data.devices.size;
      data.devices = undefined; // Remove Set object

      if (data.durations.length > 0) {
        data.avgDuration = (data.durations.reduce((a, b) => a + b, 0) / data.durations.length).toFixed(2);
        data.minDuration = Math.min(...data.durations).toFixed(2);
        data.maxDuration = Math.max(...data.durations).toFixed(2);
      }
      data.durations = undefined; // Remove array
    });

    return breakdown;
  }

  /**
   * Generate breakdown by device (fixed to respect 2 SIM slots max)
   */
  _generateDeviceBreakdown(activations) {
    const deviceSlotMap = {};

    // First pass: organize by device and slot, keeping only the latest activation per slot
    activations.forEach(activation => {
      const deviceId = activation.device.toString();
      const slot = activation.slot || 1; // Default to slot 1 if not specified
      const serialNumber = activation.deviceData?.serialNumber || 'unknown';

      if (!deviceSlotMap[deviceId]) {
        deviceSlotMap[deviceId] = {
          serialNumber,
          slots: {},
          lastActivation: activation.createdAt
        };
      }

      // Update last activation time for device
      if (new Date(activation.createdAt) > new Date(deviceSlotMap[deviceId].lastActivation)) {
        deviceSlotMap[deviceId].lastActivation = activation.createdAt;
      }

      // Keep only the latest activation per slot
      if (!deviceSlotMap[deviceId].slots[slot] ||
          new Date(activation.createdAt) > new Date(deviceSlotMap[deviceId].slots[slot].createdAt)) {
        deviceSlotMap[deviceId].slots[slot] = activation;
      }
    });

    // Second pass: calculate stats based on latest status per slot
    const breakdown = {};

    Object.keys(deviceSlotMap).forEach(deviceId => {
      const deviceData = deviceSlotMap[deviceId];
      const slots = Object.values(deviceData.slots);

      breakdown[deviceId] = {
        serialNumber: deviceData.serialNumber,
        total: slots.length, // Number of slots that have been activated
        successful: slots.filter(activation => activation.success === true).length,
        failed: slots.filter(activation => activation.success === false).length,
        pending: slots.filter(activation => activation.status === 'pending').length,
        inProgress: slots.filter(activation => activation.status === 'in_progress').length,
        suspended: slots.filter(activation => activation.status === 'suspended').length,
        lastActivation: deviceData.lastActivation,
        totalAttempts: activations.filter(a => a.device.toString() === deviceId).length // Track total attempts for debugging
      };

      // Calculate success rates based on slots, not attempts
      const data = breakdown[deviceId];
      data.successRate = data.total > 0 ? ((data.successful / data.total) * 100).toFixed(2) : 0;
      data.failureRate = data.total > 0 ? ((data.failed / data.total) * 100).toFixed(2) : 0;
    });

    // Sort by number of failed slots, then by total attempts
    const sortedBreakdown = {};
    Object.keys(breakdown)
      .sort((a, b) => {
        // First sort by failed slots (most failures first)
        if (breakdown[b].failed !== breakdown[a].failed) {
          return breakdown[b].failed - breakdown[a].failed;
        }
        // Then by total attempts (most attempts first)
        return breakdown[b].totalAttempts - breakdown[a].totalAttempts;
      })
      .forEach(deviceId => {
        sortedBreakdown[deviceId] = breakdown[deviceId];
      });

    return sortedBreakdown;
  }

  /**
   * Alternative method: Generate summary stats that respect SIM slot limits
   */
  _generateSummaryStatsFixed(activations) {
    // Get latest status per device-slot combination
    const deviceSlotMap = {};

    activations.forEach(activation => {
      const deviceId = activation.device.toString();
      const slot = activation.slot || 1;
      const key = `${deviceId}-${slot}`;

      if (!deviceSlotMap[key] ||
          new Date(activation.createdAt) > new Date(deviceSlotMap[key].createdAt)) {
        deviceSlotMap[key] = activation;
      }
    });

    const latestStatuses = Object.values(deviceSlotMap);
    const total = latestStatuses.length;
    const successful = latestStatuses.filter(a => a.success === true).length;
    const failed = latestStatuses.filter(a => a.success === false).length;
    const pending = latestStatuses.filter(a => a.status === 'pending').length;
    const inProgress = latestStatuses.filter(a => a.status === 'in_progress').length;
    const suspended = latestStatuses.filter(a => a.status === 'suspended').length;

    return {
      total,
      successful,
      failed,
      failureCount: failed,
      pending,
      inProgress,
      suspended,
      successRate: total > 0 ? ((successful / total) * 100).toFixed(2) : 0,
      failureRate: total > 0 ? ((failed / total) * 100).toFixed(2) : 0,
      uniqueDevices: [...new Set(latestStatuses.map(a => a.device.toString()))].length,
      uniqueConfigs: [...new Set(latestStatuses.map(a => a.config?.toString()).filter(Boolean))].length,
      totalAttempts: activations.length // Track total attempts for comparison
    };
  }
  /**
   * Generate timeline data (daily aggregation)
   */
  _generateTimelineData(activations) {
    const timeline = {};

    activations.forEach(activation => {
      const date = new Date(activation.createdAt).toISOString().split('T')[0];

      if (!timeline[date]) {
        timeline[date] = {
          total: 0,
          successful: 0,
          failed: 0,
          pending: 0,
          inProgress: 0,
          suspended: 0
        };
      }

      timeline[date].total++;
      if (activation.success === true) timeline[date].successful++;
      if (activation.success === false) timeline[date].failed++;
      if (activation.status === 'pending') timeline[date].pending++;
      if (activation.status === 'in_progress') timeline[date].inProgress++;
      if (activation.status === 'suspended') timeline[date].suspended++;
    });

    // Calculate success rates and sort by date
    const sortedTimeline = {};
    Object.keys(timeline)
      .sort()
      .forEach(date => {
        const data = timeline[date];
        data.successRate = data.total > 0 ? ((data.successful / data.total) * 100).toFixed(2) : 0;
        data.failureRate = data.total > 0 ? ((data.failed / data.total) * 100).toFixed(2) : 0;
        sortedTimeline[date] = data;
      });

    return sortedTimeline;
  }

  /**
   * Generate failure analysis
   */
  _generateFailureAnalysis(activations) {
    const failedActivations = activations.filter(a => a.success === false && a.reason);
    const reasonCounts = {};
    const reasonsByPlan = {};
    const reasonsByRegion = {};

    failedActivations.forEach(activation => {
      const reason = activation.reason || 'Unknown reason';
      const plan = activation.configData?.plan || 'unknown';
      const region = activation.configData?.region || 'unknown';

      // Count overall reasons
      reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;

      // Count reasons by plan
      if (!reasonsByPlan[plan]) reasonsByPlan[plan] = {};
      reasonsByPlan[plan][reason] = (reasonsByPlan[plan][reason] || 0) + 1;

      // Count reasons by region
      if (!reasonsByRegion[region]) reasonsByRegion[region] = {};
      reasonsByRegion[region][reason] = (reasonsByRegion[region][reason] || 0) + 1;
    });

    // Sort reasons by frequency
    const sortedReasons = Object.entries(reasonCounts)
      .sort(([,a], [,b]) => b - a)
      .reduce((acc, [reason, count]) => {
        acc[reason] = count;
        return acc;
      }, {});

    return {
      totalFailures: failedActivations.length,
      reasonCounts: sortedReasons,
      reasonsByPlan,
      reasonsByRegion,
      topFailureReasons: Object.entries(sortedReasons).slice(0, 10)
    };
  }

  /**
   * Analyze stage progression for devices with failed activations
   * This helps distinguish real problems from false alarms
   */
  async _analyzeStageProgression(activations) {
    const failedActivations = activations.filter(a => a.success === false);

    if (failedActivations.length === 0) {
      return {
        totalFailures: 0,
        realProblems: [],
        falseAlarms: [],
        summary: {
          realProblemCount: 0,
          falseAlarmCount: 0,
          realProblemRate: 0
        }
      };
    }

    const deviceIds = failedActivations.map(a => a.device);

    // Get current stage for each device with failed activations
    const QcStageModel = mongoose.model('QcStage');
    const currentStages = await QcStageModel.aggregate([
      { $match: { device: { $in: deviceIds } } },
      { $sort: { device: 1, createdAt: -1 } },
      {
        $group: {
          _id: '$device',
          currentStage: { $first: '$stage' },
          stageEnteredAt: { $first: '$createdAt' }
        }
      }
    ]);

    const stageMap = {};
    currentStages.forEach(stage => {
      stageMap[stage._id.toString()] = {
        stage: stage.currentStage,
        enteredAt: stage.stageEnteredAt
      };
    });

    // Categorize failed activations
    const realProblems = [];
    const falseAlarms = [];

    failedActivations.forEach(activation => {
      const deviceId = activation.device.toString();
      const deviceStage = stageMap[deviceId];

      const deviceInfo = {
        device: activation.device,
        serialNumber: activation.deviceData?.serialNumber,
        activationDate: activation.createdAt,
        reason: activation.reason,
        currentStage: deviceStage?.stage || 'unknown',
        stageEnteredAt: deviceStage?.enteredAt,
        config: activation.configData
      };

      // Real problems: stuck in early stages or moved to repair/failure stages
      if (!deviceStage ||
          ['sim', 'software', 'failure', 'repair', 'engineering'].includes(deviceStage.stage)) {
        realProblems.push(deviceInfo);
      }
      // False alarms: progressed to hardware/shipping/archive (manually resolved)
      else if (['hardware', 'shipping', 'archive'].includes(deviceStage.stage)) {
        falseAlarms.push(deviceInfo);
      }
      // Unknown case
      else {
        realProblems.push(deviceInfo);
      }
    });

    return {
      totalFailures: failedActivations.length,
      realProblems,
      falseAlarms,
      summary: {
        realProblemCount: realProblems.length,
        falseAlarmCount: falseAlarms.length,
        realProblemRate: failedActivations.length > 0 ?
          ((realProblems.length / activations.length) * 100).toFixed(4) : 0
      }
    };
  }

  /**
   * Get current activation status for all devices
   */
  async getCurrentActivationStatus(deviceIds = null) {
    try {
      const matchCriteria = {};
      if (deviceIds && deviceIds.length) {
        matchCriteria.device = { $in: deviceIds.map(id => new mongoose.Types.ObjectId(id)) };
      }

      const currentStatus = await QcSimActivationModel.aggregate([
        { $match: matchCriteria },
        { $sort: { device: 1, slot: 1, createdAt: -1 } },
        {
          $group: {
            _id: { device: '$device', slot: '$slot' },
            latestActivation: { $first: '$$ROOT' }
          }
        },
        { $replaceRoot: { newRoot: '$latestActivation' } },
        {
          $lookup: {
            from: 'qcdeviceconfigs',
            localField: 'config',
            foreignField: '_id',
            as: 'configData'
          }
        },
        {
          $lookup: {
            from: 'epikboxes',
            localField: 'device',
            foreignField: '_id',
            as: 'deviceData'
          }
        },
        {
          $unwind: { path: '$configData', preserveNullAndEmptyArrays: true }
        },
        {
          $unwind: { path: '$deviceData', preserveNullAndEmptyArrays: true }
        }
      ]);

      return {
        success: true,
        data: currentStatus,
        message: `Found current status for ${currentStatus.length} device/slot combinations`
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Get devices with real activation problems (not false alarms)
   */
  async getRealActivationProblems(options = {}) {
    try {
      const {
        startDate,
        endDate,
        deviceIds,
        plans,
        regions
      } = options;

      // Get recent failed activations
      const report = await this.generateActivationReport(options);
      const stageProgression = report.data.stageProgression;

      // Focus on real problems only
      const realProblems = stageProgression.realProblems;

      return {
        success: true,
        data: {
          totalFailures: stageProgression.totalFailures,
          realProblems,
          falseAlarms: stageProgression.falseAlarms,
          summary: stageProgression.summary,
          generatedAt: new Date()
        },
        message: `Found ${realProblems.length} devices with real activation problems out of ${stageProgression.totalFailures} total failures`
      };

    } catch (error) {
      throw error;
    }
  }
}

export default new SimActivationReportController();

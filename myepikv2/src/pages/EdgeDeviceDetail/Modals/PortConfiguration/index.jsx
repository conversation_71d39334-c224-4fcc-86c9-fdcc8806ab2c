import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Flex,
  Typography,
  Input,
  Button,
  CheckTickIcon,
  InfoIcon,
  Toggle,
  RefreshIcon,
} from '@/components';
import { Divider, Row, Col } from 'antd';
import './styles.css';
import { useEdgeDeviceDetailContext } from '../../context';
import { isEscene } from '@/utils/helpers';

const Title = () => (
  <Flex vertical>
    <Typography className="heading-four-bold">Port Configuration</Typography>
    <Divider className="divider-sm" />
  </Flex>
);

const Field = ({ label, component, defaultValue = '' }) => (
  <Row gutter={8} style={{ marginBottom: '16px' }}>
    <Col xs={8} lg={8} md={8} sm={24}>
      <Flex align="center" gap={8}>
        <Typography
          className="small-text"
          style={{ color: 'var(--primary-gray)' }}
        >
          {label}
        </Typography>
        <InfoIcon height="12" width="12" />
      </Flex>
    </Col>
    <Col xs={12} lg={12} md={8} sm={24}>
      {component}
    </Col>
    {defaultValue && (
      <Col xs={4} lg={4} md={8} sm={24} align="end">
        <Button color="default" variant="outlined" style={{ width: '80%' }}>
          {defaultValue}
        </Button>
      </Col>
    )}
  </Row>
);

Field.propTypes = {
  label: PropTypes.string.isRequired,
  component: PropTypes.node.isRequired,
  defaultValue: PropTypes.number || PropTypes.string,
};

const PortConfiguration = ({ onCancel, handleOk }) => {
  const { selectedPort } = useEdgeDeviceDetailContext();
  const [formValues, setFormValues] = useState({
    CPCDuration: '',
    OnHookTipRingVoltage: '',
    OffHookCurrentMax: '',
    DTMFDetectMinLength: '',
    DTMFDetectMinGap: '',
    ChannelTxGain: '',
    ChannelRxGain: '',
    DTMFMethod: '',
    DTMFPlaybackLevel: '',
    SilenceDetectSensitivity: 'Med', // Default value for toggle
    showVadToggle: 'Disable', // Default value for toggle
    modemMode: selectedPort?.appliedConfigTemplateDoc?.modemMode || false,
    t38Enabled: selectedPort?.appliedConfigTemplateDoc?.t38Enabled || false,
    faxEnabled: selectedPort?.appliedConfigTemplateDoc?.faxEnabled || false,
    JitterMinDelay: selectedPort?.appliedConfigTemplateDoc?.JitterMinDelay || 0,
    JitterMaxDelay: selectedPort?.appliedConfigTemplateDoc?.JitterMaxDelay || 0,
    inbandRoute: selectedPort?.inbandRoute || false,
  });

  useEffect(() => {
    if (!selectedPort) return;

    setFormValues({
      CPCDuration: selectedPort?.CPCDuration || '2000',
      OnHookTipRingVoltage: selectedPort?.OnHookTipRingVoltage || '',
      OffHookCurrentMax: selectedPort?.OffHookCurrentMax || '',
      DTMFDetectMinLength: selectedPort?.DTMFDetectMinLength || '',
      DTMFDetectMinGap: selectedPort?.DTMFDetectMinGap || '',
      ChannelTxGain: selectedPort?.ChannelTxGain || '',
      ChannelRxGain: selectedPort?.ChannelRxGain || '',
      DTMFMethod: selectedPort?.DTMFMethod || '',
      DTMFPlaybackLevel: selectedPort?.DTMFPlaybackLevel || '',
      SilenceDetectSensitivity:
        selectedPort?.SilenceDetectSensitivity === 'Low' ? 'Low' : 'Med',
      showVadToggle: selectedPort?.showVadToggle ? 'Enable' : 'Disable',
      inbandRoute: selectedPort?.inbandRoute,
    });
  }, [selectedPort]);

  const isEsceneDevice =
    selectedPort?.macAddress && isEscene(selectedPort?.macAddress);

  const handleChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
  };

  const fieldsConfig = [
    {
      name: 'CPCDuration',
      label: 'CPC Duration (in Millisecond)',
      placeholder: '2000',
      defaultValue: '2000',
      disabled: true,
    },
    {
      name: 'OnHookTipRingVoltage',
      label: 'Onhook Volts',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'OffHookCurrentMax',
      label: 'Onhook Current',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'DTMFDetectMinLength',
      label: 'DTMF Detect Length',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'DTMFDetectMinGap',
      label: 'DTMF Detect Gap',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'ChannelTxGain',
      label: 'TX Gain',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'ChannelRxGain',
      label: 'RX Gain',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'DTMFMethod',
      label: 'DTMF Method',
      placeholder: 'Enter Value',
      disabled: true,
    },
    {
      name: 'DTMFPlaybackLevel',
      label: 'DTMF Playback Level',
      placeholder: 'Enter Value',
      disabled: true,
    },
  ];

  return (
    <Modal
      title={<Title />}
      open={true}
      onCancel={onCancel}
      okText="Assign Edge Device"
      cancelText="Cancel"
      handleOk={() => handleOk(formValues)}
      width="50%"
      footer={
        <Flex justify="center" gap={6} style={{ marginTop: '24px' }} vertical>
          <Divider className="divider-sm" />
          <Flex gap={4} justify="end">
            <Button
              color="default"
              variant="filled"
              onlyIcon={true}
              icon={<RefreshIcon height={18} width={18} />}
            />
            <Button
              color="default"
              variant="outlined"
              style={{ minWidth: '25%' }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button style={{ minWidth: '25%' }}>Apply</Button>
          </Flex>
        </Flex>
      }
    >
      <Row gutter={8} style={{ marginBottom: '16px' }}>
        <Col xs={8} lg={8} md={8} sm={24}></Col>
        <Col xs={12} lg={12} md={8} sm={24} align="center">
          <Typography>Database Settings</Typography>
        </Col>
        <Col xs={4} lg={4} md={8} sm={24} align="end">
          <Typography>Port Settings</Typography>
        </Col>
      </Row>
      {fieldsConfig.map((field) => (
        <Field
          key={field.name}
          label={field.label}
          component={
            <Input
              suffix={<CheckTickIcon height={24} width={24} />}
              placeholder={field.placeholder}
              value={formValues[field.name]}
              disabled={field.disabled || false}
              onChange={(e) => handleChange(field.name, e.target.value)}
            />
          }
          defaultValue={selectedPort[field?.name]}
        />
      ))}
      {/* Silence Detect Field */}
      <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex align="center" gap={4}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              Silence Detect
            </Typography>
            <InfoIcon height="12" width="12" />
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={16} sm={24}>
          <Flex align="center" gap={8}>
            <Typography>Med</Typography>
            <Toggle
              size="small"
              checked={formValues.SilenceDetectSensitivity === 'Low'}
              onChange={(checked) =>
                handleChange(
                  'SilenceDetectSensitivity',
                  checked ? 'Low' : 'Med',
                )
              }
            />
            <Typography>Low</Typography>
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex
            justify="center"
            style={{
              border: '1px solid #D0D5DD',
              padding: '2px 6px',
              borderRadius: '6px',
              textAlign: 'center',
            }}
          >
            <Typography>Unable to fetch Silence Detect Sensitivity</Typography>
          </Flex>
        </Col>
      </Row>
      {/* VAD Field */}
      <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex align="center" gap={4}>
            <Typography
              className="small-text"
              style={{ color: 'var(--primary-gray)' }}
            >
              VAD
            </Typography>
            <InfoIcon height="12" width="12" />
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={16} sm={24}>
          <Flex align="center" gap={6}>
            <Typography>Disable</Typography>
            <Toggle
              size="small"
              checked={formValues.showVadToggle === 'Enable'}
              onChange={(checked) =>
                handleChange('showVadToggle', checked ? 'Enable' : 'Disable')
              }
            />
            <Typography>Enable</Typography>
          </Flex>
        </Col>
        <Col xs={24} lg={8} md={8} sm={24}>
          <Flex
            justify="center"
            style={{
              border: '1px solid #D0D5DD',
              padding: '2px 6px',
              borderRadius: '6px',
              textAlign: 'center',
            }}
          >
            <Typography>Disable</Typography>
          </Flex>
        </Col>
      </Row>

      {/* Inband route */}
      {selectedPort?.appliedConfigTemplateDoc?.showInbandRouteToggle && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Inband Route
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.inbandRoute}
                onChange={(checked) =>
                  handleChange('inbandRoute', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.inbandRoute ? 'Enabled' : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {/* modemMode */}
      {(selectedPort?.appliedConfigTemplateDoc?.showModemModeToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Modem Mode
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.modemMode}
                onChange={(checked) =>
                  handleChange('modemMode', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.modemMode
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {/* t38Enabled */}
      {(selectedPort?.appliedConfigTemplateDoc?.showT38EnableToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                T38 Enable
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.t38Enabled}
                onChange={(checked) =>
                  handleChange('t38Enabled', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.t38Enabled
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {/* faxEnabled */}
      {(selectedPort?.appliedConfigTemplateDoc?.showFaxEnabledToggle ||
        isEsceneDevice) && (
        <Row gutter={6} align="middle" style={{ marginBottom: '16px' }}>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex align="center" gap={4}>
              <Typography
                className="small-text"
                style={{ color: 'var(--primary-gray)' }}
              >
                Fax Event
              </Typography>
              <InfoIcon height="12" width="12" />
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={16} sm={24}>
            <Flex align="center" gap={6}>
              <Typography>Disable</Typography>
              <Toggle
                size="small"
                checked={formValues?.faxEnabled}
                onChange={(checked) =>
                  handleChange('faxEnabled', checked ? true : false)
                }
              />
              <Typography>Enable</Typography>
            </Flex>
          </Col>
          <Col xs={24} lg={8} md={8} sm={24}>
            <Flex
              justify="center"
              style={{
                border: '1px solid #D0D5DD',
                padding: '2px 6px',
                borderRadius: '6px',
                textAlign: 'center',
              }}
            >
              <Typography>
                {selectedPort?.appliedConfigTemplateDoc?.faxEnabled
                  ? 'Enabled'
                  : 'Disabled'}
              </Typography>
            </Flex>
          </Col>
        </Row>
      )}

      {(selectedPort?.appliedConfigTemplateDoc?.showJitterDelay ||
        isEsceneDevice) && (
        <>
          <Field
            key={'JitterMinDelay'}
            label={'Jitter Min Delay'}
            component={
              <Input
                suffix={<CheckTickIcon height={24} width={24} />}
                placeholder={'Jitter Min Delay'}
                value={formValues['JitterMinDelay']}
                onChange={(e) => handleChange('JitterMinDelay', e.target.value)}
                defaultValue={
                  selectedPort?.appliedConfigTemplateDoc?.JitterMinDelay
                }
              />
            }
          />
          <Field
            key={'JitterMaxDelay'}
            label={'Jitter Max Delay'}
            component={
              <Input
                suffix={<CheckTickIcon height={24} width={24} />}
                placeholder={'Jitter Max Delay'}
                value={formValues['JitterMaxDelay']}
                onChange={(e) => handleChange('JitterMaxDelay', e.target.value)}
                defaultValue={
                  selectedPort?.appliedConfigTemplateDoc?.JitterMaxDelay
                }
              />
            }
          />
        </>
      )}
    </Modal>
  );
};

PortConfiguration.propTypes = {
  onCancel: PropTypes.func.isRequired,
  handleOk: PropTypes.func.isRequired,
};

export default PortConfiguration;

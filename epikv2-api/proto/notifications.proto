syntax = "proto3";

package edge.v1;

service EdgeDeviceProxy {
  rpc PingBox(stream PingMessage) returns (stream PingMessage);
  rpc UnaryEcho(EchoRequest) returns (EchoResponse);
  rpc PowerSource(DeviceRequest) returns (PowerSourceResponse);
  rpc ActiveInterface(DeviceRequest) returns (ActiveInterfaceResponse);
  rpc LanIp(DeviceRequest) returns (LanIpResponse);
  rpc PublicIp(DeviceRequest) returns (PublicIpResponse);
  rpc SignalStrength(DeviceRequest) returns (SignalStrengthResponse);
  rpc SimStatus(DeviceRequest) returns (SimStatusResponse);
  rpc ModemInfo(DeviceRequest) returns (ModemInfoResponse);
  rpc SensorData(DeviceRequest) returns (SensorDataResponse);
  rpc DeviceOnline(DeviceRequest) returns (DeviceOnlineResponse);
  rpc DcAvgPing(DeviceRequest) returns (DcAvgPingResponse);
  rpc RegStatus(DeviceRequest) returns (RegStatusResponse);
  rpc PortRegStatus(EpiRequest) returns (PortRegStatusResponse);
  rpc PortPhysicalStatus(EpiRequest) returns (PortStatusResponse);
  rpc EnableDisablePort(EnableDisablePortRequest) returns (EnableDisablePortResponse);
  rpc LiveEpis(DeviceRequest) returns (LiveEpisResponse);
  rpc WifiStatus(DeviceRequest) returns (WifiStatusResponse);
  rpc NetworkInfo(DeviceRequest) returns (NetworkInfoResponse);
  rpc DeviceNightlyUpdateTime(DeviceRequest) returns (DeviceNightlyUpdateTimeResponse);
  rpc PortForwardList(DeviceRequest) returns (PortForwardListResponse);
  rpc PriorityInterface(DeviceRequest) returns (PriorityInterfaceResponse);
  rpc PrimarySim(DeviceRequest) returns (PrimarySimResponse);
  rpc CurrentApn(DeviceRequest) returns (CurrentApnResponse);
  rpc EpikUpdateStatus(DeviceRequest) returns (EpikUpdateStatusResponse);
  rpc SystemInfo(DeviceRequest) returns (SystemInfoResponse);
  rpc DCConnectionStats(DeviceRequest) returns (DCConnectionStatsResponse);
  rpc DnsCheck(DeviceRequest) returns (DnsCheckResponse);
  rpc VSwitchTab(DeviceRequest) returns (VSwitchTabResponse);
  rpc LteAnalyzer(DeviceRequest) returns (LteAnalyzerResponse);
  rpc SpeedTest(DeviceRequest) returns (SpeedTestResponse);
  rpc InitLtePerf(DeviceRequest) returns (InitLtePerfResponse);
  rpc FetchLtePerf(DeviceRequest) returns (FetchLtePerfResponse);
  rpc PortConfigValues(EpiRequest) returns (PortConfigValuesResponse);
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse);
  rpc EnqueueRequest(AsyncRequest) returns (AsyncResponse);
  rpc GetRequestStatus(StatusRequest) returns (StatusResponse);
  rpc CancelRequest(CancelRequestMessage) returns (CancelResponse);
  rpc DashboardData(DashboardRequest) returns (DashboardResponse);
  rpc ExecuteSshCommand(SshCommandRequest) returns (SshCommandResponse);
  rpc ExecuteSshCommandAsync(SshCommandRequest) returns (stream SshCommandResponse);
}

message DeviceRequest {
  string serial_number = 1;
  string payload = 2;
}

message EnableDisablePortRequest {
  string serial_number = 1;
  int32 port = 2;
  bool enable = 3;
}

message EnableDisablePortResponse {
  bool success = 1;
}

message EpiRequest {
  string mac_address = 1;
  string serial_number = 2;
  string port = 3;
}

message PowerSourceResponse {
  string power_source = 1;
}

message ActiveInterfaceResponse {
  string active_interface = 1;
}

message LanIpResponse {
  string lan_ip = 1;
}

message PublicIpResponse {
  string public_ip = 1;
}

message SignalStrengthResponse {
  string signal_strength = 1;
}

message SimStatusResponse {
  string sim_status = 1;
}

message ModemInfo {
  string manufacturer_placeholder = 1;
  string manufacturer = 2;
  string model_placeholder = 3;
  string model = 4;
  string sim_placeholder = 5;
  string sim = 6;
  string imei_placeholder = 7;
  string imei = 8;
  string carrier_placeholder = 9;
  string carrier = 10;
  string ipAddress_placeholder = 11;
  string ipAddress = 12;
}

message ModemInfoResponse {
  ModemInfo modem_info = 1;
}

message SensorDataResponse {
  string power = 1;
  string temp = 2;
}

message DeviceOnlineResponse {
  bool online = 1;
}

message DeviceResponse {
  bytes body = 1;
}

message AsyncRequest {
  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
}

message AsyncResponse {
  string request_id = 1;
  string status = 2;
  int64 estimated_completion_seconds = 3;
}

message StatusRequest {
  string request_id = 1;
}

message StatusResponse {
  string request_id = 1;
  string status = 2;
  int32 status_code = 3;
  bytes body = 4;
  string error = 6;
  int64 created_at = 7;
  int64 completed_at = 8;
}

message CancelRequestMessage {
  string request_id = 1;
}

message CancelResponse {
  string request_id = 1;
  bool cancelled = 2;
  string message = 3;
}

message DashboardEndpoint {
  string path = 1;
  string identifier = 2;
  int32 timeout_seconds = 3;
}

message DashboardRequest {
  string serial_number = 1;
}

message DashboardEndpointResult {
  string identifier = 1;
  string path = 2;
  int32 status_code = 3;
  bytes body = 4;
  string error = 6;
  int64 duration_ms = 7;
  bool success = 8;
}

message DashboardResponse {
  string serial_number = 1;
  repeated DashboardEndpointResult results = 2;
  int32 total_endpoints = 3;
  int32 successful_endpoints = 4;
  int32 failed_endpoints = 5;
  int64 total_duration_ms = 6;
  bool overall_success = 7;
}

message EchoRequest {
  string message = 1;
}

message EchoResponse {
  string message = 1;
}

message DcAvgPingResponse {
  string atPingAvg = 1;
  string bestDC = 2;
  string bestLatency = 3;
  string chPingAvg = 4;
  string dlPingAvg = 5;
  string laPingAvg = 6;
  string nyPingAvg = 7;
  string timeUpdate = 8;
  string error = 9;
}

message PingMessage {
  oneof payload {
    PingCommand command = 1;
    PingLine line = 2;
  }
}

message PingCommand {
  string serial_number = 1;
  bool stop = 2;
  string ip = 3;
}

message PingLine {
  string line = 1;
  bool completed = 2;
}

message RegStatusResponse {
  bool registered = 1;
}

message WanInfo {
  string place_holder = 1;
  string ip = 2;
  string subnet = 3;
  string gateway = 4;
  string dns = 5;
}

message Sp1ServiceStatus {
  string status = 1;
  string call_state = 2;
}

message Sp2ServiceStatus {
  string status = 1;
  string call_state = 2;
}

message ObiTalkServiceStatus {
  string place_holder = 1;
  string status = 2;
}

message PortRegStatusResponse {
  WanInfo wanInfo = 1;
  Sp1ServiceStatus sp1ServiceStatus = 2;
  Sp2ServiceStatus sp2ServiceStatus = 3;
  ObiTalkServiceStatus obiTalkServiceStatus = 4;
}

message PortStatus {
  string name = 1;
  string state = 2;
  string loopCurrent = 3;
  string vbat = 4;
  string tipRingVoltage = 5;
  string lastCallerInfo = 6;
}

message PortStatusResponse {
  repeated PortStatus portStatus = 1;
}

message LiveEpisResponse {
  map<string, string> epis = 1;
}

message WifiStatusResponse {
  string error_msg = 1;
  string gateway = 2;
  string ip = 3;
  string mode = 4;
  string password = 5;
  string SSID = 6;
  string sec_mode = 7;
  string status = 8;
  string subnet = 9;
}

message NetworkInterfaceObj {
  string interface = 1;
  string internet = 2;
  string icmp = 3;
  string wg = 4;
}

message NetworkInfoResponse {
  string dns = 1;
  string error = 2;
  repeated NetworkInterfaceObj interfaces = 3;
  string timestamp = 4;
}

message DeviceNightlyUpdateTimeResponse {
  string device_update_time = 1;
}

message PortForwardObj {
  string srcIP = 1;
  int32 srcStartPort = 2;
  int32 srcEndPort = 3;
  string dstIP = 4;
  int32 dstStartPort = 5;
  int32 dstEndPort = 6;
  string proto = 7;
}

message PortForwardListResponse {
  repeated PortForwardObj portForwardList = 1;
}

message PriorityInterfaceResponse {
  string priorityInterface = 1;
}

message PrimarySimResponse {
  string primarySim = 1;
}

message CurrentApnResponse {
  string apn = 1;
}

message EpikUpdateStatusResponse {
  bool status = 1;
}

message SystemInfoResponse {
  map<string, string> sysInfo = 1;
}

message SshCommandRequest {
  string serial_number = 1;
  string command = 2;
}

message SshCommandResponse {
  string output = 1;
  string error_message = 2;
  int32 exit_code = 3;
  int64 duration_ms = 4;
  bool success = 5;
}

message DCConnectionStatsResponse {
  map<string, string> dc_connection_stats = 1;
}

message DnsCheckResponse {
  bool dns = 1;
}

message PortInfo {
  string port = 1;
  string calledId = 2;
  string recording = 3;
  string trunkType = 4;
}

message VSwitchTabResponse {
  bool registered = 1;
  bool registerationConfigCreated = 2;
  repeated PortInfo portsInfo = 3;
  repeated string portsConfigCreated = 4;
}

message LteAnalyzerResponse {
  bool Busy = 1;
}

message SpeedTestResponse {
  string downloadSpeed = 1;
  string uploadSpeed = 2;
  string latency = 3;
  string jitter = 4;
}

message InitLtePerfResponse {
  string initiated = 1;
}

message SimPingInfo {
  string Error = 1;
  double Jitter = 2;
  double PacketLoss = 3;
  double PingAvg = 4;
  int32 SIM = 5;
}

message FetchLtePerfResponse {
  repeated SimPingInfo SimsInfo = 1;
  string TimeStamp = 2;
}

message PortConfigValuesResponse {
  string OnhookVolts = 1;
  string OffhookCurrent = 2;
  string DtmfDetectLength = 3;
  string DtmfDetectGap = 4;
  string TxGain = 5;
  string RxGain = 6;
  string DtmfMethod = 7;
  string DtmfPlaybackLevel = 8;
  string RingVoltage = 9;
  string DigitMapShortTimer = 10;
  string CpcDuration = 11;
  string CpcDelayTime = 12;
  string JitterBufferType = 13;
  string JitterBufferMinDeley = 14;
  string JitterBufferMaxDeley = 15;
  string T38Enabled = 16;
  string ModemMode = 17;
  string VadEnable = 18;
  string ThreeWayCalling = 19;
  string SilenceDetectSensitivity = 20;
}


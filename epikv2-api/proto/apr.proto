syntax = "proto3";

package apr.v1;

service APRService {
  rpc ToggleAPR(APRToggleRequest) returns (APRToggleResponse);
  rpc APRStatus(APRStatusRequest) returns (APRStatusResponse);
  rpc APRTestResults(APRTestResultRequest) returns (APRTestResultResponse);
  rpc InitiateAprTest(InitiateAprRequest) returns (InitiateAprResponse);
}

message APRToggleRequest {
  string serial_number = 1;
  string port = 2;
  bool enable = 3;
}

message APRToggleResponse {
  bool status = 1;
  string response = 2;
}

message InitiateAprRequest {
  string serial_number = 1;
  string port = 2;
  string companion_port = 3;
}

message InitiateAprResponse {
  bool status = 1;
  string response = 2;
}

message APRTestResultRequest {
  string serial_number = 1;
  string port = 2;
}

message APRTestResultResponse {
  bool result = 1;
  string response = 2;
  string timestamp = 3;
}

message APRStatusRequest {
  string serial_number = 1;
  string port = 2;
}

message APRStatusResponse {
  bool status = 1;
  string response = 2;
}

message APRTestStartRequest {
  string serial_number = 1;
  string port = 2;
  string companion_port = 3;
}

message APRTestStartResponse {
  string test_id = 1;
  string status = 2;
  int64 start_time = 3;
  string message = 4;
}


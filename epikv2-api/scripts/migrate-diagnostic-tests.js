
import { MongoClient } from 'mongodb';
const logger = console;
const DiagnosticDeviceVersion = {
  V2: 2,
  V3: 3,
  V4: 4,
  ALL: 0,
};
const DiagnosticTestCategory = {
  VOICE: 'voice',
  NETWORK: 'network',
  SYSTEM: 'system',
  CONTAINER: 'container',
  STORAGE: 'storage',
  PERMISSIONS: 'permissions',
  SERVICE: 'service',
  CONNECTIVITY: 'connectivity',
};
const DiagnosticTestSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

// Diagnostic test definitions based on your existing diagnostic controller
const diagnosticTestDefinitions = [
  {
    name: 'vswitch_container_test',
    displayName: 'Voice Switch Container Test',
    description: 'Check if the FreeSWITCH container is running properly',
    category: DiagnosticTestCategory.VOICE,
    severity: DiagnosticTestSeverity.HIGH,
    executionOrder: 1,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: '/snap/bin/lxc ls | grep freeswitch',
        requiresSudo: false,
        timeoutSeconds: 30,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'vswitch_container_running',
        description: 'Check if FreeSWITCH container is in RUNNING state',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: [
          'sudo ifconfig lo: *********** netmask ************* up',
          'sudo snap start lxd',
          "sudo bash -c '/snap/bin/lxc config set core.https_address=127.0.0.1:8443'",
          'sudo ifconfig lo: *********** netmask ************* down',
          '/snap/bin/lxc restart freeswitch',
          '/snap/bin/lxc start freeswitch',
        ],
        requiresSudo: false,
        timeoutSeconds: 120,
        description: 'Restart LXD and FreeSWITCH container',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['voice', 'container', 'freeswitch'],
  },
  {
    name: 'disk_space_test',
    displayName: 'Disk Space Test',
    description: 'Check if the FreeSWITCH container has sufficient disk space',
    category: DiagnosticTestCategory.STORAGE,
    severity: DiagnosticTestSeverity.CRITICAL,
    executionOrder: 2,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: "bash -c '/snap/bin/lxc exec freeswitch -- df -h|grep loop'",
        requiresSudo: false,
        timeoutSeconds: 30,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'disk_space_not_full',
        description: 'Check if disk usage is not at 100%',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: [
          "bash -c '/snap/bin/lxc exec freeswitch -- apt-get clean'",
          "bash -c '/snap/bin/lxc exec rm /usr/local/freeswitch/log/freeswitch.log'",
          "bash -c '/snap/bin/lxc exec rm /usr/local/freeswitch/log/logbackup.tar.gz'",
          '/snap/bin/lxc restart freeswitch',
        ],
        requiresSudo: false,
        timeoutSeconds: 90,
        description: 'Clean up disk space and restart FreeSWITCH',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['storage', 'disk', 'cleanup'],
  },
  {
    name: 'failover_config_test',
    displayName: 'Failover Configuration Test',
    description: 'Check if the failover configuration is properly set up',
    category: DiagnosticTestCategory.NETWORK,
    severity: DiagnosticTestSeverity.HIGH,
    executionOrder: 3,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: 'cat /etc/default/epikFailover |wc -l|tail -1',
        requiresSudo: false,
        timeoutSeconds: 30,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'failover_config_lines',
        description: 'Check if failover config has at least 5 lines',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: [
          // Note: This will be dynamically generated based on device datacenter
          'printf "INTERFACES=wwan0,eth0\\nLATENCYTRIGGER=500,500\\nWIREGUARDPUBLIC=*************\\nWIREGUARDLATENCY=900\\nPINGHOST=**************\\n" | sudo tee /etc/default/epikFailover',
          '/snap/bin/lxc restart freeswitch',
          'sudo service epikFailover restart',
        ],
        requiresSudo: false,
        timeoutSeconds: 90,
        description: 'Recreate failover configuration and restart services',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['network', 'failover', 'configuration'],
  },
  {
    name: 'iam_test',
    displayName: 'IAM Agent Test',
    description: 'Check if the IAM agent is installed and running',
    category: DiagnosticTestCategory.SERVICE,
    severity: DiagnosticTestSeverity.MEDIUM,
    executionOrder: 4,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: 'wget -q -O - "$@" http://{{device_ip}}:9988/v2/iamagent/status',
        requiresSudo: false,
        timeoutSeconds: 30,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'iam_agent_running',
        description: 'Check if IAM agent status shows installed and running',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: ['wget -q -O - "$@" http://{{device_ip}}:9988/v2/iamagent/reinstall'],
        requiresSudo: false,
        timeoutSeconds: 60,
        description: 'Reinstall IAM agent',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['service', 'iam', 'agent'],
  },
  {
    name: 'trunking_test',
    displayName: 'SIP Trunking Test',
    description: 'Check if SIP profiles are registered and working',
    category: DiagnosticTestCategory.VOICE,
    severity: DiagnosticTestSeverity.HIGH,
    executionOrder: 5,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: 'bash -c \'/snap/bin/lxc exec freeswitch -- fs_cli -x "sofia status"\'',
        requiresSudo: false,
        timeoutSeconds: 30,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'sofia_profiles_registered',
        description: 'Check if 2 profiles are present and REGED status exists',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: ['bash -c \'/snap/bin/lxc exec freeswitch -- fs_cli -x "reload mod_sofia"\''],
        requiresSudo: false,
        timeoutSeconds: 60,
        description: 'Reload Sofia SIP module',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['voice', 'sip', 'trunking', 'sofia'],
  },
  {
    name: 'permissions_test',
    displayName: 'File Permissions Test',
    description: 'Check if FreeSWITCH file permissions are correctly set',
    category: DiagnosticTestCategory.PERMISSIONS,
    severity: DiagnosticTestSeverity.MEDIUM,
    executionOrder: 6,
    testCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        command: `bash -c '
          count1=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/ |grep root |wc -l)
          count2=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/ |grep nobody |wc -l)
          count3=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/log/ |grep root |wc -l)
          count4=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/log/ |grep nobody |wc -l)
          count5=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/conf/ |grep root |wc -l)
          count6=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/conf/ |grep nobody |wc -l)
          count7=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/db/ |grep root |wc -l)
          count8=$(sudo ls -l /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/db/ |grep nobody |wc -l)
          echo $((count1 + count2 + count3 + count4 + count5 + count6 + count7 + count8))
        '`,
        requiresSudo: false,
        timeoutSeconds: 45,
        workingDirectory: undefined,
        environment: undefined,
      },
    ],
    validations: [
      {
        type: 'custom',
        customFunction: 'permissions_check',
        description: 'Check if total count of root/nobody owned files is 0',
      },
    ],
    fixCommands: [
      {
        version: DiagnosticDeviceVersion.ALL,
        commands: [
          'sudo chown -R 1001000:1001000 /var/snap/lxd/common/mntns/var/snap/lxd/common/lxd/storage-pools/default/containers/freeswitch/rootfs/usr/local/freeswitch/',
        ],
        requiresSudo: false,
        timeoutSeconds: 60,
        description: 'Fix FreeSWITCH file permissions',
      },
    ],
    enabled: true,
    autoFix: true,
    tags: ['permissions', 'filesystem', 'freeswitch'],
  },
];

async function migrateDiagnosticTests() {
  try {
    const mongoClient = new MongoClient('mongodb://localhost:27017/epikFax2', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    await mongoClient.connect();
    logger.info('Starting diagnostic test migration...');

    const diagnosticTestModel = mongoClient.db().collection('diagnostictests');
    let created = 0;
    let skipped = 0;

    for (const testDef of diagnosticTestDefinitions) {
      try {
        // Check if test already exists
        const existing = await diagnosticTestModel.findOne({ name: testDef.name });
        if (existing) {
          logger.info({ name: testDef.name }, 'Diagnostic test already exists, skipping');
          skipped++;
          continue;
        }

        // Create the test
        const testData = {
          ...testDef,
          createdBy: 'migration-script',
          executionCount: 0,
          successCount: 0,
          failureCount: 0,
          fixSuccessCount: 0,
          creationDate: new Date(),
          lastUpdated: new Date(),
          deleted: false,
        };

        await diagnosticTestModel.insertOne(testData);
        logger.info({ name: testDef.name }, 'Diagnostic test created successfully');
        created++;
      } catch (error) {
        logger.error(
          {
            error: error.message,
            name: testDef.name,
          },
          'Failed to create diagnostic test'
        );
      }
    }

    logger.info(
      { created, skipped, total: diagnosticTestDefinitions.length },
      'Diagnostic test migration completed'
    );
  } catch (error) {
    process.exit(1);
  } finally {
  }
}
migrateDiagnosticTests();
export { migrateDiagnosticTests };

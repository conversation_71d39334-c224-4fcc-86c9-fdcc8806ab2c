//!/usr/bin/env tsx

import { createModuleLogger } from '../src/utils/logger';
import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'grpc-reflection-js';
import * as grpc from '@grpc/grpc-js';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const logger = createModuleLogger('proto-generator');
const fileName: string[] = [];
interface ServiceTarget {
  name: string;
  serverAddress: string;
  useKubectl?: boolean;
  kubeNamespace?: string;
  kubeServiceName?: string;
  kubeServicePort?: number;
  localPort?: number;
  kubeContext?: string;
  package?: string;
  expectedFileName?: string;
}

const targets: ServiceTarget[] = [
  {
    name: 'EdgeDeviceProxy',
    serverAddress: 'localhost:50051',
    useKubectl: true,
    kubeNamespace: 'default',
    kubeServiceName: 'edge-proxy-svc',
    kubeServicePort: 50051,
    localPort: 50051,
    package: 'package edge.v1;',
    expectedFileName: 'notifications.proto',
  },
  {
    name: 'APRService',
    serverAddress: 'localhost:50051',
    useKubectl: true,
    kubeNamespace: 'default',
    kubeServiceName: 'edge-proxy-svc',
    kubeServicePort: 50051,
    localPort: 50051,
    package: 'package apr.v1;',
    expectedFileName: 'apr.proto',
  },
  {
    name: 'PermissionService',
    serverAddress: 'localhost:50052',
    useKubectl: true,
    kubeNamespace: 'default',
    kubeServiceName: 'auth-api-svc',
    kubeServicePort: 50051,
    localPort: 50052,
    package: 'package auth;',
  },
];
const _typesDir = path.join(process.cwd(), 'src', 'types', 'generated');
class ProtoGenerator {
  private protoDir = path.join(process.cwd(), 'proto');
  private typesDir = _typesDir;
  private portForwardProcess: any = null;

  constructor() {
    [this.protoDir, this.typesDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  private async setupKubectl(
    service: ServiceTarget
  ): Promise<{ serverAddress: string; cleanup: () => void }> {
    if (!service.useKubectl) return { serverAddress: service.serverAddress, cleanup: () => {} };

    const namespace = service.kubeNamespace || 'default';
    const svc = service.kubeServiceName || service.name.toLowerCase();
    const localPort = service.localPort || 50051;
    const remotePort = service.kubeServicePort || 50051;

    return new Promise((resolve, reject) => {
      this.portForwardProcess = spawn('kubectl', [
        'port-forward',
        ...(service.kubeContext ? [`--context=${service.kubeContext}`] : []),
        `-n`,
        namespace,
        `service/${svc}`,
        `${localPort}:${remotePort}`,
      ]);

      const timeout = setTimeout(() => {
        this.portForwardProcess?.kill();
        reject(new Error('Timeout on port-forward'));
      }, 10000);

      this.portForwardProcess.stdout?.on('data', (d: Buffer) => {
        if (d.toString().includes('Forwarding from')) {
          clearTimeout(timeout);
          resolve({
            serverAddress: `localhost:${localPort}`,
            cleanup: () => this.portForwardProcess?.kill(),
          });
        }
      });

      this.portForwardProcess.stderr?.on('data', (data: Buffer) => {
        logger.warn(data.toString());
      });
    });
  }

  private toProtoFile(protoJson: any, service: ServiceTarget): string {
    let content = 'syntax = "proto3";\n\n';
    if (protoJson.package) content += `package ${protoJson.package};\n\n`;
    else if (service.package) content += `${service.package}\n\n`;

    if (protoJson.options) {
      for (const [k, v] of Object.entries(protoJson.options)) {
        content += typeof v === 'string' ? `option ${k} = \"${v}\";\n` : `option ${k} = ${v};\n`;
      }
      content += '\n';
    }

    const { messages, services, mapEntries } = this.extract(protoJson.nested);
    return content + mapEntries + services + messages;
  }

  private extract(nested: any): { messages: string; services: string; mapEntries: string } {
    let messages = '',
      services = '',
      mapEntries = '';

    const walk = (obj: [any, any], indent = '') => {
      for (const [name, def] of Object.entries(obj)) {
        // Add debugging
        if (name === 'LiveEpisResponse') {
          console.log(`Processing ${name}:`, {
            hasFields: !!def.fields,
            hasOptions: !!def.options,
            options: def.options,
            hasNested: !!def.nested,
            nestedKeys: def.nested ? Object.keys(def.nested) : [],
          });
        }

        if (def.fields) {
          messages += `${indent}message ${name} {\n`;
          let currentOneof: string | null = null;

          for (const [fName, f] of Object.entries(def.fields) as [any, any]) {
            let fieldRule = f.rule === 'repeated' ? 'repeated ' : '';
            let fieldType = f.type;

            // Handle map fields - check if the field type points to a nested map entry
            if (f.rule === 'repeated' && f.type && def.nested) {
              const typeName = f.type.split('.').pop(); // Get 'EpisEntry' from '.edge.v1.LiveEpisResponse.EpisEntry'
              const mapEntry = def.nested[typeName];

              if (mapEntry && mapEntry.options?.map_entry && mapEntry.fields) {
                // This is a map field, extract key and value types
                const keyField = mapEntry.fields.key;
                const valueField = mapEntry.fields.value;

                if (keyField && valueField) {
                  messages += `${indent}  map<${keyField.type}, ${valueField.type}> ${fName} = ${f.id};\n`;
                  continue;
                }
              }
            }

            // Handle oneof fields
            if (f.oneofIndex !== undefined) {
              const oneofName = f.oneofIndex === 0 ? fName : currentOneof;
              if (f.oneofIndex === 0) {
                // Start of a new oneof group
                if (currentOneof) {
                  messages += `${indent}  }\n`;
                }
                currentOneof = fName;
                messages += `${indent}  oneof ${fName} {\n`;
              }
              if (fieldType.includes('.')) fieldType = fieldType.split('.').pop();
              messages += `${indent}    ${fieldType} ${fName} = ${f.id};\n`;
              continue;
            }

            // Close any open oneof before adding regular fields
            if (currentOneof) {
              messages += `${indent}  }\n`;
              currentOneof = null;
            }

            // Handle regular fields
            if (fieldType.includes('.')) fieldType = fieldType.split('.').pop();
            messages += `${indent}  ${fieldRule}${fieldType} ${fName} = ${f.id};\n`;
          }

          // Close any remaining oneof
          if (currentOneof) {
            messages += `${indent}  }\n`;
          }

          messages += `${indent}}\n\n`;
        } else if (def.methods) {
          services += `${indent}service ${name} {\n`;
          for (const [mName, m] of Object.entries(def.methods) as [any, any]) {
            const req = m.requestType?.split('.').pop();
            const res = m.responseType?.split('.').pop();

            let streaming = '';
            if (m.requestStream) streaming += 'stream ';
            const input = streaming + req;

            streaming = '';
            if (m.responseStream) streaming += 'stream ';
            const output = streaming + res;

            services += `${indent}  rpc ${mName}(${input}) returns (${output});\n`;
          }
          services += `${indent}}\n\n`;
        } else if (def.values) {
          messages += `${indent}enum ${name} {\n`;
          for (const [vName, vValue] of Object.entries(def.values)) {
            messages += `${indent}  ${vName} = ${vValue};\n`;
          }
          messages += `${indent}}\n\n`;
        } else if (def.nested) {
          walk(def.nested, indent);
        }
      }
    };

    walk(nested);
    return { messages, services, mapEntries };
  }

  private async generateTypeScript(protoPath: string) {
    const pluginPath = path.join(process.cwd(), 'node_modules', '.bin', 'protoc-gen-ts');
    const cmd = `protoc \
      --plugin=protoc-gen-ts=${pluginPath} \
      --ts_out=${this.typesDir} \
      --proto_path=${this.protoDir} \
      ${protoPath}`;

    logger.debug(`Running: ${cmd}`);
    await execAsync(cmd);
  }

  async generate(service: ServiceTarget) {
    const { serverAddress, cleanup } = await this.setupKubectl(service);
    const client = new Client(serverAddress, grpc.credentials.createInsecure());
    const services = await client.listServices();
    const seen = new Set<string>();

    for (const svc of services) {
      if (!svc) continue;
      if (svc === 'grpc.reflection.v1alpha.ServerReflection') continue;
      if (!svc.includes(service.name)) continue;
      const file = await client.fileContainingSymbol(svc);
      if (seen.has(file.name)) continue;
      seen.add(file.name);
      const json = file.toJSON();
      let protoStr = this.toProtoFile(json, service);

      const fileBase = service.expectedFileName || file.files[0]?.split('/').pop() || 'output';
      const protoPath = `${fileBase}`;
      console.log({ protoPath, fileName: file.files, svc });
      // Fix specific issues with the generated proto
      if (protoPath === 'notifications.proto') {
        // Fix PingMessage to use oneof
        protoStr = protoStr.replace(
          /message PingMessage \{\s+PingCommand command = 1;\s+PingLine line = 2;\s+\}/s,
          'message PingMessage {\n  oneof payload {\n    PingCommand command = 1;\n    PingLine line = 2;\n  }\n}'
        );

        // Fix SystemInfoResponse to use map
        protoStr = protoStr.replace(
          /message SystemInfoResponse \{\s+repeated SysInfoEntry sysInfo = 1;\s+\}/,
          'message SystemInfoResponse {\n  map<string, string> sysInfo = 1;\n}'
        );

        // Fix LiveEpisResponse to use map
        protoStr = protoStr.replace(
          /message LiveEpisResponse \{\s+repeated EpisEntry epis = 1;\s+\}/,
          'message LiveEpisResponse {\n  map<string, string> epis = 1;\n}'
        );
        // Fix DCConnectionStatsResponse to use map
        protoStr = protoStr.replace(
          /repeated DcConnectionStatsEntry dc_connection_stats = 1;/,
          'map<string, string> dc_connection_stats = 1;'
        );
      }

      const fullPath = path.join(this.protoDir, protoPath);
      fileName.push(`export const ${service.name} = '${protoPath}';`);
      fs.writeFileSync(fullPath, protoStr);
      logger.info(`✅ Saved: ${protoPath}`);

      try {
        await this.generateTypeScript(protoPath);
        logger.info(`✅ Generated types for: ${protoPath}`);
      } catch (err) {
        logger.error(`❌ Failed generating types for ${protoPath}:`, err);
      }
    }

    cleanup();
  }
}

(async () => {
  const generator = new ProtoGenerator();

  for (const target of targets) {
    try {
      await generator.generate(target);

      const files = fs
        .readdirSync(_typesDir)
        .filter(f => f.endsWith('.ts'))
        .map(f => f.replace(/\.ts$/, ''));
      let exports =
        files.map(name => (name === 'index' ? '' : `export * from './${name}';`)).join('\n') + '\n';
      exports = exports + fileName.join('\n');
      fs.writeFileSync(path.join(_typesDir, 'index.ts'), exports);
      logger.info(`✅ Generated index.ts`);
    } catch (err) {
      logger.error(`Failed on ${target.name}:`, err);
    }
  }
})();

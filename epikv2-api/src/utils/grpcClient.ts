import { auth } from '@/types/generated';
import { createModuleLogger } from '@/utils/logger';
import * as grpc from '@grpc/grpc-js';
import * as grpc_1 from '@grpc/grpc-js';
import { ServiceClient } from '@grpc/grpc-js/build/src/make-client';
import * as path from 'path';

export interface GrpcServiceConfig {
  serverAddress: string;
  service: grpc_1.ServiceClientConstructor;
  maxRetries?: number;
}

type UnaryCallback<T> = (err: grpc.ServiceError | null, response: T) => void;

type UnaryMethod = (req: any, callback: UnaryCallback<any>) => grpc.ClientUnaryCall;

type MethodInput<T extends UnaryMethod> = T extends (req: infer R, cb: UnaryCallback<any>) => any
  ? R
  : never;

type MethodOutput<T extends UnaryMethod> = T extends (req: any, cb: UnaryCallback<infer R>) => any
  ? R
  : never;

// Generic gRPC client helper class
export class GrpcServiceClient<TClient extends ServiceClient = ServiceClient> {
  private client: TClient;
  private serverAddress: string;
  private service: grpc_1.ServiceClientConstructor;
  private isConnected = false;
  private readonly MAX_RETRIES: number;
  private logger: any;

  constructor(config: GrpcServiceConfig) {
    this.serverAddress = config.serverAddress;
    this.service = config.service;
    this.MAX_RETRIES = config.maxRetries || 3;
    this.logger = createModuleLogger(`grpc-${this.service.name.toLowerCase()}`);
  }

  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }
    const keepaliveOptions = {
      'grpc.keepalive_time_ms': 1_000,
      'grpc.keepalive_timeout_ms': 1_000,
      'grpc.keepalive_permit_without_calls': 1,
      'grpc.service_config': JSON.stringify({
        loadBalancingConfig: [],
        methodConfig: [
          {
            name: [
              {
                service: '*',
              },
            ],
            retryPolicy: {
              maxAttempts: this.MAX_RETRIES,
              initialBackoff: '0.01s',
              maxBackoff: '0.01s',
              backoffMultiplier: 1.0,
              retryableStatusCodes: ['UNAVAILABLE'],
            },
          },
        ],
      }),
    };
    try {
      this.client = new this.service(
        this.serverAddress,
        grpc.credentials.createInsecure(),
        keepaliveOptions
      ) as TClient;
      this.isConnected = true;
      this.logger.info(`Successfully connected to gRPC service at ${this.serverAddress}`);
    } catch (error) {
      this.logger.error(`Failed to connect to gRPC service at ${this.serverAddress}: ${error}`);
      throw error;
    }
  }
  async ping(request: any) {
    return new Promise((resolve, reject) => {
      this.client.UnaryEcho(request, (error: any, value: any) => {
        if (error) {
          reject(error);
          return;
        }
        resolve(value);
      });
    });
  }

  makeRequest = async <
    MethodName extends keyof TClient,
    Method extends TClient[MethodName] = TClient[MethodName],
    Input = MethodInput<Extract<Method, UnaryMethod>>,
    Output = MethodOutput<Extract<Method, UnaryMethod>>,
  >(
    method: MethodName,
    params: Input,
    responseField?: string
  ): Promise<Output> => {
    await this.connect();
    // Convert protobuf Message objects to plain objects for gRPC transmission
    return new Promise((resolve, reject) => {
      this.client[method](params, (error: any, response: any) => {
        this.logger.debug({ method, params: params, response }, 'gRPC request');
        if (error) {
          reject(error);
          return;
        }

        if (responseField && response[responseField] !== undefined) {
          resolve(response[responseField]);
          return;
        }

        resolve(response);
      });
    });
  };

  getClient() {
    return this.client;
  }

  isServiceConnected(): boolean {
    return this.isConnected;
  }
}

// Helper function for safe gRPC operations with error handling
export async function safeGrpcOperation<T = boolean>(
  operation: () => Promise<T>,
  context: string,
  logger: any,
  defaultValue?: T
): Promise<T> {
  try {
    return await operation();
  } catch (_err) {
    logger.error({ _err, context }, `Failed to ${context}`);
    return (defaultValue !== undefined ? defaultValue : false) as T;
  }
}

// Factory function to create gRPC service clients
export function createGrpcServiceClient(config: GrpcServiceConfig): GrpcServiceClient {
  return new GrpcServiceClient(config);
}

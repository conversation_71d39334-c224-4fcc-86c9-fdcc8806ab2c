import axios from "axios"

export function extractCookieValue(cookieHeader: string, name: string) {
  if (!cookieHeader) return null;
  const parts = cookieHeader
    .split(';')
    .map(part => part.trim())
    .filter(part => part.startsWith(name + '='));
  if (parts.length === 0) return null;
  return decodeURIComponent(parts[0].split('=')[1]);
}

export const openSipAPI = (datacenter: string, didNumber: string) => {
  const url = `http://${datacenter}.apilb.epikadmin.com:8480/routingInfoMulti/notify`;
  const did1 = didNumber.replace("+", "");
  const did2 = didNumber.replace("+1", "");
  axios.delete(url, {
    headers: { Authorization: `Bearer ${process.env.OPENSIP_TOKEN}` },
    data: [didNumber, did1, did2],
  });
}

export const checkIsEscene = (macAddress: string) => {
  return macAddress?.startsWith('002');
}

export const portConfigValidationRules = (isEscene: boolean) => {
  return [
    {
      name: 'OnHookTipRingVoltage',
      regEx: '^(?:3[0-9]|4[0-9]|5[0-2])',
      defaultValue: 46,
      tooltip: 'Default 46, ( 30V to 52V )',
    },
    {
      name: 'OffHookCurrentMax',
      regEx: '^(?:1[5-9]|2[0-9]|3[0-9]|4[0-5])',
      defaultValue: 20,
      tooltip: 'Default 20mA, ( 15mA to 45mA )',
    },
    {
      name: 'ChannelTxGain',
      regEx: '^(?:-[1-9]|-1[0-2]|[0-6])$',
      defaultValue: 1,
      tooltip:
        'Default 1, FXS transmit from EPI to phone, gain dB ( -12 to 6 )',
    },
    {
      name: 'DTMFPlaybackLevel',
      regEx:
        '^(?:-[1-9]|-1[0-9]|-2[0-9]|-3[0-9]|-4[0-9]|-5[0-9]|-6[0-9]|-7[0-9]|-8[0-9]|-9[0]|[0-3])$',
      defaultValue: 0,
      tooltip: 'Default 0, DTMF Playback, dB ( -90 to 3 )',
    },
    {
      name: 'ChannelRxGain',
      regEx: '^(?:-[1-9]|-1[0-2]|[0-6])$',
      defaultValue: 1,
      tooltip:
        'Default 1, FXS transmit from phone to EPI, gain dB ( -12 to 6 )',
    },
    {
      name: 'DTMFDetectMinGap',
      regEx: '^(?:[1-9]|1[0-9])$',
      defaultValue: 7,
      tooltip:
        'Default 7, minimum gap between dtmf digits in ms, actual value is multiple of 10 of specified value, ( 1 to 19 )',
    },
    {
      name: 'DTMFDetectMinLength',
      regEx: '^(?:[1-9]|10)$',
      defaultValue: 3,
      tooltip:
        'Default 3, minimum duration for DTMF tone in ms to be considered valid, 30 + x * 10 where x is specified value, ( 1 to 10 )',
    },
    {
      name: 'DTMFMethod',
      defaultValue: 'auto',
      tooltip: 'Default auto, method to pass DTMF to peer, ( auto, inband )',
      type: 'dropdown',
      options: ['inband', 'auto', 'rfc2833'],
    },
    {
      name: 'RingVoltage',
      regEx: isEscene ? '^[0-5]$' : '^(?:5[5-9]|6[0-9]|7[0-2])$',
      defaultValue: isEscene ? '4' : '70',
      tooltip: isEscene ? 'Default 4, peak ring voltage in volts ( 30 - 80 )' : 'Default 70, peak ring voltage in volts ( 55 - 72 )',
    },
    {
      name: 'DigitMapShortTimer',
      regEx: '^(?:[1-9]|1[0-9])$',
      defaultValue: '2',
      tooltip:
        'number of seconds to wait after a dtmf pattern has matched before call is processed ( 1 - 19 )',
    },
    {
      name: 'SilenceDetectSensitivity',
      type: 'toggle',
      defaultValue: 'Medium',
      tooltip:
        'Sets Silence Detection on the line to LOW. May help with MODEM communications.',
    },
    {
      name: 'CPCDelayTime',
      regEx: `^[0-9]+$`,
      defaultValue: isEscene ? 500 : 2000,
      tooltip: `Default ${isEscene ? 500 : 2000}, CPC Delay Time in ms, ( ${isEscene ? '250~3000' : '0~3000'}  )`,
    },
    {
      name: 'CPCDuration',
      regEx: `^[0-9]+$`,
      defaultValue: isEscene ? 1000 : 500,
      tooltip: `Default ${isEscene ? 1000 : 500}, CPC Duration in ms, ( ${isEscene ? '3000~5000' : '0~5000'}  )`,
    },
  ];
}
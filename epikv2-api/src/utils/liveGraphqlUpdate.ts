
import { GraphqlContext, LiveFieldOptions } from '@/types';
import { createModuleLogger } from './logger';
const logger = createModuleLogger('liveGraphqlUpdate');
// Don't pass options.cacheField if you know the EdgeProxy service
// function is returning the object with keys you want to save in DB

export function LiveField(options: LiveFieldOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    logger.debug({ target, propertyKey, descriptor }, 'LiveField');
    Reflect.defineMetadata('liveField', options, target, propertyKey);
  };
}

export abstract class BaseLiveResolver {
  constructor() {}
  protected async handleLiveField(
    root: any,
    context: GraphqlContext,
    fieldName: string,
    defaultValue: any = null
  ): Promise<any> {
    const options: LiveFieldOptions = Reflect.getMetadata('liveField', this, fieldName);
    if (!options) return defaultValue;

    const { fastify, pubsub } = context;
    const id = root._id.toString();
    const topicName = `FIELD_UPDATE_${id}`;

    // Background update
    setImmediate(async () => {
      try {
        // Dynamically call the service method
        const [remoteServiceName, remoteMethodName] = options.remoteServiceMethod.split('.');
        const remoteService = (fastify.services as any)[remoteServiceName];
        const serviceMethod = remoteService[remoteMethodName];

        const latestValue = await serviceMethod.call(remoteService, root.serialNumber || id, root.macAddress);
        // Update database if updateMethod provided
        if (options.updateMethod) {
          const [serviceName, methodName] = options.updateMethod.split('.');
          const service = (fastify.services as any)[serviceName];
          const updateMethod = service[methodName];

          if (options.cacheField) {
            await updateMethod.call(service, id, { [options.cacheField]: latestValue });
          } else {
            await updateMethod.call(service, id, latestValue);
          }
        }
        const publish = ({ field, value }: { field: string; value: any }) => {
          return pubsub.publish(topicName, {
            fieldName: field,
            value: value,
            id: root._id,
          });
        };
        logger.info({ topicName, latestValue }, 'handleLiveField');
        if (options.cacheField) {
          publish({ field: options.cacheField, value: latestValue });
        } else {
          if (Object.entries(latestValue)?.length) {
            for (const [field, value] of Object.entries(latestValue)) {
              publish({ field: field, value: value });
            }
          } else {
            publish({ field: "field", value: latestValue });
          }

        }
      } catch (err: any) {
        logger.error({ err }, '  field');
        const error = err.message.includes('failed to contact box')
          ? 'Box is offline'
          : err.details || 'unknown error';
        await pubsub.publish(topicName, {
          fieldName: options.cacheField,
          value: null,
          error: error,
          id: root._id,
        });
        logger.error(error, `Failed to update ${err}`);
      }
    });
    let returnData;
    if (options.cacheField) {
      returnData = root[options.cacheField];
    } else {
      returnData = {} as any;
      for (const [field, value] of Object.entries(root)) {
        returnData[field] = value;
      }
    }
    return returnData;
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}


import { PubSub } from 'graphql-subscriptions';
import type { PubSub as PubSubEngine } from 'type-graphql';

// Create a wrapper that implements the type-graphql PubSubEngine interface
export class TypeGraphQLPubSub implements PubSubEngine {
  private pubsub = new PubSub();

  async publish(triggerName: string, payload: any): Promise<void> {
    return this.pubsub.publish(triggerName, payload);
  }

  subscribe(triggerName: string): AsyncIterable<any> {
    return this.pubsub.asyncIterableIterator(triggerName);
  }
}

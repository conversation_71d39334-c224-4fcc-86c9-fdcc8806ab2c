import { EpikBoxDocument, EpikBoxModel } from '@/models/epikbox';
import fastify, { FastifyReply, FastifyRequest } from 'fastify';
import { respondJSON } from '@/utils/response';
import logger from '@/utils/logger';


export class EpikBoxController {
  constructor(private readonly epikBoxModel: EpikBoxModel) {}

  async createEpikBox(req: FastifyRequest<{ Body: EpikBoxDocument }>, res: FastifyReply) {
    const epikBox = await this.epikBoxModel.create(req.body);
    logger.info(`EpikBox created: ${epikBox}`);
    return respondJSON(epikBox);
  }

  /**
   * Get EpikBox by id
   * @param req Express Request
   * @param res Express Response
   */

  async getEpikBoxById(req: FastifyRequest<{ Params: { id: string } }>, res: FastifyReply) {
    try {
      const { id } = req.params;
      const epikbox = await this.epikBoxModel.findById(id);
      if (!epikbox) {
        logger.error(`EpikBox not found: ${id}`);
        return respondJSON(null);
      }
      return respondJSON(epikbox);
    } catch (error) {
      logger.error(`Internal server error: ${error}`);
      return respondJSON(error);
    }
  }

//   async getEpikBoxById(req: FastifyRequest<{ Params: { id: string } }>, res: FastifyReply) {
//     try {
//       const { id } = req.params;
//       const epikbox = await this.epikBoxModel.findById(id);
//       if (!epikbox) {
//         logger.error(`EpikBox not found: ${id}`);
//         return respondJSON(null);
//       }
//       return respondJSON(epikbox);
//     } catch (error) {
//       logger.error(`Internal server error: ${error}`);
//       return respondJSON(error);
//     }
//   }
}

import {  EpikBoxModel,  } from '@/models/epikbox';
import fastify, { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { respondJSON } from '@/utils/response';
import logger from '@/utils/logger';
import { NumberModel, NumberDocument } from '@/models/number';
import {  EpiModel , E911HistoryModel, PortConfigTemplateDocument, PortConfigTemplateModel,PortConfigTemplateParams } from '@/models';
import { E911ServiceFactory } from '@/services/emergencyService/EmergencyServiceFactory';
import { E911UpdateData, E911UpdateResult } from '@/services/emergencyService/BaseE911Service';

export class PortConfigTemplateController {
  constructor(
    private readonly portConfigTemplateModel: PortConfigTemplateModel
  ) {}

  async createPortConfigTemplate(req: FastifyRequest<{ Body: PortConfigTemplateDocument }>, res: FastifyReply) {
    const number = await this.portConfigTemplateModel.create(req.body);
    logger.info(`Number created: ${number}`);
    return respondJSON(number);
  }

  /**
   * Get Port Config Template
   */
  
  async getPortConfigTemplate(body: PortConfigTemplateParams): Promise<PortConfigTemplateDocument | Error> {
    
      const {portType, ataType} = body;
      const portConfigTemplate = await this.portConfigTemplateModel.findOne({
         lineType : portType,
         ataType : {$in: [ataType, 'both']}
        });  
        
        if(!portConfigTemplate){
          throw new Error('Port Config Template not found');
        }

      return portConfigTemplate;
   
  }

}

import { respondJ<PERSON><PERSON> } from '@/utils/response';
import { FastifyReply, FastifyRequest } from 'fastify';
import logger from '@/utils/logger';
import {  EpiModel , EpiDocument, EpiParams, EpiPort } from '@/models';
import { checkIsEscene, portConfigValidationRules } from '@/utils/helpers';
import { EdgeProxyService } from '@/services/EdgeProxy';

export class EpiController {
  constructor(
    private readonly epiModel: EpiModel,
    private readonly edgeProxyService: EdgeProxyService 
  ) {}

  async createEpi(req: FastifyRequest<{ Body: EpiDocument }>, res: FastifyReply) {
    const epi = await this.epiModel.create(req.body);
    logger.info(`Epi created: ${epi}`);
    return respondJSON(epi);
  }
  /**
   * Get Port Values
   */
  
  async getPortValues(epiId: string, portNumber: string): Promise<any | Error> {
    logger.info(`getPortValues ${epiId} ${portNumber}`);
    // return body;

    const epiPopulated = await this.epiModel.findByIdWithPopulate(epiId, ['assignedTo,epikboxes']);
    if (!epiPopulated) {
      throw new Error('Epi not found');
    }

    const isEscene = checkIsEscene(epiPopulated['macAddress']);
    const portNo = `port${portNumber}` ;
    const {
      OnHookTipRingVoltage,
      OffHookCurrentMax,
      ChannelTxGain,
      DTMFPlaybackLevel,
      ChannelRxGain,
      DTMFDetectMinGap,
      DTMFDetectMinLength,
      DTMFMethod,
      RingVoltage,
      DigitMapShortTimer,
      SilenceDetectSensitivity,
      CPCDelayTime,
      CPCDuration,
    } = epiPopulated[portNo as keyof EpiDocument] as EpiPort;

    let serialNumber: string | undefined;

    // Safely extract serialNumber from assignedTo if it exists and is an object
    if (
      epiPopulated.assignedTo &&
      typeof epiPopulated.assignedTo === 'object' &&
      'serialNumber' in epiPopulated.assignedTo
    ) {
      serialNumber = (epiPopulated.assignedTo as any).serialNumber;
    } else {
      serialNumber = undefined;
    }
    if(!serialNumber){
      throw new Error('Serial number not found');
    }

    const realPortConfigValues = await this.edgeProxyService.PortConfigValues(serialNumber, epiPopulated['macAddress'], portNumber.toString());
    if(!realPortConfigValues){
      throw new Error('Port config values not found');
    }

    const defaultRingVoltage = isEscene ? '4' : '70';
    const mappingObj = {
      OnHookTipRingVoltage: realPortConfigValues.OnhookVolts,
      OffHookCurrentMax: realPortConfigValues.OffhookCurrent,
      DTMFDetectMinLength: realPortConfigValues.DtmfDetectLength,
      DTMFDetectMinGap: realPortConfigValues.DtmfDetectGap,
      ChannelTxGain: realPortConfigValues.TxGain,
      ChannelRxGain: realPortConfigValues.RxGain,
      DTMFMethod: realPortConfigValues.DtmfMethod,
      DTMFPlaybackLevel: realPortConfigValues.DtmfPlaybackLevel,
      RingVoltage: realPortConfigValues.RingVoltage || defaultRingVoltage,
      DigitMapShortTimer: realPortConfigValues.DigitMapShortTimer
        ? realPortConfigValues.DigitMapShortTimer
        : '2',
      SilenceDetectSensitivity: realPortConfigValues.silenceDetectSensitivity,
      CPCDelayTime: realPortConfigValues.CpcDelayTime,
      CPCDuration: realPortConfigValues.CpcDuration,
    };
    const resultOdd = {
      data: {
        id: '973420001',
        cfgType: '1',
        device: '9734200',
        settings: [
          {
            id: '397166282',
            name: 'VoiceService.1.X_FXS.1.Settings.OnHookTipRingVoltage',
            dbValue: OnHookTipRingVoltage,
            value: mappingObj.OnHookTipRingVoltage,
            useDefault: false,
          },
          {
            id: '2845626946',
            name: 'VoiceService.1.X_FXS.1.Settings.OffHookCurrentMax',
            dbValue: OffHookCurrentMax,
            value: mappingObj.OffHookCurrentMax,
            useDefault: false,
          },
          {
            id: '3197346679',
            name: 'VoiceService.1.X_FXS.1.Settings.DTMFPlaybackLevel',
            dbValue: DTMFPlaybackLevel,
            value: mappingObj.DTMFPlaybackLevel,
            useDefault: false,
          },
          {
            id: '1906740087',
            name: 'VoiceService.1.X_FXS.1.Settings.DTMFDetectMinLength',
            dbValue: DTMFDetectMinLength,
            value: mappingObj.DTMFDetectMinLength,
            useDefault: false,
          },
          {
            id: '4117058733',
            name: 'VoiceService.1.X_FXS.1.Settings.DTMFDetectMinGap',
            dbValue: DTMFDetectMinGap,
            value: mappingObj.DTMFDetectMinGap,
            useDefault: false,
          },
          {
            id: '1481122353',
            name: 'VoiceService.1.X_FXS.1.Settings.ChannelTxGain',
            dbValue: ChannelTxGain,
            value: mappingObj.ChannelTxGain,
            useDefault: false,
          },
          {
            id: '1402851567',
            name: 'VoiceService.1.X_FXS.1.Settings.ChannelRxGain',
            dbValue: ChannelRxGain,
            value: mappingObj.ChannelRxGain,
            useDefault: false,
          },
          {
            id: '940393563',
            name: 'VoiceService.1.VoiceProfile.3.DTMFMethod',
            dbValue: DTMFMethod,
            value: mappingObj.DTMFMethod,
            useDefault: false,
          },
          {
            id: '1299843205',
            name: 'VoiceService.1.X_FXS.1.Ringer.RingVoltage',
            dbValue: RingVoltage,
            value: mappingObj.RingVoltage,
            useDefault: false,
          },
          {
            id: '3325204637',
            name: 'VoiceService.1.X_FXS.1.Timer.DigitMapShortTimer',
            dbValue: DigitMapShortTimer,
            value: mappingObj.DigitMapShortTimer,
            useDefault: false,
          },
          {
            id: '3025783732',
            name: 'VoiceService.1.X_FXS.1.Settings.SilenceDetectSensitivity',
            dbValue: SilenceDetectSensitivity,
            value: mappingObj.SilenceDetectSensitivity,
            useDefault: false,
          },
          {
            id: '2891384561',
            name: 'VoiceService.1.X_FXS.1.Timer.CPCDelayTime',
            dbValue: CPCDelayTime,
            value: mappingObj.CPCDelayTime,
            useDefault: false,
          },
          {
            id: '682279193',
            name: 'VoiceService.1.X_FXS.1.Timer.CPCDuration',
            dbValue: CPCDuration,
            value: mappingObj.CPCDuration,
            useDefault: false,
          },
        ],
      },
    };

    const resultEven = {
      data: {
        id: '973420001',
        cfgType: '1',
        device: '9734200',
        settings: [
          {
            id: '3832616587',
            name: 'VoiceService.1.X_FXS.2.Settings.OnHookTipRingVoltage',
            dbValue: OnHookTipRingVoltage,
            value: mappingObj.OnHookTipRingVoltage,
            useDefault: false,
          },
          {
            id: '741921187',
            name: 'VoiceService.1.X_FXS.2.Settings.OffHookCurrentMax',
            dbValue: OffHookCurrentMax,
            value: mappingObj.OffHookCurrentMax,
            useDefault: false,
          },
          {
            id: '1093640920',
            name: 'VoiceService.1.X_FXS.2.Settings.DTMFPlaybackLevel',
            dbValue: DTMFPlaybackLevel,
            value: mappingObj.DTMFPlaybackLevel,
            useDefault: false,
          },
          {
            id: '188737304',
            name: 'VoiceService.1.X_FXS.2.Settings.DTMFDetectMinLength',
            dbValue: DTMFDetectMinLength,
            value: mappingObj.DTMFDetectMinLength,
            useDefault: false,
          },
          {
            id: '148794350',
            name: 'VoiceService.1.X_FXS.2.Settings.DTMFDetectMinGap',
            dbValue: DTMFDetectMinGap,
            value: mappingObj.DTMFDetectMinGap,
            useDefault: false,
          },
          {
            id: '657561874',
            name: 'VoiceService.1.X_FXS.2.Settings.ChannelTxGain',
            dbValue: ChannelTxGain,
            value: mappingObj.ChannelTxGain,
            useDefault: false,
          },
          {
            id: '579291088',
            name: 'VoiceService.1.X_FXS.2.Settings.ChannelRxGain',
            dbValue: ChannelRxGain,
            value: mappingObj.ChannelRxGain,
            useDefault: false,
          },
          {
            id: '940393563',
            name: 'VoiceService.1.VoiceProfile.3.DTMFMethod',
            dbValue: DTMFMethod,
            value: mappingObj.DTMFMethod,
            useDefault: false,
          },
          {
            id: '429995750',
            name: 'VoiceService.1.X_FXS.2.Ringer.RingVoltage',
            dbValue: RingVoltage,
            value: mappingObj.RingVoltage,
            useDefault: false,
          },
          {
            id: '4116007870',
            name: 'VoiceService.1.X_FXS.2.Timer.DigitMapShortTimer',
            dbValue: DigitMapShortTimer,
            value: mappingObj.DigitMapShortTimer,
            useDefault: false,
          },
          {
            id: '4069692405',
            name: 'VoiceService.1.X_FXS.2.Settings.SilenceDetectSensitivity',
            dbValue: SilenceDetectSensitivity,
            value: mappingObj.SilenceDetectSensitivity,
            useDefault: false,
          },
          {
            id: '2021537106',
            name: 'VoiceService.1.X_FXS.2.Timer.CPCDelayTime',
            dbValue: CPCDelayTime,
            value: mappingObj.CPCDelayTime,
            useDefault: false,
          },
          {
            id: '3649382234',
            name: 'VoiceService.1.X_FXS.2.Timer.CPCDuration',
            dbValue: CPCDuration,
            value: mappingObj.CPCDuration,
            useDefault: false,
          },
        ],
      },
    };
    const validationRules = portConfigValidationRules(isEscene);

    let result: any = parseInt(portNumber) % 2 === 0 ? resultEven : resultOdd;

    if (!result.data) {
      result.data = {
        id: '973420001',
        cfgType: '1',
        device: '9734200',
        settings: [],
      };
    }
   
    const extraConfigValues = {
      modemMode: realPortConfigValues.ModemMode,
      t38Enabled: realPortConfigValues.T38Enabled,
      faxEnabled: realPortConfigValues.faxEnabled,
      JitterMinDelay: realPortConfigValues.JitterBufferMinDeley,
      JitterMaxDelay: realPortConfigValues.JitterBufferMaxDeley,
      ThreeWayCalling: realPortConfigValues.ThreeWayCalling,
      vadEnable: String(realPortConfigValues.VadEnable).toLowerCase() === "true", 
    }

    result = { 
      ...result.data, 
      extraConfigValues 
    };  
    result = { ...result, validationRules };

    // return result;
    return epiPopulated[portNo as keyof EpiDocument] as EpiPort;
  }

  async setPortValues(epiId: string, portValues: Partial<EpiPort> & { portNumber: string }): Promise<any | Error> {
    logger.info(`setPortValues ${epiId} ${portValues}`);

    const epiPopulated = await this.epiModel.findByIdWithPopulate(epiId, ['assignedTo,epikboxes']);
    if (!epiPopulated) {
      throw new Error('Epi not found');
    }

    let portNo = `port${portValues.portNumber}`;
    let updatedPortConfig = {
      ...(portValues as any),
      configOverride: true,
      // rebootRequired: true, // todo: reboot the epi
    };
    delete updatedPortConfig.portNumber;

    const updatedPortConfigValues = { ...(epiPopulated as any)[portNo], ...updatedPortConfig };

    // todo: reboot the epi
    const updatedEpi = await this.epiModel.update(epiId, { [portNo]: updatedPortConfigValues }, { returnDocument: 'after' });
    if (!updatedEpi) {
      throw new Error('Epi not found after update');
    }
    let updatedPort = updatedEpi[portNo as keyof EpiDocument] as EpiPort;
    return updatedPort;
  }
}

import {  EpikBoxModel,  } from '@/models/epikbox';
import fastify, { FastifyReply, FastifyRequest } from 'fastify';
import { respondJSON } from '@/utils/response';
import logger from '@/utils/logger';
import { NumberModel, NumberDocument } from '@/models/number';
import {  EpiModel , E911HistoryModel } from '@/models';
import { E911ServiceFactory } from '@/services/emergencyService/EmergencyServiceFactory';
import { E911UpdateData, E911UpdateResult } from '@/services/emergencyService/BaseE911Service';

export class NumberController {
  constructor(
    private readonly numberModel: NumberModel,
    private readonly epikBoxModel: EpikBoxModel,
    private readonly epiModel: EpiModel,
    private readonly e911ServiceFactory: E911ServiceFactory,
    private readonly e911HistoryModel: E911HistoryModel
  ) {}

  async createNumber(req: FastifyRequest<{ Body: NumberDocument }>, res: FastifyReply) {
    const number = await this.numberModel.create(req.body);
    logger.info(`Number created: ${number}`);
    return respondJSON(number);
  }

  /**
   * Get Number by id
   * @param req Express Request
   * @param res Express Response
   */

  async getNumberById(req: FastifyRequest<{ Params: { id: string } }>) {
    try {
      const { id } = req.params;
      const number = await this.numberModel.findById(id);
      if (!number) {
        logger.error(`Number not found: ${id}`);
        return respondJSON(null);
      }
      return respondJSON(number);
    } catch (error) {
      logger.error(`Internal server error: ${error}`);
      return respondJSON(error);
    }
  }
  
  async checkIfE911NumberAlreadyAssignedToOtherDevice(number: string) {
    const epikbox = await this.epikBoxModel.findOne({ e911Number: number });
    return epikbox ? epikbox.serialNumber : false;
  }
  
  /**
   * Update E911 number using the appropriate carrier service
   */
  async updateE911Number(numberId: string, body: E911UpdateData): Promise<E911UpdateResult> {
    try {
      logger.info(`updateNumberE911 ${numberId} ${JSON.stringify(body)}`);

      let number = await this.numberModel.findById(numberId);
      if (!number) {
        throw new Error('Number not found!');
      }

      // todo remove this:
      // use only for testing purpose
      number.number = "+9709650813";

      // Check if e911 number is already assigned to other boxes
      const isE911Assigned = await this.checkIfE911NumberAlreadyAssignedToOtherDevice(number.number);
      if (isE911Assigned) {
        throw new Error(`${number.number} is already assigned to another Epik edge device(s) ${isE911Assigned}.`);
      }

      const obi = await this.epiModel.findOne({
        $or: [
          {"port1.assignedNumberRef": number._id},
          {"port2.assignedNumberRef": number._id}
        ]
      });

      if (!obi) {
        throw new Error(`${number.number} is not assigned to Epik edge device(s).`);
      }

      if (number.linkedBox?.toString() !== body.boxId) {
        throw new Error(`${number.number} is not assigned to Epik edge device(s).`);
      }

      // Use the carrier service factory to update E911
      const result = await this.e911ServiceFactory.updateE911Number(number, body);

      console.info(`result updateNumberE911`, result);
      
      if (result.success && obi) {
        // Update the OBI device timestamp
        let port = "";
        let numberPort = number.port;
        if (numberPort) {
          port = (parseInt(numberPort.split(' ')[1]) % 2 === 0) ? 'port2' : 'port1';
        }
        
        if (port) {
          await this.epiModel.update(
            obi._id.toString(),
            { [`${port}.updatedOn`]: new Date() }
          );
        }
      }

      return result;
    } catch (error) {
      logger.error(`Error in updateNumberE911: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }


  /**
   * Get carrier information for a number
   */
  async getE911NumberInfo(numberId: string): Promise<E911UpdateResult> {
    try {
      let number = await this.numberModel.findById(numberId);
      if (!number) {
        throw new Error('Number not found!');
      }
      

      // get e911 number info and update address in db 
      number.number = "+9709650813";
      const result = await this.e911ServiceFactory.getE911NumberInfo(number);

      // update address in db if changed from previous result
      let e911Data
      if (!number.e911Info) {
        e911Data = { ...result.data, comment: 'E911 number Populated' }
      } else {
        e911Data = { ...result.data, comment: 'E911 number Updated' }
      }
      if(result.success){
        const e911Info = number.e911Info;
        if(
          e911Info?.address1 !== result.data.address1 || 
          e911Info?.address2 !== result.data.address2 || 
          e911Info?.city !== result.data.city ||
          e911Info?.state !== result.data.state || 
          e911Info?.zip !== result.data.zip || 
          e911Info?.country !== result.data.country ||
          e911Info?.callername !== result.data.callername ||
          e911Info?.vendor !== result.data.vendor
        ){
          await this.numberModel.update(numberId, { e911Info: result.data, updatedAt: new Date() });
          const e911History = {
            number: number.number,
            e911Info: e911Data,
            date: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          }
          await this.e911HistoryModel.create(e911History);
        }
      }
      return result;
    } catch (error) {
      logger.error(`Error getting carrier info: ${error}`);
      return {
        success: false,
        error: 'Unknown error occurred'
      };
    }
  }

  /**
   * Remove E911 number using the appropriate carrier service
   */
  async removeE911Number(numberId: string): Promise<E911UpdateResult> {
    try {
      let number = await this.numberModel.findById(numberId);
      if (!number) {
        throw new Error('Number not found!');
      }
      number.number = "+9709650813";
      const result = await this.e911ServiceFactory.removeE911Number(number);
      console.info(`result removeE911Number`, result);
      await this.numberModel.update(numberId, { e911Info: undefined, updatedAt: new Date() });
      const e911History = {
        number: number.number,
        e911Info: undefined,
        date: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        comment: 'E911 number removed'
      }
      await this.e911HistoryModel.create(e911History);
      return result;
    } catch (error) {
      logger.error(`Error in removeE911Number: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

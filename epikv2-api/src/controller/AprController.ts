import { FastifyInstance } from 'fastify';
import { AprService, APRToggleRequest, APRTimerRequest, APRUpdateRequest } from '@/services/AprService';
import { createModuleLogger } from '@/utils/logger';
import { ForbiddenError, NotFoundError, ValidationError } from '@/middleware/errorHandler';

const logger = createModuleLogger('AprController');

export class AprController {
  private aprService: AprService;

  constructor(aprService: AprService) {
    this.aprService = aprService;
  }

  /**
   * Toggle APR on/off for a specific port
   */
  async toggleAlarmRelay(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRToggleRequest
  ) {
    try {
      logger.info('Toggle APR request', { userId, boxSerial, request });
      return await this.aprService.toggleAlarmRelay(fastify, userId, boxSerial, request);
    } catch (error) {
      logger.error('Toggle APR failed', { error, userId, boxSerial, request });
      throw error;
    }
  }

  /**
   * Create alarm relay timer and initiate test
   */
  async createAlarmRelayTimer(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRTimerRequest
  ) {
    try {
      logger.info('Create APR timer request', { userId, boxSerial, request });
      return await this.aprService.createAlarmRelayTimer(fastify, userId, boxSerial, request);
    } catch (error) {
      logger.error('Create APR timer failed', { error, userId, boxSerial, request });
      throw error;
    }
  }

  /**
   * Get alarm relay timer status
   */
  async getAlarmRelayTimer(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ) {
    try {
      logger.info('Get APR timer request', { userId, boxSerial });
      return await this.aprService.getAlarmRelayTimer(fastify, userId, boxSerial);
    } catch (error) {
      logger.error('Get APR timer failed', { error, userId, boxSerial });
      throw error;
    }
  }

  /**
   * Update alarm relay session (finish timer)
   */
  async updateAlarmRelaySession(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ) {
    try {
      logger.info('Update APR session request', { userId, boxSerial });
      return await this.aprService.updateAlarmRelaySession(fastify, userId, boxSerial);
    } catch (error) {
      logger.error('Update APR session failed', { error, userId, boxSerial });
      throw error;
    }
  }

  /**
   * Update alarm relay settings
   */
  async updateAlarmRelay(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRUpdateRequest
  ) {
    try {
      logger.info('Update APR request', { userId, boxSerial, request });
      return await this.aprService.updateAlarmRelay(fastify, userId, boxSerial, request);
    } catch (error) {
      logger.error('Update APR failed', { error, userId, boxSerial, request });
      throw error;
    }
  }

  /**
   * Fetch alarm relay test results
   */
  async fetchAlarmRelayResults(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    numberID: string,
    portNumber: string
  ) {
    try {
      logger.info('Fetch APR results request', { userId, boxSerial, numberID, portNumber });
      return await this.aprService.fetchAlarmRelayResults(
        fastify,
        userId,
        boxSerial,
        numberID,
        portNumber
      );
    } catch (error) {
      logger.error('Fetch APR results failed', { error, userId, boxSerial, numberID, portNumber });
      throw error;
    }
  }

  /**
   * Get alarm relay status
   */
  async getAlarmRelayStatus(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    portNumber: string
  ) {
    try {
      logger.info('Get APR status request', { userId, boxSerial, portNumber });
      return await this.aprService.getAlarmRelayStatus(fastify, userId, boxSerial, portNumber);
    } catch (error) {
      logger.error('Get APR status failed', { error, userId, boxSerial, portNumber });
      throw error;
    }
  }

  /**
   * Enable APR on port unconditionally (admin operation)
   */
  async enableAprOnPortUnconditionally(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    portNumber: string
  ) {
    try {
      logger.info('Enable APR unconditionally request', { userId, boxSerial, portNumber });
      return await this.aprService.enableAprOnPortUnconditionally(
        fastify,
        userId,
        boxSerial,
        portNumber
      );
    } catch (error) {
      logger.error('Enable APR unconditionally failed', { error, userId, boxSerial, portNumber });
      throw error;
    }
  }

  /**
   * Remove alarm protocol settings
   */
  async removeAlarmProtocolSettings(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    payload: { _id: string; alarmRelayInfo: string }
  ) {
    try {
      logger.info('Remove APR settings request', { userId, boxSerial, payload });
      await this.aprService.removeAlarmProtocolSettings(fastify, userId, boxSerial, payload);
      return { success: true, message: 'Alarm protocol settings removed successfully' };
    } catch (error) {
      logger.error('Remove APR settings failed', { error, userId, boxSerial, payload });
      throw error;
    }
  }

  /**
   * Check and disable APR on timer expired (cleanup operation)
   */
  async checkAndDisableAprOnTimerExpired(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ) {
    try {
      logger.info('APR timer cleanup request', { userId, boxSerial });
      await this.aprService.checkAndDisableAprOnTimerExpired(fastify, userId, boxSerial);
      return { success: true, message: 'APR timer cleanup completed' };
    } catch (error) {
      logger.error('APR timer cleanup failed', { error, userId, boxSerial });
      throw error;
    }
  }
}

import { NotFoundError } from '@/middleware/errorHandler';
import { EpikBoxDocument, EpikBoxModel, SpeedTest, FetchLtePerf,  } from '@/models/epikbox';
import { JSONResponse } from '@/types/response';
import logger from '@/utils/logger';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { NumberDocument, NumberModel } from '@/models/number';
import { NumberController } from '@/controller/NumberController';
import { E911HistoryModel, EpiModel } from '@/models';
import { E911ServiceFactory } from '@/services/emergencyService/EmergencyServiceFactory';
import { E911UpdateData } from '@/services/emergencyService/BaseE911Service';
import fs from 'node:fs';
interface NumberParams {
  id: string;
}

export async function numberRoutes(fastify: FastifyInstance) {
  const epikBoxModel = new EpikBoxModel(fastify);
  const numberModel = new NumberModel(fastify);
  const epiModel = new EpiModel(fastify);
  const e911HistoryModel = new E911HistoryModel(fastify);
  const e911ServiceFactory = new E911ServiceFactory();
  const numberController = new NumberController(
    numberModel, 
    epikBoxModel, 
    epiModel, 
    e911ServiceFactory,
    e911HistoryModel
  );
  
  fastify.get('/vmail-file/:numberId', async (req: FastifyRequest<{ Params: { numberId: string } }>, reply: FastifyReply) => {
    const { numberId } = req.params;
    const { filePath, mime, stat } = await fastify.services.numberService.getVoicemailFile(numberId);

    reply
      .type(mime)
      .header('Accept-Ranges', 'bytes')
      .header('Cache-Control', 'private, max-age=0, no-store');

    reply.header('Content-Length', stat.size);
    return reply.send(fs.createReadStream(filePath));
  });

  fastify.put(
    '/:id',
    async (
      request: FastifyRequest<{ Params: NumberParams; Body: Partial<NumberDocument> }>
    ): Promise<JSONResponse<NumberDocument>> => {
      const number = await numberModel.update(request.params.id, request.body);
      if (!number) {
        throw new NotFoundError('Number');
      }
      return respondJSON(number);
    }
  );

  // provision e911 number
  fastify.put(
    '/:id/e911',
    async (
      request: FastifyRequest<{ Params: NumberParams; Body: E911UpdateData }>
    ): Promise<JSONResponse<any>> => {
      const result = await numberController.updateE911Number(request.params.id, request.body);
      // Fix: E911UpdateResult does not have statusCode, so check for error property instead
      if (result?.error) {
        throw new NotFoundError(result.error || 'Failed to update E911');
      }
      return respondJSON(result);
    }
  );

  // get e911 number info and update address in db 
  fastify.get(
    '/:id/e911',
    async (
      request: FastifyRequest<{ Params: NumberParams }>
    ): Promise<JSONResponse<any>> => {
      const result = await numberController.getE911NumberInfo(request.params.id);
      return respondJSON(result);
    }
  );

  // remove e911 number
  fastify.delete(
    '/:id/e911',
    async (
      request: FastifyRequest<{ Params: NumberParams }>
    ): Promise<JSONResponse<any>> => {
      const result = await numberController.removeE911Number(request.params.id);
      return respondJSON(result);
    }
  );
}

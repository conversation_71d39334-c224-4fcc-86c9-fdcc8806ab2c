import { NotFoundError } from '@/middleware/errorHandler';
import { AlarmRelayDocument, AlarmRelayModel ,APRTestResultResponse} from '@/models/alarmRelay';
import { NumberDocument, NumberModel } from '@/models/number';
import { EpikBoxModel } from '@/models/epikbox';
import { JSONResponse } from '@/types/response';
import logger from '@/utils/logger';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

interface AlarmRelayParams {
  id: string;
}

interface CreateAlarmRelayBody {
  isEnabled?: boolean;
  protoclSelection?: string;
  operationMode?: string;
  isTestPassed?: boolean;
  isTestStarted?: boolean;
  timerSession?: string;
  deleted?: boolean;
  companionPort?: string;
}

interface AlarmRelayRequest {
  numberID: string;
  isEnabled?: boolean;
  obiID?: string;
  portNumber?: number;
  isBoxTab?: boolean;
  boxId?: string;
}
interface AlarmRelayTestRequest {
  numberID: string;
  portocolSelection: string;
  portNumber: number;
  companionPort: string;
  isBoxTab: boolean;
  boxId: string;
}
  
function isAlarmRelaySupported(numberType: string){
  return ['alarm','fireAlarm'].includes(numberType);
}

export async function alarmRelayRoutes(fastify: FastifyInstance) {
    const alarmRelayModel = new AlarmRelayModel(fastify);
    const numberModel = new NumberModel(fastify);
    const epikBoxModel = new EpikBoxModel(fastify);

    
  fastify.put(
    '/:id',
    async (
      request: FastifyRequest<{ Params: { id: string }, Body: CreateAlarmRelayBody }>
    ): Promise<JSONResponse<any>> => {
      console.log('alarm relay request', request.params.id, request.body);
      const alarmRelay = await alarmRelayModel.update(request.params.id, request.body);
      console.log('alarm relay', alarmRelay);
      return respondJSON(alarmRelay);
    }
  );



  fastify.post(
    '/:boxId/alarmRelayToggle',
    async (
      request: FastifyRequest<{ Params: { boxId: string }, Body: AlarmRelayRequest }>
    ): Promise<JSONResponse<any>> => {
      console.log('alarm relay request', request.params.boxId, request.body);


      const epikBox = await epikBoxModel.findById(request.params.boxId);
      if (!epikBox) {
        throw new Error('EpikBox not found!');
      }
      const { numberID, isEnabled, obiID, portNumber } = request.body;

      // todo
      // const existingTimer = await BoxTimer.findOne({ box: boxID });    
      // if (existingTimer) {
      //   return {
      //     success: false,
      //     message: 'Please wait for the Timer',
      //   };
      // }
    
      const number = await numberModel.findById(numberID);
      if (!number) {
        throw new Error('Number not found!');
      }
      if (!isAlarmRelaySupported(number.type)) {
        throw new Error('Number type not supported!');
      }
      let isAlarmRelayExists = null;

      if(number.alarmRelayInfo){
          isAlarmRelayExists = await alarmRelayModel.findById(number.alarmRelayInfo.toString());
        }

      if (isAlarmRelayExists && isEnabled) {
          const endPoint = `/aprtestresults/${portNumber}`;
    
          const testResponse = await fastify.services.edgeProxyService.APRTestResult( epikBox.serialNumber, endPoint);
          if(isAlarmRelayExists.isEnabled  && testResponse?.result === "Failed"){
            await fastify.services.edgeProxyService.EnableDisableAPRTest(epikBox.serialNumber, `/aprtoggle/${portNumber}/disable`);
          }
        }
    
    const baseEndPoint = `/aprtoggle/${portNumber}`;

    const endpoint = isEnabled
      ? `${baseEndPoint}/enable`
      : `${baseEndPoint}/disable`;
    let enableDisableResponse = await fastify.services.edgeProxyService.EnableDisableAPRTest(epikBox.serialNumber, endpoint);
  
    let obj = {
      isEnabled: enableDisableResponse ?? false,
      operationMode: !isEnabled || !isAlarmRelayExists ? "Test" : isAlarmRelayExists.operationMode,
      isTestPassed: !isEnabled || !isAlarmRelayExists ? false : isAlarmRelayExists.isTestPassed,
      isTestStarted: !isEnabled || !isAlarmRelayExists ? false : isAlarmRelayExists.isTestStarted
    }
    
    if (isAlarmRelayExists) {
      await alarmRelayModel.update(
        number.alarmRelayInfo.toString(),
        obj,
        true as any
      );
    } else {
      let apr = await alarmRelayModel.create(obj as any as AlarmRelayDocument)
      await numberModel.update(number._id.toString(), {
        alarmRelayInfo: apr._id?.toString()
      })
    }

    const numberWithAlarmRelay = await numberModel.findById(numberID);
      return respondJSON(numberWithAlarmRelay);
    }
  );


  fastify.post(
    '/:boxId/initiateAPRTest',
    async (
      request: FastifyRequest<{ Params: { boxId: string }, Body: AlarmRelayTestRequest }>
    ): Promise<JSONResponse<any>> => {
      console.log('alarm relay request', request.params.boxId, request.body);
      const boxID = request.params.boxId;
      const epikBox = await epikBoxModel.findById(boxID);
      if (!epikBox) {
        throw new Error('EpikBox not found!');
      }

      const { numberID, portocolSelection, portNumber, companionPort, isBoxTab, boxId } = request.body;
    
    
    // todo
      // const existingTimer = await BoxTimer.findOne({ box: boxID });
      // if (existingTimer) {
      //   return {
      //     success: false,
      //     message: 'Please wait for the Timer',
      //   };
      // }
    
      const number = await numberModel.findById(numberID);
      if (!number) {
        throw new Error('Number not found!');
      }

      
      if(!isAlarmRelaySupported(number.type)){
        throw new Error('Number type not supported!');
      }

 
      try {
        await fastify.services.edgeProxyService.EnableDisableAPRTest(epikBox.serialNumber, `aprtoggle/${portNumber}/enable`);
      } catch (error) {
        console.log(error, 'APR enable failed')
      }

    const endpoint = `aprtest/${portNumber}`;
    let aprTestResponse = await fastify.services.edgeProxyService.APRTestInitiate(epikBox.serialNumber, endpoint);

    
    // ******** Todo ********
  
    /*
      if (companionPort) {
        let portNum = companionPort.split('').pop()
        console.log({ linkedBox: boxID, port: companionPort })
        let num = await Number.findOne({ linkedBox: boxID, port: `port ${portNum}` })
        let obi = await obiController.getObiByAssignedNumber(num._id)
        await obiController.enableDisablePort(userID, obi._id, portNum, "disable")
      }
      
      const alarmNote = new NotesModel({
        user: userID,
        epikBox: boxID,
        note: 'Initiate Alarm Relay Test',
        hasAttachment: 'No',
      });

      await alarmNote.save();
      Logger.info(`Note added for box : ${boxID}`);

      const newSession = new BoxTimerSession({
        box: boxID,
        user: userID,
        state: PENDING_STATE,
        type: 'alarm',
      });

      const savedSession = await newSession.save();
      Logger.info(`new boxLockSession added for box id ${boxID}`);
    */

  await alarmRelayModel.update(
    number.alarmRelayInfo.toString(),
    {
      portocolSelection,
      operationMode: 'Test',
      isTestPassed: false,
      isTestStarted: true,
      isEnabled: true,
      // BoxTimerSession: savedSession._id,
      companionPort
    }
  );

  // todo
  // const boxTimer = new BoxTimer({
  //   box: boxID,
  //   user: userID,
  //   session: savedSession._id,
  // });
  // await boxTimer.save();

    const numberWithAlarmRelay = await numberModel.findById(number._id.toString());
      return respondJSON(numberWithAlarmRelay);
    }
  );

  fastify.get(
    '/:boxId/alarmStatus',
    async (
      request: FastifyRequest<{ Params: { boxId: string }, Querystring: { portNumber: string } }>
    ): Promise<JSONResponse<any>> => {
      console.log('alarm relay status request', request.params.boxId, request.query.portNumber);
      const boxID = request.params.boxId;
      const portNumber = request.query.portNumber;
      const epikBox = await epikBoxModel.findById(boxID);
      if (!epikBox) {
        throw new Error('EpikBox not found!');
      }
      const aprStatus = await fastify.services.edgeProxyService.APRStatus(epikBox.serialNumber, `/aprstatus/${portNumber}`);
      return respondJSON(aprStatus);
    }
  );

}



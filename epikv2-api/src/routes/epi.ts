
import { JSONResponse } from '@/types/response';
import logger from '@/utils/logger';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyRequest } from 'fastify';
import { EpiDocument, EpiPort } from '@/models/epi';
import { EpiModel, EpiParams } from '@/models';
import { EpiController } from '@/controller/epiController';
import { EdgeProxyService } from '@/services/EdgeProxy';


export async function epiRoutes(fastify: FastifyInstance) {
  const edgeProxyService = new EdgeProxyService();
  const epiController = new EpiController(new EpiModel(fastify), edgeProxyService);
  
  fastify.get(
    '/:epiId/portValues',
    async (
      request: FastifyRequest<{ Params: { epiId: string }, Query:EpiParams }>
    ): Promise<JSONResponse<any>> => {
      const query = request.query as EpiParams;
      logger.info(`getPortValues  ${request.params.epiId} ${query.portNumber}`);
      try {
        const result = await epiController.getPortValues(request.params.epiId, query.portNumber);
        // const result = { adf: query.portNumber };
        logger.info(`getPortValues result  ${JSON.stringify(result)}`);
        return respondJSON(result);
      } catch (error) {
        return respondJSON(error as Error);
      }
    }
  );

  fastify.put(
    '/:epiId/portValues',
    async (
      request: FastifyRequest<{ Params: { epiId: string }, Body: Partial<EpiPort> & { portNumber: string } }>
    ): Promise<JSONResponse<any>> => {
      const body = request.body as Partial<EpiPort> & { portNumber: string };
      logger.info(`getPortValues  ${request.params.epiId} ${body}`);
      try {
        const result = await epiController.setPortValues(request.params.epiId, body );
        // const result = { adf: query.portNumber };
        logger.info(`getPortValues result  ${JSON.stringify(result)}`);
        return respondJSON(result);
      } catch (error) {
        return respondJSON(error as Error);
      }
    }
  );

}

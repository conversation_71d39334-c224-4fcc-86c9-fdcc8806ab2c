import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { AprController } from '@/controller/AprController';
import { AprService } from '@/services/AprService';
import { JSONResponse } from '@/types/response';
import { respondJSON } from '@/utils/response';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('AprRouter');

// Request/Response interfaces
interface APRToggleBody {
  numberID: string;
  isEnabled: boolean;
  obiID?: string;
  portNumber: string;
}

interface APRTimerBody {
  numberID: string;
  portocolSelection: string;
  portNumber: string;
  companionPort?: string;
}

interface APRUpdateBody {
  numberID: string;
  operationMode: string;
  portocolSelection?: string;
}

interface APRRemoveBody {
  _id: string;
  alarmRelayInfo: string;
}

interface BoxSerialParams {
  boxSerial: string;
}

interface APRTestParams extends BoxSerialParams {
  numberID: string;
  portNumber: string;
}

interface APRStatusParams extends BoxSerialParams {
  portNumber: string;
}

interface APRPortParams extends BoxSerialParams {
  portNumber: string;
}

export async function aprRoutes(fastify: FastifyInstance): Promise<void> {
  const aprService = new AprService();
  const aprController = new AprController(aprService);

  // Toggle APR on/off
  fastify.post<{ Params: BoxSerialParams; Body: APRToggleBody }>(
    '/:boxSerial/toggle',
    {
      schema: {
        description: 'Toggle APR on/off for a specific port',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        },
        body: {
          type: 'object',
          properties: {
            numberID: { type: 'string' },
            isEnabled: { type: 'boolean' },
            obiID: { type: 'string' },
            portNumber: { type: 'string' }
          },
          required: ['numberID', 'isEnabled', 'portNumber']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.toggleAlarmRelay(
          fastify,
          userId,
          boxSerial,
          request.body
        );
        
        return respondJSON(result);
      } catch (error) {
        logger.error('APR toggle failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Create APR timer and initiate test
  fastify.post<{ Params: BoxSerialParams; Body: APRTimerBody }>(
    '/:boxSerial/timer',
    {
      schema: {
        description: 'Create alarm relay timer and initiate test',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        },
        body: {
          type: 'object',
          properties: {
            numberID: { type: 'string' },
            portocolSelection: { type: 'string' },
            portNumber: { type: 'string' },
            companionPort: { type: 'string' }
          },
          required: ['numberID', 'portocolSelection', 'portNumber']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.createAlarmRelayTimer(
          fastify,
          userId,
          boxSerial,
          request.body
        );
        
        return respondJSON(result);
      } catch (error) {
        logger.error('APR timer creation failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Get APR timer status
  fastify.get<{ Params: BoxSerialParams }>(
    '/:boxSerial/timer',
    {
      schema: {
        description: 'Get alarm relay timer status',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.getAlarmRelayTimer(fastify, userId, boxSerial);
        
        return respondJSON(result);
      } catch (error) {
        logger.error('Get APR timer failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Update APR session (finish timer)
  fastify.put<{ Params: BoxSerialParams }>(
    '/:boxSerial/session',
    {
      schema: {
        description: 'Update alarm relay session (finish timer)',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.updateAlarmRelaySession(fastify, userId, boxSerial);
        
        return respondJSON(result);
      } catch (error) {
        logger.error('Update APR session failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Update APR settings
  fastify.put<{ Params: BoxSerialParams; Body: APRUpdateBody }>(
    '/:boxSerial/update',
    {
      schema: {
        description: 'Update alarm relay settings',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        },
        body: {
          type: 'object',
          properties: {
            numberID: { type: 'string' },
            operationMode: { type: 'string' },
            portocolSelection: { type: 'string' }
          },
          required: ['numberID', 'operationMode']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.updateAlarmRelay(
          fastify,
          userId,
          boxSerial,
          request.body
        );
        
        return respondJSON(result);
      } catch (error) {
        logger.error('Update APR failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Fetch APR test results
  fastify.get<{ Params: APRTestParams }>(
    '/:boxSerial/results/:numberID/:portNumber',
    {
      schema: {
        description: 'Fetch alarm relay test results',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' },
            numberID: { type: 'string' },
            portNumber: { type: 'string' }
          },
          required: ['boxSerial', 'numberID', 'portNumber']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial, numberID, portNumber } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.fetchAlarmRelayResults(
          fastify,
          userId,
          boxSerial,
          numberID,
          portNumber
        );
        
        return respondJSON(result);
      } catch (error) {
        logger.error('Fetch APR results failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Get APR status
  fastify.get<{ Params: APRStatusParams }>(
    '/:boxSerial/status/:portNumber',
    {
      schema: {
        description: 'Get alarm relay status',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' },
            portNumber: { type: 'string' }
          },
          required: ['boxSerial', 'portNumber']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial, portNumber } = request.params;
        const userId = request.user.id;
        
        const result = await aprController.getAlarmRelayStatus(
          fastify,
          userId,
          boxSerial,
          portNumber
        );
        
        return respondJSON(result);
      } catch (error) {
        logger.error('Get APR status failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Enable APR unconditionally (admin operation)
  fastify.post<{ Params: APRPortParams }>(
    '/:boxSerial/enable/:portNumber',
    {
      schema: {
        description: 'Enable APR on port unconditionally (admin operation)',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' },
            portNumber: { type: 'string' }
          },
          required: ['boxSerial', 'portNumber']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial, portNumber } = request.params;
        const userId = request.user.id;

        const result = await aprController.enableAprOnPortUnconditionally(
          fastify,
          userId,
          boxSerial,
          portNumber
        );

        return respondJSON(result);
      } catch (error) {
        logger.error('Enable APR unconditionally failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // Remove APR settings
  fastify.delete<{ Params: BoxSerialParams; Body: APRRemoveBody }>(
    '/:boxSerial/remove',
    {
      schema: {
        description: 'Remove alarm protocol settings',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        },
        body: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            alarmRelayInfo: { type: 'string' }
          },
          required: ['_id', 'alarmRelayInfo']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;

        const result = await aprController.removeAlarmProtocolSettings(
          fastify,
          userId,
          boxSerial,
          request.body
        );

        return respondJSON(result);
      } catch (error) {
        logger.error('Remove APR settings failed', { error });
        return respondJSON(error as Error);
      }
    }
  );

  // APR timer cleanup (admin operation)
  fastify.post<{ Params: BoxSerialParams }>(
    '/:boxSerial/cleanup',
    {
      schema: {
        description: 'Check and disable APR on timer expired (cleanup operation)',
        tags: ['APR'],
        params: {
          type: 'object',
          properties: {
            boxSerial: { type: 'string' }
          },
          required: ['boxSerial']
        }
      }
    },
    async (request, reply): Promise<JSONResponse<any>> => {
      try {
        const { boxSerial } = request.params;
        const userId = request.user.id;

        const result = await aprController.checkAndDisableAprOnTimerExpired(
          fastify,
          userId,
          boxSerial
        );

        return respondJSON(result);
      } catch (error) {
        logger.error('APR cleanup failed', { error });
        return respondJSON(error as Error);
      }
    }
  );
}

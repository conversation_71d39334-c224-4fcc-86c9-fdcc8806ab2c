import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { JSONResponse } from '@/types/response';
import { respondJSON } from '@/utils/response';
import { NotFoundError } from '@/middleware/errorHandler';
import fs from 'node:fs';
import { VmailBoxDocument } from '@/models/VmailBox';

export async function voiceMailRoutes(fastify: FastifyInstance) {

  fastify.get('/:obiId/:port/vmailbox', async (req: FastifyRequest<{ Params: { obiId: string, port: string}}>): Promise<JSONResponse<VmailBoxDocument>> => {
    const { obiId, port } = req.params as any;
    const vmailBox = await fastify.services.voiceMailService.getVoicemailBox(fastify, obiId, port);
    return respondJSON(vmailBox);
  });

  fastify.put('/:obiId/:port/vmailbox', async (req: FastifyRequest<{ Params: { obiId: string, port: string}}>): Promise<JSONResponse<VmailBoxDocument>> => {
    const { obiId, port } = req.params as any;
    const updated = await  fastify.services.voiceMailService.updateVoicemailBox(fastify, obiId, port, req.body);
    return respondJSON(updated);
  });

  fastify.get('/:obiId/:port/vmailbox/file', async (req: FastifyRequest<{ Params: { obiId: string, port: string}}>, reply: FastifyReply) => {
    const { obiId, port } = req.params as any;
    const { filePath, mime, stat } = await fastify.services.voiceMailService.getVoicemailFile(fastify, obiId, port);

    reply
      .type(mime)
      .header('Accept-Ranges', 'bytes')
      .header('Cache-Control', 'private, max-age=0, no-store');

    reply.header('Content-Length', stat.size);
    return reply.send(fs.createReadStream(filePath));
  });

  fastify.put(
    '/:id/disableVM',
    async (request: FastifyRequest<{ Params: { id: string }, Body: {
      number: string;
      port: string;
      boxId: string;
      isBoxTab?: boolean;
    }}>): Promise<JSONResponse<any>> => {
      const { id: obiId } = request.params;
      const body = request.body;

      console.log("request.body101", body)

      if (!body?.port || !body?.boxId) {
        throw new NotFoundError('Missing required fields: port or boxId');
      }

      const result = await fastify.services.voiceMailService.disableVM(
        fastify,
        obiId,
        body
      );

      return respondJSON(result);
    }
  );

}


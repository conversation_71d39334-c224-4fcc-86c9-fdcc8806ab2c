import { NotFoundError } from '@/middleware/errorHandler';
import { JSONResponse } from '@/types/response';
import logger from '@/utils/logger';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { PortConfigTemplateDocument } from '@/models/portConfigTemplate';
import { PortConfigTemplateModel, PortConfigTemplateParams } from '@/models';
import { PortConfigTemplateController } from '@/controller/PortConfigTemplate';


export async function portConfigTemplateRoutes(fastify: FastifyInstance) {
  const portConfigTemplateController = new PortConfigTemplateController(new PortConfigTemplateModel(fastify));
  
  fastify.post(
    '/getportconfigtemplate',
    async (
      request: FastifyRequest<{ Body: PortConfigTemplateParams }>
      ): Promise<JSONResponse<PortConfigTemplateDocument| Error| undefined>> => {
        logger.info(`Getting port config template: ${request.body}`);
      try {
      const result = await portConfigTemplateController.getPortConfigTemplate(request.body);
        if (result instanceof Error) {
          logger.error(`Error getting port config template26: ${result}`);
          return respondJSON(result);
        }
        return respondJSON(result);
      } catch (error) {
        logger.error(`Error getting port config template30: ${error}`);
        return respondJSON(error as Error );
      }
    }
  );
  

}

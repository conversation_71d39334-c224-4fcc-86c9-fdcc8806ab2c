import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import fs from 'node:fs';


export async function numberRoutes(fastify: FastifyInstance) { 
  fastify.get('/vmail-file/:numberId', async (req: FastifyRequest<{ Params: { numberId: string}}>, reply: FastifyReply) => {
    const { numberId } = req.params;
    const { filePath, mime, stat } = await fastify.services.numberService.getVoicemailFile(numberId);

    reply
      .type(mime)
      .header('Accept-Ranges', 'bytes')
      .header('Cache-Control', 'private, max-age=0, no-store');

    reply.header('Content-Length', stat.size);
    return reply.send(fs.createReadStream(filePath));
  });

}


import { NotFoundError } from '@/middleware/errorHandler';
import { EpikBoxDocument, EpikBoxModel, SpeedTest, FetchLtePerf, } from '@/models/epikbox';
import { E911GeocodeData } from '@/services/emergencyService/BaseE911Service';
import { JSONResponse } from '@/types/response';
import logger from '@/utils/logger';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

interface EpikBoxParams {
  id: string;
}

interface CreateEpikBoxBody {
  serialNumber: string;
  ip: string;
  status?: string;
}

export async function epikboxRoutes(fastify: FastifyInstance) {
  const epikBoxModel = new EpikBoxModel(fastify);

  fastify.post(
    '/ping/:serialNumber',
    async (
      request: FastifyRequest<{
        Params: { serialNumber: string };
        Body: { ip?: string };
      }>,
      reply: FastifyReply
    ): Promise<void> => {
      try {
        const serial = request.params.serialNumber;
        let streamActive = true;
        let cancel: () => void;
        logger.info(`pingBox ${serial}`);
        reply.raw.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive',
        });
        reply.raw.write(`data: Connected\n\n`);
        const sendSSE = (data: string) => {
          try {
            reply.raw.write(`data: ${data}\n\n`);
          } catch (error) {
            console.log(error);
          }
        };
        const cleanup = () => {
          if (cancel) {
            logger.debug('Cancelling ping due to client disconnect');
            cancel();
          }
          streamActive = false;
        };
        // Handle client disconnect
        request.raw.on('close', cleanup);
        request.raw.on('error', cleanup);

        // Handle reply close
        reply.raw.on('close', cleanup);
        reply.raw.on('error', cleanup);
        const res = await fastify.services.edgeProxyService.pingBox(
          request.params.serialNumber,
          request?.body?.ip ?? '',
          (data: string) => {
            if (streamActive) {
              sendSSE(data);
            }
          },
          () => {
            if (streamActive) {
              sendSSE('completed');
              reply.raw.end();
              streamActive = false;
            }
          },
        );
        cancel = res.cancel;
      } catch (error) {
        logger.error(error, 'PingBox HTTP Stream error');
        if (!reply.raw.destroyed) {
          reply.raw.write('Error while pinging to epikbox');
          reply.raw.end();
        }
      }
    }
  );

  fastify.put(
    '/:id',
    async (
      request: FastifyRequest<{ Params: EpikBoxParams; Body: Partial<EpikBoxDocument> }>
    ): Promise<JSONResponse<EpikBoxDocument>> => {
      const epikbox = await epikBoxModel.update(request.params.id, request.body);
      if (!epikbox) {
        throw new NotFoundError('EpikBox');
      }
      return respondJSON(epikbox);
    }
  );
  fastify.get(
    '/:id/lteanalyzer',
    async (
      request: FastifyRequest<{ Params: { id: string } }>
    ): Promise<JSONResponse<EpikBoxDocument>> => {
      console.log('lteanalyzer request', request.params.id);
      const epikbox = await epikBoxModel.findById(request.params.id);
      if (!epikbox) {
        throw new NotFoundError('EpikBox');
      }
      const lteAnalyzer = await fastify.services.edgeProxyService.getLiveLteAnalyzer(epikbox.serialNumber);
      if (!lteAnalyzer) {
        throw new NotFoundError('LteAnalyzer');
      }

      // todo need to update endpoint which Sadan is using to save lteanalyzer result in database
      epikbox.lteAnalyzer = { inProgress: lteAnalyzer, lteTestInProgress: new Date() };
      const epikboxUpdated = await epikBoxModel.update(request.params.id, epikbox);
      if (epikboxUpdated) {
        epikboxUpdated.lteAnalyzer = { inProgress: false, };
        await epikBoxModel.update(request.params.id, epikboxUpdated);
      }
      return respondJSON(epikbox);
    }
  );
  fastify.get(
    '/:id/speedtest/:testType',
    async (
      request: FastifyRequest<{ Params: { id: string, testType: string } }>
    ): Promise<JSONResponse<SpeedTest>> => {
      console.log('speedtest request', request.params.id);
      const epikbox = await epikBoxModel.findById(request.params.id);
      if (!epikbox) {
        throw new NotFoundError('EpikBox');
      }
      const speedTest = await fastify.services.edgeProxyService.getLiveSpeedTest(epikbox.serialNumber, request.params.testType);
      if (!speedTest) {
        throw new NotFoundError('SpeedTest');
      }

      if (request.params.testType === 'data') {
        epikbox.speedTestData = speedTest;
      } else {
        epikbox.speedTestVoice = speedTest;
      }
      await epikBoxModel.update(request.params.id, epikbox);
      return respondJSON(speedTest);
    }
  );
  fastify.get(
    '/:id/initlteperfcheck',
    async (
      request: FastifyRequest<{ Params: { id: string } }>
    ): Promise<JSONResponse<FetchLtePerf & { initiated: string }>> => {
      console.log('initlteperfcheck request', request.params.id);
      let epikbox = await epikBoxModel.findById(request.params.id);
      if (!epikbox) {
        throw new NotFoundError('EpikBox');
      }
      const initLtePerf = await fastify.services.edgeProxyService.initLtePerf(epikbox.serialNumber);
      if (!initLtePerf) {
        throw new NotFoundError('InitLtePerf');
      }

      let preferredProviderTest = {}
      if (initLtePerf && String(initLtePerf).search("initiatied") !== -1) {
        console.log('initLtePerf initiated');
        preferredProviderTest = { initiatedTimeStamp: new Date() }
      }

      epikbox.preferredProviderTest = epikbox.preferredProviderTest ?
        { ...epikbox.preferredProviderTest, ...preferredProviderTest } : preferredProviderTest;
      epikbox = await epikBoxModel.update(request.params.id, epikbox, { returnDocument: 'after' });


      return respondJSON({
        initiated: String(initLtePerf),
        ...(epikbox && epikbox.preferredProviderTest ? { preferredProviderTest: epikbox.preferredProviderTest } : preferredProviderTest)
      });
    }
  );

  fastify.post(
    '/:id/geocode',
    async (
      request: FastifyRequest<{ Params: { id: string }, Body: E911GeocodeData }>
    ): Promise<JSONResponse<any>> => {
      console.log('geocode request', request.params.id, request.body);
      const geocode = await fastify.services.e911ServiceFactory.checkGeocode(request.body);
      console.log('geocode', geocode);
      return respondJSON(geocode);
    }
  );

}

import { config } from '@/config/environment';
import { ForbiddenError, UnauthorizedError } from '@/middleware/errorHandler';
import { Features, ListAccessResponse } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { GrpcServiceClient, safeGrpcOperation } from '@/utils/grpcClient';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';
import { auth } from '@/types/generated';

export class DynamicPermissionClient extends GrpcServiceClient {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || config.authGrpcUri,
      maxRetries: 3,
      service: auth.PermissionServiceClient,
    });
  }
  async ping() {
    const request=new auth.EchoRequest( { message: 'keepalive' });
    super.ping(request);
  }
  async validateUserPermission(
    userId: string,
    featureKey: string,
    operation: number
  ): Promise<boolean> {
    const request = new auth.ValidateUserPermissionRequest({
      user_id: userId,
      feature_key: featureKey,
      operation: operation,
    });
    const response = await this.makeRequest<auth.ValidatePermissionResponse>(
      'ValidateUserPermission',
      request
    );
    return response.has_permission;
  }

  async validateCompanyAccess(userId: string, companyId: string): Promise<boolean> {
    const request = new auth.ValidateCompanyAccessRequest({
      user_id: userId,
      company_id: companyId,
    });

    const response = await this.makeRequest<auth.ValidateAccessResponse>(
      'ValidateCompanyAccess',
      request
    );
    return response.has_access;
  }

  async validateEnterpriseAccess(userId: string, enterpriseId: string): Promise<boolean> {
    const request = new auth.ValidateEnterpriseAccessRequest({
      user_id: userId,
      enterprise_id: enterpriseId,
    });

    const response = await this.makeRequest<auth.ValidateAccessResponse>(
      'validateEnterpriseAccess',
      request
    );
    return response.has_access;
  }

  async listAllowedCompanies(userId: string): Promise<auth.ListCompanyOrEnterpriseResponse> {
    const request = new auth.UserIdInput({ user_id: userId });
    return this.makeRequest<auth.ListCompanyOrEnterpriseResponse>(
      'ListUserAccessableCompaniesWithEnterprises',
      request
    );
  }

  async listAllowedEnterprises(userId: string): Promise<auth.ListCompanyOrEnterpriseResponse> {
    const request = new auth.UserIdInput({ user_id: userId });
    return this.makeRequest<auth.ListCompanyOrEnterpriseResponse>(
      'ListUserAccessableEnterprises',
      request
    );
  }
}

const permissionClient = new DynamicPermissionClient();
const permLogger = createModuleLogger('permissionClient');

export const permissionPlugin = fp(async function (fastify: FastifyInstance) {
  try {
    await permissionClient.connect();
    await permissionClient.ping();
  } catch (error) {
    fastify.log.warn('Permission service not available, will retry on first request');
  }

  fastify.decorate(
    'requirePermission',
    async (request: FastifyRequest, featureKey: string, operation: number): Promise<boolean> => {
      const res = await safeGrpcOperation(
        async () => {
          return await permissionClient.validateUserPermission(
            request.user.id,
            featureKey,
            operation
          );
        },
        'validate permission',
        permLogger
      );
      return res;
    }
  );

  fastify.decorate('requirePermissionHook', (featureKey: Features, operation: number) => {
    return async (request: FastifyRequest, _: FastifyReply) => {
      const res = await fastify.requirePermission(request, featureKey, operation);
      if (!res) {
        throw new ForbiddenError(`Missing required permission: ${featureKey}`);
      }
    };
  });

  fastify.decorate(
    'requireCompanyAccess',
    async (request: FastifyRequest, companyIdParam: string = 'companyId'): Promise<Boolean> => {
      const companyId = companyIdParam;

      if (!companyId) {
        return false;
      }

      return await safeGrpcOperation(
        async () => {
          const hasAccess = await permissionClient.validateCompanyAccess(
            request.user.id,
            companyId
          );
          return hasAccess;
        },
        'validate company access',
        permLogger
      );
    }
  );

  fastify.decorate(
    'requireEnterpriseAccess',
    async (
      request: FastifyRequest,
      enterpriseIdParam: string = 'enterpriseId'
    ): Promise<Boolean> => {
      const enterpriseId = enterpriseIdParam;

      if (!enterpriseId) {
        return false;
      }

      return await safeGrpcOperation(
        async () => {
          const hasAccess = await permissionClient.validateEnterpriseAccess(
            request.user.id,
            enterpriseId
          );
          return hasAccess;
        },
        'validate enterprise access',
        permLogger
      );
    }
  );
  fastify.decorate(
    'listAllowedCompanies',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeGrpcOperation(
        async () => {
          const response = await permissionClient.listAllowedCompanies(request.user.id);
          return {
            ids: response.ids || [],
            isAll: response.is_all ?? false,
          };
        },
        'list all companies',
        permLogger
      );
    }
  );
  fastify.decorate(
    'listAllowedEnterprises',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeGrpcOperation(
        async () => {
          const response = await permissionClient.listAllowedEnterprises(request.user.id);
          return {
            ids: response.ids || [],
            isAll: response.is_all ?? false,
          };
        },
        'list all enterprises',
        permLogger
      );
    }
  );
});

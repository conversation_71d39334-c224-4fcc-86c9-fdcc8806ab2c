import { config } from '@/config/environment';
import { UnauthorizedError } from '@/middleware/errorHandler';
import cookie from '@fastify/cookie';
import jwt from '@fastify/jwt';
import { FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';
import multipart from '@fastify/multipart';

export const jwtPlugin = fp(async function (fastify) {
  await fastify.register(jwt, {
    secret: config.secret,
    cookie: {
      cookieName: 'v2-jwt',
      signed: false,
    },
    verify: { onlyCookie: true },
  });
});

export const authMiddleware = fp(async function (fastify) {
  await fastify.register(cookie);
  await fastify.register(jwtPlugin);
  await fastify.register(multipart, { attachFieldsToBody: true, 
    limits: {
      fileSize: 50 * 1024 * 1024,
    },
   });

  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // Skip authentication for GraphQL requests. to see middleware for graphql. look at graphql/graphql.ts
      if (request.url === '/apps/epikv2-api/query') {
        return;
      }
      // console.log("called1")
      const publicRoutes = ['/health', '/docs'];
      if (publicRoutes.some(route => request.url.startsWith(route))) {
        return;
      }

      const payload = (await request.jwtVerify()) as any;
      request.user = payload;
    } catch (error) {
      const err = error as any;

      if (err.name === 'TokenExpiredError') {
        throw new UnauthorizedError('Token has expired');
      }

      if (err.name === 'JsonWebTokenError') {
        throw new UnauthorizedError('Invalid token');
      }

      throw new UnauthorizedError('Authentication failed');
    }
  });
});

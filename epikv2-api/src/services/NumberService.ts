import { NotFoundError } from '@/middleware/errorHandler';
import { AlarmRelayDocument, getAllModels, ModelInstanceMap, NumberDocument, NumberDocumentPopulated } from '@/models';
import { ListNumberInput, ListNumbersPaginatedResultType, PaginationInput } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import path from 'node:path';
const logger = createModuleLogger('NumberService');

function escapeRegex(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export class NumberService {
  private models: ModelInstanceMap | null = null;

  constructor(models?: ModelInstanceMap) {
    if (models) {
      this.models = models;
    }
  }

  private getModels(): ModelInstanceMap {
    if (!this.models) {
      this.models = getAllModels();
    }
    return this.models;
  }
  async listNumbers(
    filter: ListNumberInput,
    pagination: PaginationInput
  ): Promise<ListNumbersPaginatedResultType> {
    logger.info('listNumbers');
    const match: any = {
      deleted: false,
      linkedBox: { $ne: null },
    };
    const applyRegex = (value?: string) =>
      value?.trim() ? { $regex: value.trim(), $options: 'i' } : undefined;
    if (filter.query?.trim()) {
      const safeQuery = escapeRegex(filter.query.trim());
      match.number = applyRegex(safeQuery);
    }

    if (!filter?.isAll) {
      match['company'] = { $in: filter?.ids || [] };
    }
    logger.debug({ match, pagination }, 'listNumbers');
    const docs = await this.getModels().numbers.findWithPagination(match, pagination);

    return docs;
  }
  async findNumberById(id: string): Promise<NumberDocument | null> {
    return await this.getModels().numbers.findById(id);
  }

  async findAlarmRelayById(id: string): Promise<AlarmRelayDocument | null> {
    return await this.getModels().alarmrelays.findById(id);
  }

  async findByNumber(number: string): Promise<NumberDocumentPopulated | null> {
    return await this.getModels().numbers.findOne({ number });
  }

  async updateNumber (id: string, data: NumberDocumentPopulated): Promise<NumberDocumentPopulated | null>{
      logger.info('updateNumber');
      logger.debug({ id, data }, 'updateNumber');
      let update: Partial<NumberDocumentPopulated> = {};
      if (data.assignedTo) {
        update['assignedTo'] = data.assignedTo;
      }
      if (data.linkedBox) {
        update['linkedBox'] = data.linkedBox;
      }
      if (data.forwardNumbers) {
        update['forwardNumbers'] = data.forwardNumbers;
      }
      if (data.allowedUsers) {
        update['allowedUsers'] = data.allowedUsers;
      }
      if (data.allowedNumbers) {
        update['allowedNumbers'] = data.allowedNumbers;
      }
      if (data.type) {
        update['type'] = data.type;
      }
      
      return await this.getModels().numbers.update(id, update);
    };

    async getVoicemailFile(numberId: string,) {
        const number = await this.getModels().numbers.findById(numberId);;
        if (!number) {
          throw new Error('Unauthorized or number does not exist');
        }
        const formattedNumber = parsePhoneNumberFromString(number.number, 'US');

         if (!formattedNumber) {
          throw new Error('Number does not exist');
        }

        const filePath = `/usr/local/freeswitch/storage/voicemail/default/${process.env.HOST_PUBLIC_IP}/${formattedNumber.nationalNumber}/greeting_1.wav`;

        await fsp.access(filePath, fs.constants.R_OK).catch(() => {
          throw new NotFoundError('No file found.');
        });
    
        const stat = await fsp.stat(filePath);
        const ext = path.extname(filePath).toLowerCase();
        const mime = ext === '.mp3' ? 'audio/mpeg' : 'audio/wav';
    
        return { filePath, mime, stat };
      }
}

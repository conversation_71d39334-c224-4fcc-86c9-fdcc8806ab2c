import { config } from '@/config/environment';
import { getAllModels, ModelInstanceMap } from '@/models';
import { apr } from '@/types/generated';
import { GrpcServiceClient } from '@/utils/grpcClient';
import { createModuleLogger } from '@/utils/logger';
const logger = createModuleLogger('AprService');

export class AprService extends GrpcServiceClient<apr.v1.APRServiceClient> {
  private models: ModelInstanceMap | null = null;

  constructor(serverAddress?: string, models?: ModelInstanceMap) {
    super({
      serverAddress: serverAddress || config.edgeProxyUri,
      service: apr.v1.APRServiceClient,
      maxRetries: 3,
    });
    this.models = models ?? getAllModels();
    this.connect();
  }
}

import { config } from '@/config/environment';
import {
  getAllModels,
  ModelInstanceMap,
  TimerSessionState,
  TimerSessionType,
  NumberDocument,
  EpikBoxDocument,
  AttachmentStatus
} from '@/models';
import { apr } from '@/types/generated';
import { GrpcServiceClient } from '@/utils/grpcClient';
import { createModuleLogger } from '@/utils/logger';
import { ObjectId } from 'mongodb';
import { FastifyInstance } from 'fastify';
import { ForbiddenError, NotFoundError, ValidationError } from '@/middleware/errorHandler';

const logger = createModuleLogger('AprService');

// Constants
const VALID_NUMBER_TYPES = ['alarm', 'fireAlarm'];
const TIMER_DURATION_MINUTES = 10;

// Types
export interface APRToggleRequest {
  numberID: string;
  isEnabled: boolean;
  obiID?: string;
  portNumber: string;
}

export interface APRTimerRequest {
  numberID: string;
  portocolSelection: string;
  portNumber: string;
  companionPort?: string;
}

export interface APRUpdateRequest {
  numberID: string;
  operationMode: string;
  portocolSelection?: string;
}

export interface APRTestResultsResponse {
  fetchResponse: any;
  updatedNumber?: NumberDocument;
}

export interface APRTimerResponse {
  message: string;
  remainingTime: number | null;
}

export class AprService extends GrpcServiceClient<apr.v1.APRServiceClient> {
  private models: ModelInstanceMap | null = null;

  constructor(serverAddress?: string, models?: ModelInstanceMap) {
    super({
      serverAddress: serverAddress || config.edgeProxyUri,
      service: apr.v1.APRServiceClient,
      maxRetries: 3,
    });
    this.models = models ?? getAllModels();
    this.connect();
  }

  private getModels(): ModelInstanceMap {
    if (!this.models) {
      this.models = getAllModels();
    }
    return this.models;
  }

  private getRemainingTime(timerCreatedAt: Date): number {
    const timeNow = new Date();
    return Math.ceil(
      TIMER_DURATION_MINUTES * 60 - (timeNow.getTime() - timerCreatedAt.getTime()) / 1000
    );
  }

  /**
   * Validates user authorization for a specific number
   */
  async validateUserAuthorization(fastify: FastifyInstance, userId: string, numberID: string): Promise<boolean> {
    try {
      const models = this.getModels();
      const numberModel = models.numbers;

      // Get the number document
      const number = await numberModel.findById(numberID);
      if (!number) {
        throw new NotFoundError('Number not found');
      }

      // Use the permission system to validate company access
      const hasAccess = await fastify.requireCompanyAccess(
        { user: { id: userId } } as any,
        number.company?.toString() || ''
      );

      return hasAccess as boolean;
    } catch (error) {
      logger.error({ error, userId, numberID }, 'Failed to validate user authorization');
      return false;
    }
  }

  /**
   * Validates if a number is valid for APR operations
   */
  async validateNumber(numberID: string): Promise<NumberDocument> {
    const models = this.getModels();
    const numberModel = models.numbers;

    const number = await numberModel.findById(numberID);
    if (!number) {
      throw new NotFoundError('Number not found');
    }

    if (!VALID_NUMBER_TYPES.includes(number.type)) {
      throw new ValidationError('Save line type as Fire or Alarm to enable APR');
    }

    return number;
  }

  /**
   * Gets EpikBox by serial number
   */
  async getEpikBoxBySerial(serialNumber: string): Promise<EpikBoxDocument> {
    const models = this.getModels();
    const epikBoxModel = models.epikboxes;

    const box = await epikBoxModel.findOne({ serialNumber, deleted: { $ne: true } });
    if (!box) {
      throw new NotFoundError('EpikBox not found');
    }

    return box;
  }

  /**
   * Makes gRPC request to APR service
   */
  private async makeAPRRequest(method: string, params: any): Promise<any> {
    try {
      return await this.makeRequest(method as any, params);
    } catch (error) {
      logger.error({ error, method, params }, 'APR gRPC request failed');
      throw new Error(`APR operation failed: ${(error as Error).message}`);
    }
  }

  /**
   * Toggle APR on/off for a specific port
   */
  async toggleAlarmRelay(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRToggleRequest
  ): Promise<NumberDocument> {
    logger.info('Toggle Alarm Relay', { userId, boxSerial, request });

    const { numberID, isEnabled, portNumber } = request;

    // Validate user authorization
    const isAuthorized = await this.validateUserAuthorization(fastify, userId, numberID);
    if (!isAuthorized) {
      throw new ForbiddenError('User does not have privilege to perform this operation');
    }

    // Check for existing timer
    const models = this.getModels();
    const boxTimerModel = models.boxtimers;
    const box = await this.getEpikBoxBySerial(boxSerial);

    const existingTimer = await boxTimerModel.findOne({ box: box._id });
    if (existingTimer) {
      throw new ValidationError('Please wait for the Timer');
    }

    // Validate number
    const number = await this.validateNumber(numberID);

    // Check if alarm relay exists
    const alarmRelayModel = models.alarmrelays;
    let alarmRelay = null;
    if (number.alarmRelayInfo) {
      alarmRelay = await alarmRelayModel.findById(number.alarmRelayInfo.toString());
    }

    // If enabling and alarm relay exists, check current status
    if (alarmRelay && isEnabled) {
      const testResults = await this.makeAPRRequest('APRTestResults', {
        serial_number: boxSerial,
        port: portNumber
      });

      if (alarmRelay.isEnabled && testResults.result === false) {
        // Disable APR if test failed
        await this.makeAPRRequest('ToggleAPR', {
          serial_number: boxSerial,
          port: portNumber,
          enable: false
        });
      }
    }

    // Toggle APR via gRPC
    const toggleResponse = await this.makeAPRRequest('ToggleAPR', {
      serial_number: boxSerial,
      port: portNumber,
      enable: isEnabled
    });

    // Update or create alarm relay document
    const aprData = {
      isEnabled: toggleResponse.status,
      operationMode: !isEnabled || !alarmRelay ? 'Test' : alarmRelay.operationMode,
      isTestPassed: !isEnabled || !alarmRelay ? false : alarmRelay.isTestPassed,
      isTestStarted: !isEnabled || !alarmRelay ? false : alarmRelay.isTestStarted
    };

    if (alarmRelay) {
      await alarmRelayModel.update(alarmRelay._id!.toString(), aprData);
    } else {
      const newAlarmRelay = await alarmRelayModel.create(aprData);
      await models.numbers.update(number._id!.toString(), {
        alarmRelayInfo: newAlarmRelay._id
      });
    }

    // Return updated number
    return await models.numbers.findById(numberID) as NumberDocument;
  }

  /**
   * Create alarm relay timer and initiate test
   */
  async createAlarmRelayTimer(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRTimerRequest
  ): Promise<NumberDocument> {
    logger.info('Creating alarm relay timer', { userId, boxSerial, request });

    const { numberID, portocolSelection, portNumber, companionPort } = request;

    // Validate user authorization
    const isAuthorized = await this.validateUserAuthorization(fastify, userId, numberID);
    if (!isAuthorized) {
      throw new ForbiddenError('User does not have privilege to perform this operation');
    }

    // Check for existing timer
    const models = this.getModels();
    const boxTimerModel = models.boxtimers;
    const box = await this.getEpikBoxBySerial(boxSerial);

    const existingTimer = await boxTimerModel.findOne({ box: box._id });
    if (existingTimer) {
      throw new ValidationError('Timer Already Exists, Please wait');
    }

    // Validate number
    const number = await this.validateNumber(numberID);

    // Enable APR first
    await this.makeAPRRequest('ToggleAPR', {
      serial_number: boxSerial,
      port: portNumber,
      enable: true
    });

    // Handle companion port if specified
    if (companionPort) {
      // Find companion port number and disable it
      const companionPortNum = companionPort.split('').pop();
      const companionNumber = await models.numbers.findOne({
        linkedBox: box._id,
        port: `port ${companionPortNum}`
      });

      if (companionNumber) {
        // Note: In the original code, this would disable the OBI port
        // For now, we'll log this action
        logger.info('Companion port handling', { companionPort, companionPortNum });
      }
    }

    // Initiate APR test
    await this.makeAPRRequest('InitiateAprTest', {
      serial_number: boxSerial,
      port: portNumber,
      companion_port: companionPort || ''
    });

    // Create note
    const notesModel = models.notes;
    await notesModel.createNote({
      user: new ObjectId(userId),
      epikBox: box._id,
      note: 'Initiate Alarm Relay Test',
      hasAttachment: AttachmentStatus.NO
    });

    // Create timer session
    const boxTimerSessionModel = models.boxtimersessions;
    const newSession = await boxTimerSessionModel.create({
      box: box._id,
      user: new ObjectId(userId),
      state: TimerSessionState.PENDING,
      type: TimerSessionType.ALARM,
      createdAt: new Date()
    });

    // Update alarm relay
    const alarmRelayModel = models.alarmrelays;
    await alarmRelayModel.update(number.alarmRelayInfo!.toString(), {
      protoclSelection: portocolSelection,
      operationMode: 'Test',
      isTestPassed: false,
      isTestStarted: true,
      isEnabled: true,
      timerSession: newSession._id,
      companionPort
    });

    // Create timer
    await boxTimerModel.createWithTTL({
      box: box._id,
      user: new ObjectId(userId),
      session: newSession._id
    });

    // Return updated number
    return await models.numbers.findById(numberID) as NumberDocument;
  }

  /**
   * Get alarm relay timer status
   */
  async getAlarmRelayTimer(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ): Promise<APRTimerResponse> {
    logger.info('Get alarm relay timer', { userId, boxSerial });

    const models = this.getModels();
    const box = await this.getEpikBoxBySerial(boxSerial);
    const boxTimerModel = models.boxtimers;
    const boxTimerSessionModel = models.boxtimersessions;

    const existingTimer = await boxTimerModel.findOne({ box: box._id });

    if (!existingTimer) {
      // Check for pending session and finish it
      const latestTimerSession = await boxTimerSessionModel.findLatestPendingSession(box._id!);

      if (latestTimerSession) {
        await boxTimerSessionModel.finishSession(latestTimerSession._id!);
      }

      return {
        message: 'No timer on Box',
        remainingTime: null
      };
    }

    return {
      message: 'Timer exists',
      remainingTime: this.getRemainingTime(existingTimer.createdAt!)
    };
  }

  /**
   * Update alarm relay session (finish timer)
   */
  async updateAlarmRelaySession(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ): Promise<{ success: boolean }> {
    logger.info('Update Alarm Timer', { userId, boxSerial });

    const models = this.getModels();
    const box = await this.getEpikBoxBySerial(boxSerial);
    const boxTimerModel = models.boxtimers;
    const boxTimerSessionModel = models.boxtimersessions;

    // Delete timer
    const timerToDelete = await boxTimerModel.findOne({ box: box._id });
    if (timerToDelete) {
      await boxTimerModel.delete(timerToDelete._id!.toString());
    }

    // Find and finish latest pending session
    const latestTimerSession = await boxTimerSessionModel.findLatestPendingSession(box._id!);

    if (latestTimerSession) {
      await boxTimerSessionModel.finishSession(latestTimerSession._id!);
      return { success: true };
    }

    return { success: false };
  }

  /**
   * Update alarm relay settings
   */
  async updateAlarmRelay(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    request: APRUpdateRequest
  ): Promise<NumberDocument> {
    logger.info('Update Alarm', { userId, boxSerial, request });

    const { numberID, operationMode, portocolSelection } = request;

    // Validate user authorization
    const isAuthorized = await this.validateUserAuthorization(fastify, userId, numberID);
    if (!isAuthorized) {
      throw new ForbiddenError('User does not have privilege to perform this operation');
    }

    // Validate number
    const number = await this.validateNumber(numberID);

    if (!number.alarmRelayInfo) {
      throw new ValidationError('Alarm Relay Disabled');
    }

    const models = this.getModels();
    const alarmRelayModel = models.alarmrelays;
    const alarmRelay = await alarmRelayModel.findById(number.alarmRelayInfo.toString());

    if (!alarmRelay?.isTestPassed) {
      throw new ValidationError(
        'Alarm Protocol Relay Cannot be set to live Status until a test is completed'
      );
    }

    // Clean up timer and session
    const box = await this.getEpikBoxBySerial(boxSerial);
    const boxTimerModel = models.boxtimers;
    const boxTimerSessionModel = models.boxtimersessions;

    const timerToDelete2 = await boxTimerModel.findOne({ box: box._id });
    if (timerToDelete2) {
      await boxTimerModel.delete(timerToDelete2._id!.toString());
    }

    if (alarmRelay.timerSession) {
      await boxTimerSessionModel.finishSession(alarmRelay.timerSession);
    }

    // Update alarm relay
    await alarmRelayModel.update(alarmRelay._id!.toString(), {
      operationMode,
      protoclSelection: portocolSelection || alarmRelay.protoclSelection,
      isTestPassed: true
    });

    // Return updated number
    return await models.numbers.findById(numberID) as NumberDocument;
  }

  /**
   * Fetch alarm relay test results
   */
  async fetchAlarmRelayResults(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    numberID: string,
    portNumber: string
  ): Promise<APRTestResultsResponse> {
    logger.info('Fetch Alarm relay test results', { userId, boxSerial, numberID, portNumber });

    // Validate user authorization
    const isAuthorized = await this.validateUserAuthorization(fastify, userId, numberID);
    if (!isAuthorized) {
      throw new ForbiddenError('User does not have privilege to perform this operation');
    }

    // Get test results
    const testResponse = await this.makeAPRRequest('APRTestResults', {
      serial_number: boxSerial,
      port: portNumber
    });

    const models = this.getModels();
    const number = await models.numbers.findById(numberID);
    if (!number?.alarmRelayInfo) {
      return { fetchResponse: testResponse };
    }

    const alarmRelayModel = models.alarmrelays;
    const alarmRelay = await alarmRelayModel.findById(number.alarmRelayInfo.toString());

    const isPassed = testResponse && testResponse.result === true;
    const isCompleted = ['Passed', 'Failed'].includes(testResponse?.response || '');

    if (isCompleted) {
      // Handle companion port re-enabling
      if (alarmRelay?.companionPort) {
        const companionPortNum = alarmRelay.companionPort.split('').pop();
        const box = await this.getEpikBoxBySerial(boxSerial);
        const companionNumber = await models.numbers.findOne({
          linkedBox: box._id,
          port: `port ${companionPortNum}`
        });

        if (companionNumber) {
          // Note: In original code, this would re-enable the OBI port
          logger.info('Re-enabling companion port', { companionPort: alarmRelay.companionPort });
        }
      }

      // Clean up timer and session
      const box = await this.getEpikBoxBySerial(boxSerial);
      const boxTimerModel = models.boxtimers;
      const boxTimerSessionModel = models.boxtimersessions;

      const timerToDelete = await boxTimerModel.findOne({ box: box._id });
      if (timerToDelete) {
        await boxTimerModel.delete(timerToDelete._id!.toString());
      }

      if (alarmRelay?.timerSession) {
        await boxTimerSessionModel.finishSession(alarmRelay.timerSession);
      }
    }

    if (!isPassed) {
      return { fetchResponse: testResponse };
    }

    // Update alarm relay as test passed
    await alarmRelayModel.update(number.alarmRelayInfo.toString(), {
      isTestPassed: true
    });

    const updatedNumber = await models.numbers.findById(numberID) as NumberDocument;

    return {
      fetchResponse: testResponse,
      updatedNumber
    };
  }

  /**
   * Get alarm relay status
   */
  async getAlarmRelayStatus(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    portNumber: string
  ): Promise<{ status: boolean; data?: any }> {
    try {
      logger.info('GET Alarm relay Status', { userId, boxSerial, portNumber });

      let statusData = { status: false };
      let testResults = { result: null };

      // Get APR status
      try {
        const statusResponse = await this.makeAPRRequest('APRStatus', {
          serial_number: boxSerial,
          port: portNumber
        });
        statusData = statusResponse || statusData;
      } catch (error) {
        logger.error('Error fetching alarm relay status', { error });
      }

      // Get test results
      try {
        const testResponse = await this.makeAPRRequest('APRTestResults', {
          serial_number: boxSerial,
          port: portNumber
        });
        testResults = testResponse || testResults;
      } catch (error) {
        logger.error('Error fetching test results', { error });
      }

      // Find associated number and update if needed
      const models = this.getModels();
      const box = await this.getEpikBoxBySerial(boxSerial);
      const number = await models.numbers.findOne({
        linkedBox: box._id,
        port: `port ${portNumber}`
      });

      if (number?.alarmRelayInfo) {
        const alarmRelayModel = models.alarmrelays;

        if (testResults.result === false || !statusData.status) {
          // Update alarm relay to failed state
          await alarmRelayModel.update(number.alarmRelayInfo.toString(), {
            isTestPassed: false,
            isEnabled: false,
            operationMode: 'Test',
            isTestStarted: false
          });

          // Disable APR
          try {
            await this.makeAPRRequest('ToggleAPR', {
              serial_number: boxSerial,
              port: portNumber,
              enable: false
            });
          } catch (error) {
            logger.error('Failed to disable APR', { error });
          }

          return { status: false, data: statusData };
        }
      }

      return { status: statusData.status, data: statusData };
    } catch (error) {
      logger.error('Unexpected error in getAlarmRelayStatus', { error });
      return { status: false };
    }
  }

  /**
   * Enable APR on port unconditionally (for admin operations)
   */
  async enableAprOnPortUnconditionally(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    portNumber: string
  ): Promise<NumberDocument> {
    logger.info('enableAprOnPortUnconditionally', { userId, boxSerial, portNumber });

    const models = this.getModels();
    const box = await this.getEpikBoxBySerial(boxSerial);
    const actualPort = `port ${portNumber}`;

    const number = await models.numbers.findOne({
      linkedBox: box._id,
      port: actualPort
    });

    if (!number) {
      throw new NotFoundError('Number not found for the specified port');
    }

    if (!VALID_NUMBER_TYPES.includes(number.type)) {
      throw new ValidationError('Number is not alarm or fireAlarm type');
    }

    // Enable APR via gRPC
    const toggleResponse = await this.makeAPRRequest('ToggleAPR', {
      serial_number: boxSerial,
      port: portNumber,
      enable: true
    });

    if (!toggleResponse.status) {
      throw new Error('Failed to toggle APR on box');
    }

    // Update or create alarm relay
    const alarmRelayModel = models.alarmrelays;
    const aprData = {
      isEnabled: true,
      operationMode: 'Live',
      isTestPassed: true,
      isTestStarted: true
    };

    if (number.alarmRelayInfo) {
      await alarmRelayModel.update(number.alarmRelayInfo.toString(), aprData);
    } else {
      const newAlarmRelay = await alarmRelayModel.create(aprData);
      await models.numbers.update(number._id!.toString(), {
        alarmRelayInfo: newAlarmRelay._id
      });
    }

    return await models.numbers.findById(number._id!.toString()) as NumberDocument;
  }

  /**
   * Remove alarm protocol settings
   */
  async removeAlarmProtocolSettings(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string,
    payload: { _id: string; alarmRelayInfo: string }
  ): Promise<void> {
    logger.info('removeAlarmProtocolSettings', { userId, boxSerial, payload });

    const { _id, alarmRelayInfo } = payload;

    // Validate user authorization
    const isAuthorized = await this.validateUserAuthorization(fastify, userId, _id);
    if (!isAuthorized) {
      throw new ForbiddenError('User not permitted to perform this operation');
    }

    const models = this.getModels();
    const number = await models.numbers.findById(_id);
    if (!number) {
      throw new NotFoundError('Number not exist in database');
    }

    // Remove alarm relay reference from number
    await models.numbers.update(_id, { alarmRelayInfo: null });

    // Delete alarm relay document
    await models.alarmrelays.delete(alarmRelayInfo);

    logger.info('Alarm protocol settings removed successfully', { numberId: _id });
  }

  /**
   * Check and disable APR on timer expired (cleanup function)
   */
  async checkAndDisableAprOnTimerExpired(
    fastify: FastifyInstance,
    userId: string,
    boxSerial: string
  ): Promise<void> {
    logger.info('checkAndDisableAprOnTimerExpired', { userId, boxSerial });

    const models = this.getModels();
    const box = await this.getEpikBoxBySerial(boxSerial);

    // Check if timer still exists
    const aprTimer = await models.boxtimers.findOne({ box: box._id });
    if (aprTimer) {
      logger.info('Timer still active, skipping cleanup');
      return;
    }

    // Find numbers with APR in pending state
    const pendingNumbers = await this.getNumbersWithAprPending(box._id!);
    const enabledNumbers = await this.getNumbersWithAprEnabled(box._id!);

    // Disable pending APR numbers
    if (pendingNumbers.length > 0) {
      await Promise.all(
        pendingNumbers.map(async (number: any) => {
          const portNumber = number.port.split('').pop();
          await this.toggleAlarmRelay(fastify, userId, boxSerial, {
            numberID: number._id.toString(),
            isEnabled: false,
            portNumber
          });
        })
      );
    }

    // Check enabled numbers and update if needed
    if (enabledNumbers.length > 0) {
      await Promise.all(
        enabledNumbers.map(async (number: any) => {
          const portNumber = number.port.split('').pop();
          logger.info('APR is enabled in database on port', { portNumber });

          const status = await this.getAlarmRelayStatus(fastify, userId, boxSerial, portNumber);
          if (!status.status) {
            const aprData = {
              isEnabled: false,
              operationMode: 'Test',
              isTestPassed: false,
              isTestStarted: false
            };
            await models.alarmrelays.update(number.apr._id.toString(), aprData);
          }
        })
      );
    }
  }

  /**
   * Helper method to find numbers with APR enabled
   */
  private async getNumbersWithAprEnabled(boxId: ObjectId): Promise<any[]> {
    const models = this.getModels();
    const collection = models.numbers.getCollection();

    const pipeline = [
      {
        $match: {
          type: { $in: ['fireAlarm', 'alarm'] },
          linkedBox: boxId
        }
      },
      {
        $lookup: {
          from: 'alarmrelays',
          let: { id: '$alarmRelayInfo' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$_id', '$$id'] },
                    { $eq: ['$isEnabled', true] },
                    { $eq: ['$operationMode', 'Live'] },
                    { $eq: ['$isTestPassed', true] },
                    { $eq: ['$isTestStarted', true] }
                  ]
                }
              }
            }
          ],
          as: 'apr'
        }
      },
      { $unwind: '$apr' }
    ];

    return await collection.aggregate(pipeline).toArray();
  }

  /**
   * Helper method to find numbers with APR pending
   */
  private async getNumbersWithAprPending(boxId: ObjectId): Promise<any[]> {
    const models = this.getModels();
    const collection = models.numbers.getCollection();

    const pipeline = [
      {
        $match: {
          type: { $in: ['fireAlarm', 'alarm'] },
          linkedBox: boxId
        }
      },
      {
        $lookup: {
          from: 'alarmrelays',
          let: { id: '$alarmRelayInfo' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$_id', '$$id'] },
                    { $eq: ['$isEnabled', true] },
                    { $eq: ['$operationMode', 'Test'] },
                    { $eq: ['$isTestPassed', false] },
                    { $eq: ['$isTestStarted', true] }
                  ]
                }
              }
            }
          ],
          as: 'apr'
        }
      },
      { $unwind: '$apr' }
    ];

    return await collection.aggregate(pipeline).toArray();
  }
}

import { FastifyInstance } from 'fastify';
import { EpikBoxService } from './EpikBoxService';
import { EpiService } from './EpiService';
import fp from 'fastify-plugin';
import { EdgeProxyService } from './EdgeProxy';
import { NumberService } from './NumberService';
import { E911ServiceFactory } from './emergencyService/EmergencyServiceFactory';
import { VoicemailService } from './VoiceMailService';

export type Services = {
  epikBoxService: EpikBoxService;
  epiService: EpiService;
  edgeProxyService: EdgeProxyService;
  numberService: NumberService;
  e911ServiceFactory: E911ServiceFactory;
  voiceMailService: VoicemailService;
};

export const createServices = (fastify: FastifyInstance) => {
  const epikBoxService = new EpikBoxService();
  const epiService = new EpiService();
  const edgeProxyService = new EdgeProxyService();
  const numberService = new NumberService();
  const e911ServiceFactory = new E911ServiceFactory();
  const voiceMailService = new VoicemailService();

  const services: Services = {
    epikBoxService,
    epiService,
    edgeProxyService,
    numberService,
    e911ServiceFactory,
    voiceMailService,
  };
  fastify.decorate('services', services);
};

export const servicesPlugin = fp(async function (fastify: FastifyInstance) {
  createServices(fastify);
});

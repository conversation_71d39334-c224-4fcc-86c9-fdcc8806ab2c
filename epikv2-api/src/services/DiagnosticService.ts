import { getAllModels, ModelInstanceMap, ModelName } from '@/models';
import {
  DiagnosticFixCommand,
  DiagnosticSuiteExecutionDocument,
  DiagnosticTestCommand,
  DiagnosticTestDocument,
  DiagnosticTestExecutionDocument,
  DiagnosticTestStatus,
  DiagnosticTestValidation,
} from '@/models/diagnosticTest';
import { EpikBoxDocument, EpikBoxVersion } from '@/models/epikbox';
import { createModuleLogger } from '@/utils/logger';
import { ObjectId } from 'mongodb';
import { EdgeProxyService } from './EdgeProxy';

const logger = createModuleLogger('DiagnosticService');

export class DiagnosticService {
  models: ModelInstanceMap;
  private edgeProxyService: EdgeProxyService;

  constructor(models?: ModelInstanceMap) {
    this.models = models ?? getAllModels();
    this.edgeProxyService = new EdgeProxyService();
  }
  async executeDiagnostic(
    serialNumber: string,
    userId: string,
    updateCallback: (event: string, data: any) => void
  ): Promise<DiagnosticSuiteExecutionDocument | null> {
    const diagnosticSuiteExecutionModel = this.models[ModelName.DiagnosticSuiteExecution];
    const tests = await this.getTests({ enabled: true });
    const execution = await diagnosticSuiteExecutionModel.create({
      serialNumber,
      totalTests: tests.length,
      userId: new ObjectId(userId),
      creationDate: new Date(),
      deleted: false,
    });
    for (const test of tests) {
      this.executeTest(
        { suiteExecutionId: execution._id!.toString(), testId: test._id!.toString() },
        userId
      );
    }
    return diagnosticSuiteExecutionModel.findById(execution._id!.toString());
  }
  async executeTest(
    input: { suiteExecutionId: string; testId: string },
    userId: string
  ): Promise<DiagnosticTestExecutionDocument | null> {
    logger.info({ input, userId }, 'Executing diagnostic test');
    try {
      const epikBoxModel = this.models.epikboxes;
      const diagnosticTestExecutionModel = this.models[ModelName.DiagnosticTestExecution];
      const diagnosticSuiteExecutionModel = this.models[ModelName.DiagnosticSuiteExecution];
      const diagnosticTestModel = this.models[ModelName.DiagnosticTest];
      const suiteExecution = await diagnosticSuiteExecutionModel.findById(input.suiteExecutionId);
      if (!suiteExecution) {
        throw new Error(`Suite execution with ID ${input.suiteExecutionId} not found`);
      }
      const test = await diagnosticTestModel.findById(input.testId);
      if (!test) {
        throw new Error(`Test with ID ${input.testId} not found`);
      }
      const serialNumber = suiteExecution.serialNumber;
      const device = await epikBoxModel.findOne({ serialNumber: serialNumber });
      const execution = await diagnosticTestExecutionModel.create({
        testId: new ObjectId(input.testId),
        suiteExecutionId: new ObjectId(input.suiteExecutionId),
        serialNumber,
        userId: new ObjectId(userId),
        status: DiagnosticTestStatus.RUNNING,
        startTime: new Date(),
        creationDate: new Date(),
        deleted: false,
      });
      if (!device) {
        throw new Error(`Device with serial number ${serialNumber} not found`);
      }
      const deviceVersion = epikBoxModel.getDeviceVersion(device);

      const testCommand = this.getTestCommand(test, deviceVersion);
      if (!testCommand) {
        throw new Error(`No test command found for device version ${deviceVersion}`);
      }

      try {
        const result = await this.edgeProxyService.executeSshCommand(
          serialNumber,
          testCommand.command
        );
        const endTime = new Date();

        const validation = this.validateTestOutput(result.output, test.validations);
        const testStatus = validation.passed
          ? DiagnosticTestStatus.PASS
          : DiagnosticTestStatus.FAILED;

        await diagnosticTestExecutionModel.update(execution._id!.toString(), {
          status: testStatus,
          output: result.output,
          errorMessage: result.error_message,
          endTime,
          duration: endTime.getTime() - execution.startTime.getTime(),
        });
        return diagnosticTestExecutionModel.findById(execution._id!.toString());
      } catch (executionError) {
        await diagnosticTestExecutionModel.update(execution._id!.toString(), {
          status: DiagnosticTestStatus.ERROR,
          errorMessage: (executionError as Error).message,
          endTime: new Date(),
        });
        throw executionError;
      }
    } catch (error) {
      logger.error(
        {
          error: (error as Error).message,
          testId: input.testId,
        },
        'Diagnostic test execution failed'
      );

      throw error;
    }
  }
  async attemptFix(
    testId: string,
    device: EpikBoxDocument,
    executionId: string
  ): Promise<DiagnosticTestExecutionDocument | null> {
    try {
      const deviceVersion = this.models.epikboxes.getDeviceVersion(device);
      const test = await this.models.diagnostictests.findById(testId);
      if (!test) {
        throw new Error(`Test with ID ${testId} not found`);
      }
      const fixCommands = this.getFixCommands(test, deviceVersion);

      if (!fixCommands || !fixCommands.commands.length) {
        throw new Error(`No fix commands available for this test`);
      }
      //update test execution in database

      let allOutput = '';
      let lastError = '';

      // Execute each fix command in sequence
      for (const command of fixCommands.commands) {
        try {
          const result = await this.edgeProxyService.executeSshCommand(
            device.serialNumber,
            command
          );

          allOutput += `Command: ${command}\nOutput: ${result.output}\n\n`;

          if (!result.success) {
            lastError = result.error_message;
          }
        } catch (error) {
          lastError = (error as Error).message;
          allOutput += `Command: ${command}\nError: ${lastError}\n\n`;
        }
      }
      await this.models.diagnostictestexecutions.update(executionId, {
        fixAttempted: true,
        fixOutput: allOutput,
        fixErrorMessage: lastError,
      });
      const testCommand = this.getTestCommand(test, deviceVersion);
      if (testCommand) {
        const verifyResult = await this.edgeProxyService.executeSshCommand(
          device.serialNumber,
          testCommand.command
        );

        const validation = this.validateTestOutput(verifyResult.output, test.validations);
        await this.models.diagnostictestexecutions.update(executionId, {
          status: validation.passed ? DiagnosticTestStatus.FIXED : DiagnosticTestStatus.FAILED,
        });
        return this.models.diagnostictestexecutions.findById(executionId);
      }

      return this.models.diagnostictestexecutions.findById(executionId);
    } catch (error) {
      logger.error({ error: (error as Error).message, testId }, 'Fix attempt failed');
      await this.models.diagnostictestexecutions.update(executionId, {
        status: DiagnosticTestStatus.ERROR,
        fixErrorMessage: (error as Error).message,
      });
      return this.models.diagnostictestexecutions.findById(executionId);
    }
  }
  async getTests(filters?: { enabled?: boolean }): Promise<DiagnosticTestDocument[]> {
    const model = this.models.diagnostictests;

    const query: any = { deleted: { $ne: true } };

    if (filters?.enabled !== undefined) {
      query.enabled = filters.enabled;
    }

    return model.findMany(query, { sort: { executionOrder: 1 } });
  }

  private getTestCommand(
    test: DiagnosticTestDocument,
    deviceVersion: EpikBoxVersion
  ): DiagnosticTestCommand | null {
    let command = test.testCommands.find(c => c.version === deviceVersion);

    if (!command) {
      command = test.testCommands.find(c => c.version === EpikBoxVersion.ALL);
    }

    if (!command && test.testCommands.length > 0) {
      command = test.testCommands[0];
      logger.warn(
        {
          testId: test._id,
          deviceVersion,
          availableVersions: test.testCommands.map(c => c.version),
        },
        'No exact version match found, using first available command'
      );
    }

    return command || null;
  }

  /**
   * Get appropriate fix commands for device version
   */
  private getFixCommands(
    test: DiagnosticTestDocument,
    deviceVersion: EpikBoxVersion
  ): DiagnosticFixCommand | null {
    if (!test.fixCommands || test.fixCommands.length === 0) {
      return null;
    }

    let fixCommand = test.fixCommands.find(c => c.version === deviceVersion);

    if (!fixCommand) {
      fixCommand = test.fixCommands.find(c => c.version === EpikBoxVersion.ALL);
    }

    if (!fixCommand && test.fixCommands.length > 0) {
      fixCommand = test.fixCommands[0];
      logger.warn(
        {
          testId: test._id,
          deviceVersion,
          availableVersions: test.fixCommands.map(c => c.version),
        },
        'No exact version match found for fix commands, using first available'
      );
    }

    return fixCommand || null;
  }

  private validateTestOutput(
    output: string,
    validations: DiagnosticTestValidation[]
  ): { passed: boolean; details: string[] } {
    const details: string[] = [];
    let allPassed = true;

    for (const validation of validations) {
      let passed = false;

      switch (validation.type) {
        case 'contains':
          passed = validation.expectedValue ? output.includes(validation.expectedValue) : false;
          details.push(`Contains "${validation.expectedValue}": ${passed ? 'PASS' : 'FAIL'}`);
          break;

        case 'not_contains':
          passed = validation.expectedValue ? !output.includes(validation.expectedValue) : true;
          details.push(
            `Does not contain "${validation.expectedValue}": ${passed ? 'PASS' : 'FAIL'}`
          );
          break;

        case 'equals':
          passed = output.trim() === validation.expectedValue?.trim();
          details.push(`Equals "${validation.expectedValue}": ${passed ? 'PASS' : 'FAIL'}`);
          break;

        case 'not_equals':
          passed = output.trim() !== validation.expectedValue?.trim();
          details.push(`Not Equals "${validation.expectedValue}": ${passed ? 'PASS' : 'FAIL'}`);
          break;

        case 'greater_than_and_equals':
          const outputNumber = parseFloat(output.trim());
          const expectedNumber = parseFloat(validation.expectedValue || '');
          passed = !isNaN(outputNumber) && !isNaN(expectedNumber) && outputNumber >= expectedNumber;
          details.push(`Not Equals "${validation.expectedValue}": ${passed ? 'PASS' : 'FAIL'}`);
          break;

        case 'regex':
          if (validation.pattern) {
            const regex = new RegExp(validation.pattern);
            passed = regex.test(output);
            details.push(`Matches pattern "${validation.pattern}": ${passed ? 'PASS' : 'FAIL'}`);
          }
          break;

        case 'custom':
          // For custom validations, we'll implement specific logic based on the function name
          passed = this.executeCustomValidation(validation.customFunction || '', output);
          details.push(
            `Custom validation "${validation.customFunction}": ${passed ? 'PASS' : 'FAIL'}`
          );
          break;

        default:
          details.push(`Unknown validation type "${validation.type}": SKIP`);
          continue;
      }

      if (!passed) {
        allPassed = false;
      }
    }

    return { passed: allPassed, details };
  }

  /**
   * Execute custom validation functions
   */
  private executeCustomValidation(functionName: string, output: string): boolean {
    switch (functionName) {
      default:
        logger.warn({ functionName }, 'Unknown custom validation function');
        return false;
    }
  }
}

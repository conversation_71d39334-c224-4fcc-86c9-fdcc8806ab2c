import { ValidationError } from '@/middleware/errorHandler';
import { getAllModels, ModelInstanceMap, PhoneDocument, } from '@/models';
import { EpikBoxDocument, EpikBoxFilterInput, ModemInfoDocument, SensorData, WifiStatus, NetworkInfo, PortForwardObj, DcConnectionStatsResponse, VSwitchTab, FetchLtePerf } from '@/models/epikbox';
import { SystemInfoDocument, DcAvgPingDocument } from '@/models/systemInfo';
import { ListEpikBoxPaginatedResultType, PaginatedResult, PaginationInput } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { ObjectId } from 'mongodb';
const logger = createModuleLogger('EpikBoxService');

export interface UpdateEpikBoxData {
  serialNumber?: string;
  vpnAddress?: string;
  powerState?: string;
  devicePower?: string;
  cpuTemp?: string;
  signalStrength?: string;
  activeInterface?: string;
  sensorData?: SensorData;
  modemInfo?: ModemInfoDocument;
  lanIp?: string;
  publicIp?: string;
  deviceOnline?: boolean;
  simStatus?: string;
  dcAvgPing?: DcAvgPingDocument;
  registered?: boolean;
  wifiStatus?: WifiStatus;
  networkInfo?: NetworkInfo;
  nightlyUpdateTime?: Date;
  portForwardList?: PortForwardObj[];
  priorityInterface?: string;
  primarySim?: string;
  currentApn?: string;
  epikUpdateStatus?: boolean;
  dcConnectionStats?: DcConnectionStatsResponse;
  dnsCheck?: boolean;
  vSwitchTab?: VSwitchTab;
  preferredProviderTest?: FetchLtePerf;
}

const searchableFields: (keyof EpikBoxFilterInput)[] = [
  'serialNumber',
  'vpnAddress',
  'displayName',
  'imei',
  'sim',
  'status',
  'shipingNumber',
  'assignedNumber',
];
const searchableFieldsDBMap: { [key in keyof EpikBoxFilterInput]?: string } = {
  imei: 'modems.imeis',
  sim: 'modems.sims',
  status: 'registered',
  shipingNumber: 'trackingInfo.trackingNumber',
};

export class EpikBoxService {
  private models: ModelInstanceMap | null = null;

  constructor(models?: ModelInstanceMap) {
    if (models) {
      this.models = models;
    }
  }

  private getModels(): ModelInstanceMap {
    if (!this.models) {
      this.models = getAllModels();
    }
    return this.models;
  }
  async getEpikBoxById(id: String): Promise<EpikBoxDocument | null> {
    return await this.getModels().epikboxes.findById(id);
  }

  async listEpikBoxes(
    filter: EpikBoxFilterInput,
    pagination: PaginationInput
  ): Promise<PaginatedResult<EpikBoxDocument>> {
    const page = pagination?.page || 1;
    const pageSize = pagination?.pageSize || 20;

    const applyRegex = (value?: string) =>
      value?.trim() ? { $regex: value.trim(), $options: 'i' } : undefined;

    const match: Record<string, any> = {};
    const orConditions: any[] = [];

    const hasCompanyFilter = !!filter?.companyName?.trim();
    const trimmedQuery = filter?.query?.trim();

    if (!filter?.isAll) {
      match['assignedTo'] = { $in: filter?.ids || [] };
    }

    if (hasCompanyFilter) {
      //check count of companies with compny filter if greater than 50 ignore this request otherwise get companies id of all companies and add in filter
      const companyRegex = applyRegex(filter.companyName);
      const filterQuery = { name: companyRegex } as any;
      if (!filter?.isAll) {
        filterQuery._id = { $in: filter?.ids || [] };
      }
      const count = await this.getModels().companiesv2.count(filterQuery);
      if (count > 50) {
        throw new ValidationError('Too many companies to filter');
      }
      const companyIds = await this.getModels().companiesv2.findMany(filterQuery, { _id: 1 });
      match['assignedTo'] = { $in: companyIds.map((c: any) => c._id) };
    }

    if (filter) {
      for (const key of searchableFields) {
        const value = filter[key];
        const dbKey = searchableFieldsDBMap[key] || key;

        if (key === 'status' && typeof value === 'string') {
          if (value === 'registered') {
            match['registered'] = true;
          } else if (value === 'unregistered') {
            match['registered'] = false;
          }
          continue;
        }

        if (key === 'assignedNumber') {
          if (typeof value === 'string') {
            try {
              match['_id'] = new ObjectId(value);
            } catch {
              throw new ValidationError('Invalid EpikBox ID for assignedNumber');
            }
          } else if (Array.isArray(value)) {
            try {
              match['_id'] = {
                $in: value.map(v => new ObjectId(v)),
              };
            } catch {
              throw new ValidationError('Invalid EpikBox IDs for assignedNumber');
            }
          }
          continue;
        }

        const regex = typeof value === 'string' ? applyRegex(value) : value;
        if (regex) match[dbKey] = regex;
      }
    }

    if (trimmedQuery) {
      const queryRegex = applyRegex(trimmedQuery);
      for (const field of searchableFields) {
        const dbKey = searchableFieldsDBMap[field] || field;
        orConditions.push({ [dbKey]: queryRegex });
      }
    }

    if (orConditions.length) {
      match.$or = orConditions;
    }

    const docs = await this.getModels().epikboxes.findWithPagination(match, pagination);

    return docs;
  }

  updateEpikBox = async (id: string, data: UpdateEpikBoxData): Promise<EpikBoxDocument | null> => {
    logger.info('updateEpikBox');
    logger.debug({ id, data }, 'updateEpikBox');
    let update: Partial<EpikBoxDocument> = {};
    if (data.powerState) {
      update['powerState'] = data.powerState;
    }
    if (data.activeInterface) {
      update['activeInterface'] = data.activeInterface;
    }
    if (data.signalStrength) {
      update['signalStrength'] = data.signalStrength;
    }
    if (data.sensorData) {
      update['sensorData'] = data.sensorData;
    }
    if (data.modemInfo) {
      update['modemInfo'] = data.modemInfo;
    }
    if (data.lanIp) {
      update['lanIp'] = data.lanIp;
    }
    if (data.publicIp) {
      update['publicIp'] = data.publicIp;
    }
    if (data.hasOwnProperty('deviceOnline')) {
      update['deviceOnline'] = data.deviceOnline;
    }
    if (data.simStatus) {
      update['simStatus'] = data.simStatus;
    }
    if (data.dcAvgPing) {
      update['dcAvgPing'] = data.dcAvgPing;
    }
    if (data.hasOwnProperty('registered')) {
      update['registered'] = data.registered;
    }
    if (data.wifiStatus) {
      update['wifiStatus'] = data.wifiStatus;
    }
    if (data.networkInfo) {
      update['networkInfo'] = data.networkInfo;
    } 
    if (data.nightlyUpdateTime) {
      update['nightlyUpdateTime'] = data.nightlyUpdateTime;
    }
    if (data.portForwardList) {
      update['portForwardList'] = data.portForwardList;
    }
    if (data.priorityInterface) {
      const priorityInterface = data.priorityInterface && data.priorityInterface.split(',')[0]
      update['priorityInterface'] = priorityInterface;
    }
    if (data.primarySim) {
      update['primarySim'] = data.primarySim;
    }
    if (data.currentApn) {
      update['currentApn'] = data.currentApn;
    }
    if (data.hasOwnProperty('epikUpdateStatus')) {
      update['epikUpdateStatus'] = data.epikUpdateStatus;
    }
    if (data.dcConnectionStats) {
      update['dcConnectionStats'] = data.dcConnectionStats;
    }
    if (data.hasOwnProperty('dnsCheck')) {
      update['dnsCheck'] = data.dnsCheck;
    }
    if (data.vSwitchTab) {
      update['vSwitchTab'] = data.vSwitchTab;
    }
    if (data.preferredProviderTest) {
      update['preferredProviderTest'] = data.preferredProviderTest;
    }
    return await this.getModels().epikboxes.update(id, update);
  };

  async listPhonesByIds(phoneIds: ObjectId[]): Promise<PhoneDocument[]> {
    return this.getModels().phones.findMany({ _id: { $in: phoneIds } }, {}, phoneIds);
  }

  async getSystemInfoByBoxId(boxId: string): Promise<SystemInfoDocument | null> {
    // console.log('getSystemInfoByBoxId ', boxId,);
    return this.getModels().systemInfo.findOne({ boxId: new ObjectId(boxId) });
  }
  async updateSystemInfoByBoxId(boxId: string, data: any): Promise<SystemInfoDocument | null> {
    // console.log('updateSystemInfoByBoxId ', boxId, Object.keys(data), data.sysInfo);

    // Prepare the data with boxId
    const systemInfoData: Partial<SystemInfoDocument> = {
      boxId: new ObjectId(boxId),
      ...data.sysInfo
    };

    systemInfoData.lastUpdated = new Date();

    let result: SystemInfoDocument | null = null;

    const existingData = await this.getModels().systemInfo.findOne({ boxId: new ObjectId(boxId) });
    if (existingData) {
      // Use upsert to create or update the systeminfo document
      result = await this.getModels().systemInfo.update(
        existingData._id.toString(),
        systemInfoData
      );
    }
    else {
      result = await this.getModels().systemInfo.create(systemInfoData);
    }

    return result;
  }

}

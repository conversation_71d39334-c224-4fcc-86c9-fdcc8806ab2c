import { EpiDocument, EpiPort, getAllModels, ModelInstanceMap, PortConfigTemplateDocument, EpiRegistrationMeta, PortPhysicalMeta, EpiDocumentPopulated } from '@/models';
import { EpiFilterInput, ListEpiPaginatedResultType, PaginationInput } from '@/types';
import { Filter, ObjectId } from 'mongodb';
import { createModuleLogger } from '@/utils/logger';
const logger = createModuleLogger('EpiService')

export interface UpdateEpiData {
  registrationMeta?: EpiRegistrationMeta;
  portPhysicalMeta?: PortPhysicalMeta;
  createVmBox?: {  portKey: string,  _id?: ObjectId;}
  deleteVmBox?: {  portKey: string}
}

function escapeRegex(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export class EpiService {
  private models: ModelInstanceMap | null = null;

  constructor(models?: ModelInstanceMap) {
    if (models) {
      this.models = models;
    }
  }

  private getModels(): ModelInstanceMap {
    if (!this.models) {
      this.models = getAllModels();
    }
    return this.models;
  }


  async getEpiById(id: String): Promise<EpiDocument | null> {
    return await this.getModels().obis.findById(id);
  }

  async listEpiByIds(epiId: ObjectId[]): Promise<EpiDocument[]> {
    return this.getModels().obis.findMany({ _id: { $in: epiId } }, {}, epiId);
  }

  async listEpi(filter: EpiFilterInput, pagination: PaginationInput): Promise<ListEpiPaginatedResultType> {
    const match: Filter<EpiDocument> = { deleted: false };

    if (filter.macAddress?.trim()) {
      const safeMac = escapeRegex(filter.macAddress.trim());
      match.macAddress = { $regex: safeMac, $options: 'i' };
    }

    if (filter.obiNumber?.trim()) {
      const safeEpi = escapeRegex(filter.obiNumber.trim());
      match.obiNumber = { $regex: safeEpi, $options: 'i' };
    }
    return this.getModels().obis.findWithPagination(match, pagination);
  }

  async findPortConfigTemplateById(id: string): Promise<PortConfigTemplateDocument | null> {
    return await this.getModels().portconfigtemplates.findById(id);
  }


  updateEpi = async (id: string, data: UpdateEpiData): Promise<EpiDocumentPopulated | null> => {
    logger.info('updateEpi');
    logger.debug({ id, data }, 'updateEpi');
    let update: any = {};

    if (data?.registrationMeta) {
      update['registrationMeta'] = data.registrationMeta;
    }
    if (data?.portPhysicalMeta) {
      update['portPhysicalMeta'] = data.portPhysicalMeta;
    }
    if(data?.createVmBox){
      update[`${data?.createVmBox.portKey}.vmBox`] = data.createVmBox._id;
    }
    if(data?.deleteVmBox){
      update[`${data?.deleteVmBox.portKey}.vmBox`] = null;
      update[`${data?.deleteVmBox.portKey}.vmNumber`] = '';
    }
    return await this.getModels().obis.update(id, update);
  };

}

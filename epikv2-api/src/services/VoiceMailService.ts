import { NotFoundError } from '@/middleware/errorHandler';
import { VmailBoxDocument, VmailBoxModel } from '@/models/VmailBox';
import { EpiDocumentPopulated } from '@/models';
import { getAllModels, ModelInstanceMap } from '@/models';
// @ts-expect-error: email-validator may not have types
import { validate } from 'email-validator';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import path from 'node:path';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import util from 'node:util';
import { exec } from 'node:child_process';
// @ts-expect-error: ffmpeg-static may not have types
import ffmpegPath from 'ffmpeg-static';
import { ObjectId } from 'mongodb';
import { openSipAPI } from '@/utils/helpers';
import { createModuleLogger } from '@/utils/logger';
import { FastifyInstance } from 'fastify';

const logger = createModuleLogger('VoicemailService');
const execP = util.promisify(exec);

type FieldObj = { type: 'field'; value: string };

function isFieldObj(x: unknown): x is FieldObj {
  return !!x && typeof x === 'object' && (x as any).type === 'field' && 'value' in (x as any);
}

function readField(v: unknown): string {
  if (v == null) return '';
  if (typeof v === 'string') return v;
  if (isFieldObj(v)) return String(v.value ?? '');
  return String(v);
}

function parseList(v?: unknown): string[] {
  if (v == null) return [];
  if (Array.isArray(v)) return v.flatMap((item) => parseList(item)).filter(Boolean);
  const s = readField(v).trim();
  if (!s) return [];
  if (s.startsWith('[')) {
    try {
      const arr = JSON.parse(s);
      if (Array.isArray(arr)) return arr.map((x) => readField(x).trim()).filter(Boolean);
    } catch(err) {
      logger.error(err)
    }
  }
  return s.split(',').map((x) => x.trim()).filter(Boolean);
}

function truthy(v: unknown) {
  return ['1', 'true', 'yes', 'on'].includes(readField(v).trim().toLowerCase());
}

export class VoicemailService {
  private models: ModelInstanceMap | null = null;

  constructor(models?: ModelInstanceMap) {
    if (models) this.models = models;
  }

  private getModels(): ModelInstanceMap {
    if (!this.models) {
      this.models = getAllModels();
    }
    return this.models;
  }

  async getVoicemailBox(fastify: FastifyInstance, obiId: string, port: string): Promise<VmailBoxDocument> {
    const epi = await fastify.services.epiService.getEpiById(obiId);
    if (!epi) throw new NotFoundError('Epi not found');

    const wantedLabel = port.replace(/^port(\d+)$/i, 'Port $1').toLowerCase();
    const { port1, port2 } = epi as any;

    const portObj =
      [port1, port2].find(
        (p) => p && String(p.boxPortNumber || '').toLowerCase() === wantedLabel,
      ) || null;

    if (!portObj) throw new NotFoundError(`Port "${port}" not found on device`);

    const vmBoxId = portObj.vmBox;
    if (!vmBoxId) throw new NotFoundError('No voicemail box configured for this port.');

    const vmailBox = await this.getModels().vmailboxes.findById(vmBoxId);
    if (!vmailBox) throw new NotFoundError(`VmailBox "${vmBoxId}" not found`);

    return vmailBox;
  }

  async updateVoicemailBox(fastify: FastifyInstance, obiId: string, port: string, body: any): Promise<VmailBoxDocument> {
    const emailsIn = body.notificationEmails;
    const numbersIn = body.notificationNumbers;

    const notificationEmails = parseList(emailsIn);
    const notificationNumbers = parseList(numbersIn);

    const hadEmailsField = Object.prototype.hasOwnProperty.call(body, 'notificationEmails');
    const hadNumbersField = Object.prototype.hasOwnProperty.call(body, 'notificationNumbers');
    const wantRemoveGreeting = truthy(body.removeGreeting);

    const filePart =
      body.greeting && typeof body.greeting === 'object' && body.greeting.type === 'file'
        ? body.greeting
        : null;

    const epi = await fastify.services.epiService.getEpiById(obiId);
    if (!epi) throw new NotFoundError('Epi not found');

    const wantedLabel = port.replace(/^port(\d+)$/i, 'Port $1').toLowerCase();
    const { port1, port2 } = epi as any;
    const portObj =
      [port1, port2].find(
        (p) => p && String(p.boxPortNumber || '').toLowerCase() === wantedLabel,
      ) || null;

    if (!portObj) throw new NotFoundError(`Port "${port}" not found on device`);

    const portKey = portObj === port1 ? 'port1' : 'port2';

    const assignedNumberId = portObj.assignedNumberRef;
    if (!assignedNumberId) throw new NotFoundError('No Assigned Number for this port');

    const didNumber = await fastify.services.numberService.findNumberById(assignedNumberId);
    if (!didNumber || !['voice', 'modem', 'hunt'].includes(didNumber.type)) {
      throw new Error('No voice number is assigned to port.');
    }

    let filePath: string | undefined | null;
    if (filePart) {
      const baseDir = process.env.VMAIL_GREETING_PATH || '/data/voicemail/greetings';
      await fsp.mkdir(baseDir, { recursive: true });
      const base = path.join(baseDir, String(didNumber.number));
      const filename = String(filePart.filename || '').toLowerCase();
      const isWav = filename.endsWith('.wav');
      const isMp3 = filename.endsWith('.mp3');
      if (!isWav && !isMp3) throw new Error('Please upload mp3 or wav file');

      const buf: Buffer =
        typeof filePart.toBuffer === 'function'
          ? await filePart.toBuffer()
          : (filePart as any)._buf;
      if (!buf || !(buf instanceof Buffer)) throw new Error('Uploaded file payload missing.');

      if (isWav) {
        await fsp.writeFile(`${base}.wav`, buf);
        filePath = `${base}.wav`;
      } else {
        await fsp.writeFile(`${base}_.mp3`, buf);
        try {
          const bin = (ffmpegPath as string) || 'ffmpeg';
          await execP(`"${bin}" -y -i "${base}_.mp3" "${base}.wav"`);
          await fsp.unlink(`${base}_.mp3`).catch(() => {});
          filePath = `${base}.wav`;
        } catch {
          await fsp.rename(`${base}_.mp3`, `${base}.mp3`).catch(async () => {
            await fsp.writeFile(`${base}.mp3`, buf);
            await fsp.unlink(`${base}_.mp3`).catch(() => {});
          });
          filePath = `${base}.mp3`;
        }
      }
      logger.info({ savedTo: filePath }, 'voicemail-greeting-saved');
    }

    const vmailBoxModel = new VmailBoxModel(fastify);
    let vmailBox = (await vmailBoxModel.findOne({ number: didNumber._id })) as
      | (VmailBoxDocument & { _id: ObjectId })
      | null;

    const set: Partial<VmailBoxDocument> = {};

    if (wantRemoveGreeting) {
      if (vmailBox?.greeting) {
        fsp.unlink(vmailBox.greeting).catch(() => {});
      }
      set.greeting = '';
    } else if (filePath) {
      set.greeting = filePath;
    }

    if (hadEmailsField) {
      const valid = notificationEmails.filter((e) => validate(e));
      set.notificationEmails = valid;
    }
    if (hadNumbersField) {
      const valid = notificationNumbers.filter((n) => !!parsePhoneNumberFromString(n, 'US'));
      set.notificationNumbers = valid;
    }

    if (!vmailBox) {
      const _id = new ObjectId();
      const doc: VmailBoxDocument & { _id: ObjectId } = {
        _id,
        pin: '1234',
        totalMessages: 0,
        limit: 0,
        greeting: set.greeting ?? '',
        notificationEmails: set.notificationEmails ?? [],
        notificationNumbers: set.notificationNumbers ?? [],
        number: didNumber._id,
        deleted: false,
      };
      await vmailBoxModel.create(doc as any);
      vmailBox = doc;
      await fastify.services.epiService.updateEpi(obiId, { createVmBox: { portKey, _id: _id} });
    } else {
      const updatedRes = await vmailBoxModel.update(vmailBox._id.toString(), set as any, {
        returnDocument: 'after',
      });
      const updatedDoc =
        updatedRes && (updatedRes as any).value ? (updatedRes as any).value : updatedRes;
      if (updatedDoc) vmailBox = updatedDoc as typeof vmailBox;

      if (!portObj.vmBox || String(portObj.vmBox) !== String(vmailBox._id)) {
        await fastify.services.epiService.updateEpi(obiId, { createVmBox : { portKey, _id: vmailBox._id } });
      }
    }

    const box = await fastify.services.epikBoxService.getEpikBoxById(
      (epi as EpiDocumentPopulated).assignedTo.toString(),
    );

    if (box?.datacenter) openSipAPI(box.datacenter, didNumber.number);

    return vmailBox;
  }

  async getVoicemailFile(fastify : FastifyInstance, obiId: string, port: string) {
    const vmailBox = await this.getVoicemailBox(fastify, obiId, port);
    const filePath = vmailBox.greeting;
    if (!filePath) throw new NotFoundError('No greeting file configured.');

    await fsp.access(filePath, fs.constants.R_OK).catch(() => {
      throw new NotFoundError('No file found.');
    });

    const stat = await fsp.stat(filePath);
    const ext = path.extname(filePath).toLowerCase();
    const mime = ext === '.mp3' ? 'audio/mpeg' : 'audio/wav';

    return { filePath, mime, stat };
  }

  async disableVM(
    fastify: FastifyInstance,
    obiId: string,
    body: { number: string; port: string; boxId: string; isBoxTab?: boolean }
    ) {
    const vmailBoxModel = new VmailBoxModel(fastify);
    const { number, port, boxId } = body;
    logger.info(`disableVM ${port} ${number}`);

    const epi = await fastify.services.epiService.getEpiById(obiId);
    if (!epi) throw new NotFoundError('Epi not found');

    const epikBox = await fastify.services.epikBoxService.getEpikBoxById(
        boxId.toString()
    );
    if (!epikBox) throw new NotFoundError('Invalid boxId');

    const wantedLabel = port.replace(/^port(\d+)$/i, 'Port $1').toLowerCase();
    const { port1, port2 } = epi as any;

    const portObj =
        [port1, port2].find(
        (p) => p && String(p.boxPortNumber || '').toLowerCase() === wantedLabel
        ) || null;
    if (!portObj) throw new NotFoundError(`Port "${port}" not found on device`);

    const portKey = portObj === port1 ? 'port1' : 'port2';

    const vmBoxId = portObj.vmBox;
    if (!vmBoxId) throw new NotFoundError('No voicemail box configured for this port.');

    let vmailBox = await this.getVoicemailBox(fastify, obiId, port);
    if (!vmailBox) throw new NotFoundError(`VmailBox "${vmBoxId}" not found`);

    const assignedNumberId = portObj.assignedNumberRef;
    if (!assignedNumberId) throw new NotFoundError('No Assigned Number for this port');

    const didNumber = await fastify.services.numberService.findNumberById(assignedNumberId);
    portObj.vmNumber = '';

    if (vmailBox && vmailBox._id) {
        const deleteRes = await vmailBoxModel.delete(vmailBox._id.toString());
        if (!deleteRes) {
            throw new Error(`Failed to delete VmailBox ${vmailBox._id}`);
        }

        if (portObj.vmBox && String(portObj.vmBox) === String(vmailBox._id)) {
            await fastify.services.epiService.updateEpi(obiId, { deleteVmBox: { portKey } });
        }

        if (epikBox.datacenter && didNumber) {
            openSipAPI(epikBox.datacenter, String(didNumber.number));
        }

        if (number) {
            const numObj = await fastify.services.numberService.findByNumber(number);
            if (numObj && numObj._id) {
              numObj.assignedTo = null as any;
              numObj.linkedBox = null as any;
              numObj.forwardNumbers = [];
              numObj.allowedUsers = [];
              numObj.allowedNumbers = [];
              numObj.type = 'unassigned';

              await fastify.services.numberService.updateNumber(numObj._id.toString(), numObj);

              const formatted = parsePhoneNumberFromString(number, 'US');
              if (formatted?.nationalNumber) {
                  await execP(
                  `/bin/sed -ie '/vm_did_${formatted.nationalNumber}/,+7d' /usr/local/freeswitch/conf/dialplan/public/voicemail_extension.xml`
                  ).catch((err) => logger.error(err));
                  await execP(
                  `sudo /usr/local/freeswitch/bin/fs_cli -x 'reloadxml'`
                  ).catch((err) => logger.error(err));
              }
            }
        }

        return { success: true };
    }
    throw new Error('Failed to disable voicemail: invalid VmailBox state');
  }

}


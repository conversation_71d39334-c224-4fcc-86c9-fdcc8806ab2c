import { NumberDocument } from '@/models';
import { BaseE911Service, E911GeocodeData, E911UpdateData, E911UpdateResult } from './BaseE911Service';
import { BandwidthE911Service } from './bandwidthE911Service';
import { VoicetelE911Service } from './VoicetelE911Service';
import logger from '@/utils/logger';

export class E911ServiceFactory {
  private BandwidthE911Service: BandwidthE911Service;
  private VoicetelE911Service: VoicetelE911Service;

  constructor() {
    this.BandwidthE911Service = new BandwidthE911Service();
    this.VoicetelE911Service = new VoicetelE911Service();
  }

  /**
   * Get the appropriate Service for a given number
   */
  private getCarrierService(number: NumberDocument): BaseE911Service | BandwidthE911Service | VoicetelE911Service {
    if (this.BandwidthE911Service.isNumberSupported(number)) {
      return this.BandwidthE911Service;
    } else if (this.VoicetelE911Service.isNumberSupported(number)) {
      return this.VoicetelE911Service;
    } else {
      throw new Error(`No carrier service found for number ${number.number} with carrier ${number.e911Carrier}`);
    }
  }
  

  /**
   * Update E911 number using the appropriate Service
   */
  async updateE911Number(
    number: NumberDocument,
    data: E911UpdateData
  ): Promise<E911UpdateResult> {
    try {
      // const carrierService = this.getCarrierService(number); // return the Service Bandwidth or voicetel based on the number.e911Carrier \
      // logger.info(`Using ${carrierService.getCarrierName()} service for number ${number.number}`);
      
      return await  this.BandwidthE911Service.updateE911Number(number, data);
    } catch (error) {
      logger.error(`Error in Service factory for number ${number.number}:`, error);
      return {
        success: false,
        error: `Failed to determine carrier service: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * to get E911 Info using the appropriate Service 
   */
  async getE911NumberInfo(
    // number: NumberDocument,
    number: any,
  ): Promise<E911UpdateResult> {
    try {
      const carrierService = this.getCarrierService(number);
      logger.info(`Using ${carrierService.getCarrierName()} service for number ${number.number}`);
      
      return await carrierService.getE911NumberInfo(number);
    } catch (error) {
      logger.error(`Error in Service factory for number ${number.number}:`, error);
      return {
        success: false,
        error: `Failed to determine carrier service: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * to remove E911 Info using the appropriate Service 
   */
  async removeE911Number(
    // number: NumberDocument,
    number: any,
  ): Promise<E911UpdateResult> {
    try {
      const carrierService = this.getCarrierService(number);
      logger.info(`Using ${carrierService.getCarrierName()} service for number ${number.number}`);
      
      return await carrierService.removeE911Number(number);
    } catch (error) {
      logger.error(`Error in Service factory for number ${number.number}:`, error);
      return {
        success: false,
        error: `Failed to determine carrier service: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  checkGeocode(addressData: E911GeocodeData): Promise<any> {
    // Try to get the carrier service using a compatible NumberDocument or fallback logic
    try {
      // If addressData contains a number property, use it to find the carrier service
      return this.BandwidthE911Service.checkGeocode(addressData);
    } catch (error) {
      return Promise.reject(
        `Failed to determine carrier service for geocode check: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Check if a number is supported by any Service
   */
  isNumberSupported(number: NumberDocument): boolean {
    return this.BandwidthE911Service.isNumberSupported(number) || 
           this.VoicetelE911Service.isNumberSupported(number);
  }
}

import { NumberDocument } from '@/models';
import { BaseE911Service, E911UpdateData , E911GeocodeData} from './BaseE911Service';
import logger from '@/utils/logger';
import { Builder, parseStringPromise } from 'xml2js';
import axios from 'axios';

export class BandwidthE911Service extends BaseE911Service {
  protected apiKey: string;
  // protected baseUrl: string = 'https://services.inteliquent.com/Services/';
  protected baseUrl: string = 'https://service.dashcs.com/dash-api/xml/emergencyprovisioning/v1';
  // https://services.bandwidth.com/e911/v1/locations
  constructor() {
    super();
    this.apiKey = process.env.BANDWIDTH_API_KEY || '';
  }

  /**
   * Check if number is supported by Bandwidth (bandwidth carrier)
   */
  isNumberSupported(number: NumberDocument): boolean {
    // return true;
    if(!number.hasOwnProperty('e911Carrier') || number.e911Carrier === 'bandwidth'){ 
      return true;
    }
    return false;
  }

  /**
   * Get carrier name
   */
  getCarrierName(): string {
    return 'Bandwidth';
  }

  /**
   * Update E911 number using Bandwidth API
   */
  async updateE911Number(
    number: NumberDocument,
    data: E911UpdateData
  ):  Promise<any> {
    try {
      logger.info(`Bandwidth: Updating E911 for number ${number.number}`);

      // if (!this.isNumberSupported(number)) {
      //   return {
      //     success: false,
      //     error: `Number ${number.number} is not supported by Bandwidth`
      //   };
      // }

      const formattedData = this.formatAddressData(data);
      
      // Bandwidth specific API call structure
      const requestBody = {
        addLocation: {
          uri: {
            uri: number.number,
            callername: formattedData.callername,
          },
          location: {
            address1: formattedData.address1,
            address2: formattedData.address2,
            community: formattedData.city,
            postalcode: formattedData.zip,
            state: formattedData.state,
            type: "ADDRESS",
          },
        },
      };

      // Make API call to Bandwidth
      const baseUrl = this.baseUrl;
      try {
        const url = `${baseUrl}/addlocation`;
        const builder = new Builder();
        const xml = builder.buildObject({ ...requestBody });
        const reqBody = xml.replace(
          '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>',
          ''
        );

        // const result = await axios.post(url, reqBody, { headers });
        const result = await this.makeBandwidthApiCall(url, 'POST',  reqBody);
        const data = result.data;
        if (!data) {
          throw new Error(`Error while updating address. Please contact administrator.`);
        }
        const parsedData = await this.parseResponseE911(data, 'Location');
        let { status, locationid } = parsedData;

      
        locationid = locationid;
        status = status.code;
        if (status.includes('GEOCODED')) {
          const provisionBody = `<provisionLocation>
                                          <locationid>${locationid}</locationid>
                                          </provisionLocation>`;
          const provisionUrl = `${baseUrl}/provisionlocation`;

          const prvisionResult = await this.makeBandwidthApiCall(provisionUrl, 'POST',  provisionBody);  
          const provisionData = prvisionResult.data;

          const parsedData = await this.parseResponseE911(provisionData, 'LocationStatus');
          let { code, description } = parsedData;
          console.log("parsedData add locations", code, description);  
          if (code == 'PROVISIONED') {
            logger.info(`Bandwidth: Successfully updated E911 for number ${number.number}`);
            return {
              success: true,
              statusCode: '200',
              message: 'Success',
            };
          }
          throw new Error(description);
        } else {
          // throw new Error(description);
        }
      } catch (err: any) {
        logger.error(`updateE911NumberBandwidth failed: ${err.message}`);
        throw new Error(err.message);
      }
      
    } catch (error) {
      logger.error(`Bandwidth: Error updating E911 for number ${number.number}:`, error);
      return this.handleApiError(error, 'E911 update');
    }
  }

  /**
   * Validate E911 number using Bandwidth API
   */
  async getE911NumberInfo(
    number: NumberDocument,
  ): Promise<any> {
    try {
      logger.info(`Bandwidth: Validating E911 for number ${number.number}`);

      if (!this.isNumberSupported(number)) {
        return {
          success: false,
          error: `Number ${number.number} is not supported by Bandwidth`
        };
      }

      
      // NO Address found response
    const NO_ADDR = {
      status: "Error",
      statusCode: "400",
      message: "No address found",
      entry_query: {
        address1: '',
        address2: '',
        callername: '',
        city: '',
        dn: '',
        state: '',        zip: '',
        locationid: '',
      }
    };

      // Make validation API call
      const num= number.number.replace('+', '');
      const baseUrl = `${this.baseUrl}/provisionedlocationbyuri/${num}`;
      // console.log(" baseUrl provisionedlocationbyuri", baseUrl);

      const result = await this.makeBandwidthApiCall(baseUrl, 'GET',  '');
      const data= result.data;
      if(!data){
        return NO_ADDR;
      }

      const parsedData = await this.parseResponseE911(data, 'Location');

    

      // let {status, location, address1,address2 } = parsedData;
      // let res= {
      //   status: status,
      //   address1: address1,
      //   address2: address2,
      //   locationid: location,
      // };

      // console.log(" parsedData provisionedlocationbyuri", parsedData);
      // return res;

       let {
        status,
        address1,
        address2,
        callername,
        community,
        postalcode,
        locationid,
        state,
        activatedtime,
      } = parsedData;
      // status = status.pop();
      status = status.code;
      address1 = address1;
      address2 = address2;
      callername = callername;
      community = community;
      postalcode = postalcode;
      locationid = locationid;
      activatedtime = activatedtime;
      state = state;
      if (typeof address1 !== 'string') address1 = '';
      if (typeof address2 !== 'string') address2 = '';
      if (typeof callername !== 'string') callername = '';
      if (typeof community !== 'string') community = '';
      if (typeof postalcode !== 'string') postalcode = '';
      if (typeof state !== 'string') state = '';
      
      if (status.includes('PROVISIONED')) {
        const result = {
          address1,
          address2,
          callername,
          city: community,
            dn: number.number,
          state,
          zip: postalcode,
          locationid,
          activatedtime,
        };
        logger.info(`Bandwidth: Successfully validated E911 for number ${number.number}`);
        return {
          entry_query: result,
          status: "Success",
          statusCode: "200",
        }
      }
      return NO_ADDR;

    } catch (error) {
      logger.error(`Bandwidth: Error validating E911 for number ${number.number}:`, error);
      return this.handleApiError(error, 'E911 validation');
    }
  }


  /**
   * Remove E911 number using Bandwidth API
   */
  async removeE911Number(
    number: NumberDocument,
  ): Promise<any> {
    try {
      logger.info(`Bandwidth: Removing E911 for number ${number.number}`);

      if (!this.isNumberSupported(number)) {
        return {
          success: false,
          error: `Number ${number.number} is not supported by Bandwidth`
        };
      }

      

      // Make API call
      const baseUrl = `${this.baseUrl}/removelocation`;
      console.log(" baseUrl removelocation", baseUrl);
      const e911Info = await this.getE911NumberInfo(number);
      console.log(" e911Info removelocation", e911Info);
      let locationid = e911Info.entry_query.locationid;
      const reqBody = `<removeLocation>
      <locationid>${locationid}</locationid>
      </removeLocation>`;

      const result = await this.makeBandwidthApiCall(baseUrl, 'POST',  reqBody);
      const data= result.data;

      let { code, description } = await this.parseResponseE911(
        data,
        'LocationStatus'
      );
      if (code == 'REMOVED') {
        return {
          status: 'Success',
          statusCode: '200',
        };
      }

      return {
        status: 'Error',
        statusCode: '400',
        message: description,
      };


    } catch (error) {
      logger.error(`Bandwidth: Error validating E911 for number ${number.number}:`, error);
      return this.handleApiError(error, 'E911 validation');
    }
  }


  async getAuthTokenBandwidth() {
    console.log("process.env.DASCHS_USERNAME", process.env.DASCHS_USERNAME);
    console.log("process.env.DASCHS_PASSWORD", process.env.DASCHS_PASSWORD);
    // return Buffer.from(
    //   `${process.env.DASCHS_USERNAME}:${process.env.DASCHS_PASSWORD}`
    // ).toString('base64');
    return "************************************************";
  }
    
  async parseResponseE911(xml: string, key: string) {
    const parsedData = await parseStringPromise(xml, { explicitArray: false });
    const responseKey = Object.keys(parsedData).pop();

    if (!responseKey || !(responseKey in parsedData)) {
      throw new Error('Invalid XML: No root response key found.');
    }

    const temp = parsedData[responseKey]?.[key];
    if (!temp) {
      throw new Error(`Missing key "${key}" in parsed response.`);
    }

    return temp;
  }
  /**
   * Make API call to Bandwidth
   */
  private async makeBandwidthApiCall(url: string, method: string, requestBody: any): Promise<any> {
  
    const authToken = await this.getAuthTokenBandwidth();

    const headers: Record<string, string> = {
      'Content-Type': 'application/xml',
      'Authorization': `Basic ${authToken}`,
    };

    let fetchOptions: any = {
      method: method,
      headers: headers,
    };
    // url="https://service.dashcs.com/dash-api/xml/emergencyprovisioning/v1/provisionedlocationbyuri/9709650813"
    // Only attach body for POST/PUT/PATCH
    if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && requestBody) {
      fetchOptions.body = requestBody;
    }

    const response = await fetch(url, fetchOptions);

    if (!response.ok) {
      const text = await response.text();
      throw new Error(`Bandwidth API error: ${response.status} ${response.statusText} - ${text}`);
    }

    // The API returns XML, not JSON
    const responseText = await response.text();
    return { data: responseText };
  }

  async checkGeocode(addressData: E911GeocodeData): Promise<{ success: boolean; data?: any }> {
    // This method gets geocode suggestions from Bandwidth's geocodeRequest endpoint.
    const { AddressLine1, AddressLine2, City, StateCode, Zip } = addressData;
      // Build XML request body
      const xmlData = `
        <RequestAddress>
          <AddressLine1>${AddressLine1}</AddressLine1>
          <AddressLine2>${AddressLine2 || ''}</AddressLine2>
          <City>${City}</City>
          <StateCode>${StateCode}</StateCode>
          <Zip>${Zip}</Zip>
        </RequestAddress>
      `.trim();

      try {
      const BANDWIDTH_USERNAME="epik911route";
      const BANDWIDTH_PASSWORD="uU6TGQs?e4i6";
      const BANDWIDTH_BASEURL="https://dashboard.bandwidth.com/api/accounts/5009007";
    

        // Make fetch request to Bandwidth geocodeRequest endpoint 
        const authString = `${BANDWIDTH_USERNAME}:${BANDWIDTH_PASSWORD}`;
        const authHeader = 'Basic ' + Buffer.from(authString).toString('base64');
        const response = await fetch(BANDWIDTH_BASEURL + "/geocodeRequest", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/xml',
            'Authorization': authHeader,
          },
          body: xmlData,
        });

        if (response?.ok) {
          // Success, address is valid
          return { success: true };
        } else if (response?.status === 409) {
            
            // 409 means suggestions are available
            const xml = await response.text();
            // Parse the XML response string into JSON using parseStringPromise
            const result = await parseStringPromise(xml, { explicitArray: false });
            const geocoded = result?.GeocodeRequestResponse?.GeocodedAddress || {};
            const suggestedAddress = {
              success: false,
              data: {
                address1: geocoded.AddressLine1 || "",
                address2: geocoded.AddressLine2 || "",
                city: geocoded.City || "",
                state: geocoded.StateCode || "",
                zip: geocoded.Zip || "",
                country: geocoded.Country || "",
              },
            };
            return suggestedAddress;
          } else {
            return { success: false };
          }
        
      } catch (error: any) {
        logger.error('Error in getSuggestions:', error);
        return { success: false,  };
      }
  }
}


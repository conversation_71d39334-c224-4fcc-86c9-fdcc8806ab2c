import { NumberDocument } from '@/models';

export interface E911UpdateData {
  address1: string;
  address2?: string;
  callername: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  isBoxTab: boolean;
  boxId: string;
}

export interface E911GeocodeData {
  AddressLine1: string;
  AddressLine2?: string;
  City: string;
  StateCode: string;
  Zip: string;
}

export interface E911UpdateResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}

export abstract class BaseE911Service {
  protected abstract apiKey: string;
  protected abstract baseUrl: string;

  constructor() {
    // this.validateConfiguration();
  }

  private validateConfiguration(): void {
    if (!this.apiKey) {
      throw new Error(`${this.constructor.name}: API key is required`);
    }
    if (!this.baseUrl) {
      throw new Error(`${this.constructor.name}: Base URL is required`);
    }
  }

  /**
   * Abstract method to update E911 number information
   * Must be implemented by each Service
   */
  updateE911Number(
    number: NumberDocument,
    data: E911UpdateData
  ): Promise<E911UpdateResult> {
    throw new Error('Not implemented');
  }

  /**
   * Abstract method to get E911 Info
   * Must be implemented by each Service
   */
  abstract getE911NumberInfo(
    number: NumberDocument,
  ): Promise<E911UpdateResult>;

  /**
   * Abstract method to remove E911 
   * Must be implemented by each Service
   */
  abstract removeE911Number(
    number: NumberDocument,
  ): Promise<E911UpdateResult>;


  /**
   * Optional method to check geocode.
   * Not required for all E911 services. Override in subclasses if needed.
   */
  checkGeocode?(addressData: E911GeocodeData): Promise<any>;

  /**
   * Common method to check if number is supported by this Service
   */
  abstract isNumberSupported(number: NumberDocument): boolean;

  /**
   * Common method to format address data
   */
  protected formatAddressData(data: E911UpdateData) {
    return {
      address1: data.address1,
      address2: data.address2 || '',
      city: data.city,
      state: data.state,
      zip: data.zip,
      callername: data.callername
    };
  }

  /**
   * Common method to get Service name
   */
  abstract getCarrierName(): string;

  /**
   * Common method to handle API errors for each Service
   */
  protected handleApiError(error: any, operation: string): E911UpdateResult {
    const errorMessage = error.message || error.toString();
    return {
      success: false,
      error: `${this.getCarrierName()} ${operation} failed: ${errorMessage}`,
      message: errorMessage
    };
  }

  /**
   * Common method to create success response for each Service
   */
  protected createSuccessResponse(data?: any, message?: string): E911UpdateResult {
    return {
      success: true,
      data,
      message: message || `${this.getCarrierName()} operation completed successfully`
    };
  }
}

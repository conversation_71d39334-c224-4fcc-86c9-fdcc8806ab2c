import { NumberDocument } from '@/models';
import { BaseE911Service, E911UpdateData, E911UpdateResult } from './BaseE911Service';
import logger from '@/utils/logger';

export class VoicetelE911Service extends BaseE911Service {
  protected apiKey: string;
  protected baseUrl: string = 'https://api.voicetel.com/v2.1';

  constructor() {
    super();
    this.apiKey = process.env.VTEL_KEY || '';
  }

  /**
   * Check if number is supported by Voicetel (non-bandwidth carriers)
   */
  isNumberSupported(number: NumberDocument): boolean {
    return number.e911Carrier !== 'bandwidth';
  }

  /**
   * Get carrier name
   */
  getCarrierName(): string {
    return 'Voicetel';
  }

  /**
   * Update E911 number using Voicetel API
   */
  // async updateE911Number(
  //   number: NumberDocument,
  //   data: E911UpdateData
  // ): Promise<E911UpdateResult> {
  //   try {
  //     logger.info(`Voicetel: Updating E911 for number ${number.number}`);

  //     if (!this.isNumberSupported(number)) {
  //       return {
  //         success: false,
  //         error: `Number ${number.number} is not supported by Voicetel`
  //       };
  //     }

  //     const formattedData = this.formatAddressData(data);
      
  //     // Voicetel specific API call structure
  //     const requestBody = {
  //       apikey: this.apiKey,
  //       number: number.number,
  //       address1: formattedData.address1,
  //       address2: formattedData.address2,
  //       callername: formattedData.callername,
  //       city: formattedData.city,
  //       state: formattedData.state,
  //       zip: formattedData.zip,
  //       boxId: data.boxId
  //     };

  //     // Make API call to Voicetel
  //     const url = `${this.baseUrl}/e911/validateAndProvision`;
  //     const result = await this.makeVoicetelApiCall(url, "POST", requestBody);
      
  //     logger.info(`Voicetel: Successfully updated E911 for number ${number.number}`);
  //     return this.createSuccessResponse(result, `E911 updated successfully for ${number.number}`);
      
  //   } catch (error) {
  //     logger.error(`Voicetel: Error updating E911 for number ${number.number}:`, error);
  //     return this.handleApiError(error, 'E911 update');
  //   }
  // }

  /**
   * Validate E911 number using Voicetel API
   */
  async getE911NumberInfo(
    number: NumberDocument,
  ): Promise<E911UpdateResult> {
    try {
      logger.info(`Voicetel: Validating E911 for number ${number.number}`);

      if (!this.isNumberSupported(number)) {
        return {
          success: false,
          error: `Number ${number.number} is not supported by Voicetel`
        };
      }

      
      const requestBody ={
          apikey: process.env.VTEL_KEY,
          dn: number.number,
      }

      // Make validation API call
      const url = `${this.baseUrl}/e911/queryRecord/`;
      const result = await this.makeVoicetelApiCall(url, 'POST',  requestBody);
      
      logger.info(`Voicetel: Successfully validated E911 for number ${number.number}`);
      return this.createSuccessResponse(result, `E911 validation successful for ${number.number}`);
      
    } catch (error) {
      logger.error(`Voicetel: Error validating E911 for number ${number.number}:`, error);
      return this.handleApiError(error, 'E911 validation');
    }
  }

  /**
   * Remove E911 number using Voicetel API
   */
  async removeE911Number(
    number: NumberDocument,
  ): Promise<E911UpdateResult> {
    try {
      logger.info(`Voicetel: Removing E911 for number ${number.number}`);

      if (!this.isNumberSupported(number)) {
        return {
          success: false,
          error: `Number ${number.number} is not supported by Voicetel`
        };
      }

      
      const requestBody ={
          apikey: process.env.VTEL_KEY,
          dn: number.number,
      }

      // Make validation API call
      const url = `${this.baseUrl}/e911/removeRecord/`;
      const result = await this.makeVoicetelApiCall(url, 'POST',  requestBody);
      
      logger.info(`Voicetel: Successfully validated E911 for number ${number.number}`);
      return this.createSuccessResponse(result, `E911 validation successful for ${number.number}`);
      
    } catch (error) {
      logger.error(`Voicetel: Error validating E911 for number ${number.number}:`, error);
      return this.handleApiError(error, 'E911 validation');
    }
  }

  /**
   * Make API call to Voicetel
   */
  private async makeVoicetelApiCall(url: string, method: string, requestBody: any): Promise<any> {
    const response = await fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestBody,
    });

    if (!response.ok) {
      throw new Error(`Voicetel API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

}

import { config } from '@/config/environment';
import {
  DcConnectionStatsResponse,
  EpiEntry,
  EpiRegistrationMeta,
  FetchLtePerf,
  ModemInfoDocument,
  NetworkInfo, PortForwardObj,
  PortPhysicalMeta,
  SensorData,
  SimPingInfo,
  SpeedTest,
  VSwitchTab,
  WifiStatus,
} from '@/models';
import { DcAvgPingDocument, SystemInfoDocument } from '@/models/systemInfo';
import { edge } from '@/types/generated';
import { GrpcServiceClient } from '@/utils/grpcClient';
import { createModuleLogger } from '@/utils/logger';
const logger = createModuleLogger('EdgeProxyService');
// Re-export the generated types for convenience
export type DeviceRequest = edge.v1.DeviceRequest;
export type DeviceResponse = edge.v1.DeviceResponse;
export type AsyncRequest = edge.v1.AsyncRequest;
export type AsyncResponse = edge.v1.AsyncResponse;
export type StatusRequest = edge.v1.StatusRequest;
export type StatusResponse = edge.v1.StatusResponse;
export type CancelRequestMessage = edge.v1.CancelRequestMessage;
export type CancelResponse = edge.v1.CancelResponse;
export type DashboardRequest = edge.v1.DashboardRequest;
export type DashboardResponse = edge.v1.DashboardResponse;
// export type SshCommandRequest = edge.v1.SshCommandRequest;
// export type SshCommandResponse = edge.v1.SshCommandResponse;
// export type SshCommandStatusResponse = edge.v1.SshCommandStatusResponse;

export class EdgeProxyService extends GrpcServiceClient<edge.v1.EdgeDeviceProxyClient> {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || config.edgeProxyUri,
      service: edge.v1.EdgeDeviceProxyClient,
      maxRetries: 3,
    });
    this.connect().then(() => {
      this.ping();
    });
  }
  async ping() {
    const request = new edge.v1.EchoRequest({ message: 'keepalive' });
    super.ping(request);
  }
  // Handle synchronous or asynchronous requests to edge devices
  async handleRequest(request: DeviceRequest): Promise<DeviceResponse> {
    return this.makeRequest('HandleRequest', request);
  }

  // Enqueue a request for asynchronous processing
  async enqueueRequest(request: AsyncRequest): Promise<AsyncResponse> {
    return this.makeRequest('EnqueueRequest', request);
  }

  // Get the status of an asynchronous request
  async getRequestStatus(request: StatusRequest): Promise<StatusResponse> {
    return this.makeRequest('GetRequestStatus', request);
  }

  // Cancel a pending asynchronous request
  async cancelRequest(request: CancelRequestMessage): Promise<CancelResponse> {
    return this.makeRequest('CancelRequest', request);
  }

  // Convenience method for simple device requests
  async proxyToDevice(serialNumber: string, path: string): Promise<DeviceResponse> {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber,
    });

    return this.handleRequest(request);
  }
  getLivePowerState = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('PowerSource', request);
    if (!response) return null;
    return response.power_source;
  };

  getLiveSignalStrength = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('SignalStrength', request);
    if (!response) return null;
    return response.signal_strength;
  };

  getLiveActiveInterface = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('ActiveInterface', request);
    if (!response) return null;
    return response.active_interface;
  };

  getLiveLanIp = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('LanIp', request);
    if (!response) return null;
    return response.lan_ip;
  };
  getLivePublicIp = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('PublicIp', request);
    if (!response) return null;
    return response.public_ip;
  };
  getLiveSimStatus = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('SimStatus', request);
    if (!response) return null;
    return response.sim_status;
  };
  getLiveModemInfo = async (serialNumber: String): Promise<ModemInfoDocument | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('ModemInfo', request);
    if (!response) return null;
    return {
      manufacturer_placeholder: response.modem_info.manufacturer_placeholder,
      manufacturer: response.modem_info.manufacturer,
      model_placeholder: response.modem_info.model_placeholder,
      model: response.modem_info.model,
      sim_placeholder: response.modem_info.sim_placeholder,
      sim: response.modem_info.sim,
      imei_placeholder: response.modem_info.imei_placeholder,
      imei: response.modem_info.imei,
      carrier_placeholder: response.modem_info.carrier_placeholder,
      carrier: response.modem_info.carrier,
      ipAddress_placeholder: response.modem_info.ipAddress_placeholder,
      ipAddress: response.modem_info.ipAddress,
    };
  };
  getLiveSensorData = async (
    serialNumber: String
  ): Promise<SensorData | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('SensorData', request);
    if (!response) return null;
    return { temp: response.temp, power: response.power };
  };
  getLiveDeviceOnline = async (serialNumber: String): Promise<Boolean> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('DeviceOnline', request);
    if (!response) return false;
    return response.online;
  };
  getLiveDcAvgPing = async (serialNumber: String): Promise<DcAvgPingDocument | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('DcAvgPing', request);
    if (!response) return null;
    return {
      atPingAvg: response.atPingAvg,
      bestDC: response.bestDC,
      bestLatency: response.bestLatency,
      chPingAvg: response.chPingAvg,
      dlPingAvg: response.dlPingAvg,
      laPingAvg: response.laPingAvg,
      nyPingAvg: response.nyPingAvg,
      timeUpdated: response.timeUpdate,
      error: response.error,
    };
  };
  pingBox = async (
    serialNumber: String,
    ip: String,
    onData: (data: string) => void,
    onEnd: () => void
  ): Promise<{ cancel: () => void }> => {
    const request = new edge.v1.PingMessage({
      command: new edge.v1.PingCommand({
        serial_number: serialNumber as string,
        stop: false,
        ip: ip as string,
      }),
    });
    logger.info('pingBox');
    const call = this.getClient().PingBox();
    call.on('error', (err: any) => {
      onData(`ERROR: ${err.message}`);
      cancel();
    });
    const cancel = () => {
      try {
        call.cancel();
        call.end();
      } catch (error) {
        logger.warn('Error cancelling ping call:', error);
      }
    };
    call.on('data', data => {
      if (data.line?.line) {
        const result = {
          line: data.line.line || null,
          completed: data.line.completed || false,
        };
        onData(result.line);
        if (result.completed) {
          cancel();
        }
      }
    });
    call.on('end', () => {
      onEnd();
    });
    call.write(request);
    return {
      cancel: () => {
        call.cancel();
        call.end();
      },
    };
  };

  getLiveRegStatus = async (serialNumber: String): Promise<Boolean> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('RegStatus', request);
    if (!response) return false;
    return response.registered;
  };

  getLivePortRegStatus = async (
    serialNumber: String,
    macAddress: String
  ): Promise<EpiRegistrationMeta> => {
    const request = new edge.v1.EpiRequest({
      serial_number: serialNumber as string,
      mac_address: macAddress as string,
    });
    const response = await this.makeRequest('PortRegStatus', request);

    if (!response) {
      throw new Error('No response received from PortRegStatus request');
    }

    // Convert protobuf response to plain JavaScript object
    return {
      wanInfo: response.wanInfo
        ? {
            placeHolder: response.wanInfo.place_holder,
            ip: response.wanInfo.ip,
            subnet: response.wanInfo.subnet,
            gateway: response.wanInfo.gateway,
            dns: response.wanInfo.dns,
          }
        : undefined,
      sp1ServiceStatus: response.sp1ServiceStatus
        ? {
            status: response.sp1ServiceStatus.status,
            callState: response.sp1ServiceStatus.call_state,
          }
        : undefined,
      sp2ServiceStatus: response.sp2ServiceStatus
        ? {
            status: response.sp2ServiceStatus.status,
            callState: response.sp2ServiceStatus.call_state,
          }
        : undefined,
      obiTalkServiceStatus: response.obiTalkServiceStatus
        ? {
            placeHolder: response.obiTalkServiceStatus.place_holder,
            status: response.obiTalkServiceStatus.status,
          }
        : undefined,
    };
  };

  getLivePortPhysicalStatus = async (
    serialNumber: String,
    macAddress: String,
    port: String
  ): Promise<PortPhysicalMeta[]> => {
    const request = new edge.v1.EpiRequest({
      serial_number: serialNumber as string,
      mac_address: macAddress as string,
      port: port as string,
    });
    const response = await this.makeRequest('PortPhysicalStatus', request);

    if (!response) {
      throw new Error('No response received from PortStatus request');
    }

    if (!response.portStatus || !Array.isArray(response.portStatus)) {
      throw new Error('Invalid response format: portStatus array missing');
    }

    // Map each portStatus object to PortPhysicalMeta
    const portPhysicalMetas: PortPhysicalMeta[] = response.portStatus.map(
      (portStatus: edge.v1.PortStatus) => {
        return {
          name: portStatus.name,
          state: portStatus.state,
          loopCurrent: portStatus.loopCurrent,
          Vbat: portStatus.vbat,
          tipRingVoltage: portStatus.tipRingVoltage,
          lastCallerInfo: portStatus.lastCallerInfo,
        };
      }
    );
    return portPhysicalMetas;
  };

  getLiveEpis = async (serialNumber: String): Promise<EpiEntry> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('LiveEpis', request);

    console.log('live Epis response', response, new Date().toISOString());
    if (!response) {
      throw new Error('No response received from LiveEpis request');
    }

    // The response object may contain a property 'u' which is an array of [ip, mac] pairs.
    // We'll convert this array into an object mapping ip -> mac, as expected by EpiEntry.
    let data: Record<string, string> = {};
    if (Array.isArray((response as any).u)) {
      (response as any).u.forEach((entry: any) => {
        // Each entry should be a Map or an object with ip:mac pairs
        if (entry && typeof entry === 'object') {
          // If entry is a Map, convert to object
          if (entry instanceof Map) {
            entry.forEach((mac: string, ip: string) => {
              data[ip] = mac;
            });
          } else {
            // If entry is a plain object, copy its properties
            Object.entries(entry).forEach(([ip, mac]) => {
              data[ip] = mac as string;
            });
          }
        }
      });
    }




    const epiData = { data }
    console.log("Final epiData:", epiData);
    return epiData;
  };

  async executeSshCommand(
    serialNumber: string,
    command: string,
    options?: {
      timeoutSeconds?: number;
      requiresSudo?: boolean;
      workingDirectory?: string;
      environment?: Record<string, string>;
      parameters?: Record<string, string>;
    }
  ): Promise<any> {
    // ): Promise<SshCommandResponse> {
    const environmentMap = new Map<string, string>();
    if (options?.environment) {
      Object.entries(options.environment).forEach(([key, value]) => {
        environmentMap.set(key, value);
      });
    }

    const parametersMap = new Map<string, string>();
    if (options?.parameters) {
      Object.entries(options.parameters).forEach(([key, value]) => {
        parametersMap.set(key, value);
      });
    }

    const request = new edge.v1.SshCommandRequest({
      serial_number: serialNumber,
      command,
    });

    logger.info({ serialNumber, command }, 'Executing SSH command');
    return this.makeRequest('ExecuteSshCommand', request);
  }


  async executeSshCommandAsync(
    serialNumber: string,
    command: string,
    options?: {
      timeoutSeconds?: number;
      requiresSudo?: boolean;
      workingDirectory?: string;
      environment?: Record<string, string>;
      parameters?: Record<string, string>;
    }
    // ): Promise<AsyncResponse> {
  ): Promise<void> {
    const environmentMap = new Map<string, string>();
    if (options?.environment) {
      Object.entries(options.environment).forEach(([key, value]) => {
        environmentMap.set(key, value);
      });
    }

    const parametersMap = new Map<string, string>();
    if (options?.parameters) {
      Object.entries(options.parameters).forEach(([key, value]) => {
        parametersMap.set(key, value);
      });
    }

    // const request = new edge.v1.SshCommandRequest({
    //   serial_number: serialNumber,
    //   command,
    //   timeout_seconds: options?.timeoutSeconds || 30,
    //   requires_sudo: options?.requiresSudo || false,
    //   working_directory: options?.workingDirectory || '',
    //   environment: environmentMap,
    //   parameters: parametersMap
    // });

    // logger.info({ serialNumber, command }, 'Executing SSH command asynchronously');
    // return this.makeRequest('ExecuteSshCommandAsync', request);
  }

  getLiveWifiStatus = async (serialNumber: String): Promise<WifiStatus | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('WifiStatus', request);
    if (!response) return null;
    return {
      Error_Msg: response.error_msg,
      Gateway: response.gateway,
      IP: response.ip,
      Mode: response.mode,
      Password: response.password,
      SSID: response.SSID,
      Sec_Mode: response.sec_mode,
      Status: response.status,
      Subnet: response.subnet,
    };
  };

  getLiveNetworkInfo = async (serialNumber: String): Promise<NetworkInfo | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    console.log("getLiveNetworkInfo call");

    const response = await this.makeRequest('NetworkInfo', request);

    if (!response) return null;

    const interfaces = response.interfaces ?? [];
    // The proto field is "interfaces", which is a repeated NetworkInterfaceObj.
    // The GraphQL expects an array of objects with keys: interface, internet, icmp, wg.
    // The proto field names are: interface, internet, icmp, wg.
    // The response may have interfaces as an array of objects, but field names may be snake_case or camelCase.
    // Let's map each interface object to the expected shape.

    const mappedInterfaces = Array.isArray(interfaces)
      ? interfaces.map((iface: any) => ({
        interface: iface.interface ?? iface['interface'] ?? null,
        internet: iface.internet ?? iface['internet'] ?? null,
        icmp: iface.icmp ?? iface['icmp'] ?? null,
        wg: iface.wg ?? iface['wg'] ?? null,
      }))
      : [];

    return {
      dns:response.dns,
      timestamp:response.timestamp,
      interfaces: mappedInterfaces,
      error:response.error
    };
  };

  getLiveDeviceNightlyUpdateTime = async (serialNumber: String): Promise<Date | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });

    const response = await this.makeRequest('DeviceNightlyUpdateTime', request);

    if (!response?.device_update_time) return null;

    // The proto field is "interfaces", which is a repeated NetworkInterfaceObj.
    // The GraphQL expects an array of objects with keys: interface, internet, icmp, wg.
    // The proto field names are: interface, internet, icmp, wg.
    // The response may have interfaces as an array of objects, but field names may be snake_case or camelCase.
    // Let's map each interface object to the expected shape.
    const date = new Date(response.device_update_time);
    return date;
  };


  getLivePortForwardList = async (serialNumber: String): Promise<PortForwardObj[] | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });

    const response = await this.makeRequest('PortForwardList', request);
    if (!response) return null;

    const portsList = response?.portForwardList ?? [];
    const mappedPortForwardList = Array.isArray(portsList)
      ? portsList.map((port: any) => ({
        srcIP: port.srcIP ?? null,
        srcStartPort: port['srcStartPort'] ?? 0,
        srcEndPort: port['srcEndPort'] ?? 0,
        dstIP: port['dstIP'] ?? null,
        dstStartPort: port['dstStartPort'] ?? 0,
        dstEndPort: port['dstEndPort'] ?? 0,
        proto: port['proto'] ?? null,
      }))
      : [];

    return mappedPortForwardList;
  };

  getLivePriorityInterface = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('PriorityInterface', request);
    if (!response) return null;
    return response.priorityInterface;
  };

  getLivePrimarySim = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('PrimarySim', request);
    if (!response) return null;
    return response.primarySim;
  };

  getLiveCurrentApn = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('CurrentApn', request);
    if (!response) return null;
    return response.apn;
  };

  getLiveEpikUpdateStatus = async (serialNumber: String): Promise<Boolean | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('EpikUpdateStatus', request);
    if (!response) return null;
    return response.status;
  };

  getLiveSystemInfo = async (serialNumber: String): Promise<SystemInfoDocument | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('SystemInfo', request);
    if (!response || !response.sysInfo) return null;
    logger.info('getLiveSystemInfo response');

    // Parse the response.sysInfo (which is a map of string to stringified JSON or string values)
    const sysInfoRaw = response.sysInfo as any;
    const systemInfo: any = {};

    for (const [key, value] of sysInfoRaw.entries()) {
      try {
        // Check if value is parseable, then parse
        let parsed;
        if (typeof value === 'string') {
          try {
            parsed = JSON.parse(value);
            // Handle case mismatch for networkInfoDetail
            if (key === 'NetworkInfoDetail') {
              systemInfo['networkInfoDetail'] = parsed;
            } else {
              systemInfo[key] = parsed;
            }
          } catch {
            // Handle case mismatch for networkInfoDetail
            if (key === 'NetworkInfoDetail') {
              systemInfo['networkInfoDetail'] = value;
            } else {
              systemInfo[key] = value;
            }
          }
        } else {
          // Handle case mismatch for networkInfoDetail
          if (key === 'NetworkInfoDetail') {
            systemInfo['networkInfoDetail'] = value;
          } else {
            systemInfo[key] = value;
          }
        }
      } catch (e) {
        // If it fails, just assign the raw string
        logger.error(`systemInfo key with raw string ${key} = ${value}`);
        // Handle case mismatch for networkInfoDetail
        if (key === 'NetworkInfoDetail') {
          systemInfo['networkInfoDetail'] = value;
        } else {
          systemInfo[key] = value;
        }
      }
    }
    // console.log('systemInfo response', systemInfo);
    return systemInfo as SystemInfoDocument;
  };

  getLiveDCConnectionStats = async (serialNumber: String): Promise<DcConnectionStatsResponse | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('DCConnectionStats', request);

    if (!response || !response.dc_connection_stats) return null;

    const dcConnStatsRaw = response.dc_connection_stats as any;
    const dcConnStats: any = {};

    for (const [key, value] of dcConnStatsRaw.entries()) {
      if (key === "Timestamp") {
        dcConnStats[key] = value;
      } else {
        try {
          dcConnStats[key] = JSON.parse(value);
        } catch {
          dcConnStats[key] = value;
        }
      }
    }
    console.log('getLiveDCConnectionStats response', dcConnStats);
    return dcConnStats as DcConnectionStatsResponse;
  };

  getLiveDnsCheck = async (serialNumber: String): Promise<Boolean | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('DnsCheck', request);

    if (!response || !response.dns) return null;

    return response.dns;
  };

  getLiveVSwitchTab = async (serialNumber: String): Promise<VSwitchTab | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('VSwitchTab', request);
    let portsInfo = undefined;
    if (response && Array.isArray(response.portsInfo)) {
      portsInfo = response.portsInfo.map((port: any) => ({
        port: port.port,
        calledId: port.calledId,
        recording: port.recording,
        trunkType: port.trunkType,
      }));
    }
    if (!response) return null;

    return {
      registered: response.registered,
      registerationConfigCreated: response.registerationConfigCreated,
      portsConfigCreated: response.portsConfigCreated,
      portsInfo: portsInfo,
    };
  };

  getLiveLteAnalyzer = async (serialNumber: String): Promise<Boolean | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('LteAnalyzer', request);

    if (!response) return null;

    return response.Busy;
  };

  getLiveSpeedTest = async (serialNumber: String, payload: any): Promise<SpeedTest | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
      payload: JSON.stringify(payload),
    });
    const response = await this.makeRequest('SpeedTest', request);

    if (!response) return null;

    return {
      downloadSpeed: response.downloadSpeed,
      uploadSpeed: response.uploadSpeed,
      latency: response.latency,
      jitter: response.jitter,
    };
  };

  getLiveFetchLtePerf = async (serialNumber: String): Promise<FetchLtePerf | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    console.log('getLiveFetchLtePerf response before');

    const response = await this.makeRequest('FetchLtePerf', request);
    console.log('getLiveFetchLtePerf response after');

    if (!response) return null;
    const simsInfo = response.SimsInfo ?? [];
    const mappedSimsInfo = Array.isArray(simsInfo)
      ? simsInfo.map((sim: any): SimPingInfo => ({
        Error: sim.Error,
        Jitter: sim.Jitter,
        PacketLoss: sim.PacketLoss,
        PingAvg: sim.PingAvg,
        SIM: sim.SIM,
      }))
      : [];
    console.log('getLiveFetchLtePerf response', mappedSimsInfo, response.TimeStamp);

    if (mappedSimsInfo.length === 0) {
      return null;
    }
    return {
      SimsInfo: mappedSimsInfo,
      TimeStamp: response.TimeStamp,
    };
  };

  initLtePerf = async (serialNumber: String): Promise<String | null> => {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('InitLtePerf', request);
    if (!response) return null;
    return response.initiated;
  };

  EnableDisableAPRTest = async (serialNumber: String, payload: any): Promise<Boolean> => {
    const request = new edge.v1.DeviceRequest({
      payload: payload as string,
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('EnableDisableAPRTest', request);
    if (!response || typeof (response as any).IsEnabled !== 'boolean') return false;
    return (response as any).IsEnabled as boolean;
  };

  PortConfigValues = async (serialNumber: String, macAddress: String, port: string): Promise<any | null> => {
    console.log('PortConfigValues request', serialNumber, macAddress, port);
    const request = new edge.v1.EpiRequest({
      port: port as string,
      // mac_address: macAddress as string,
      mac_address: "9CADEF46C885" as string,
      serial_number: serialNumber as string,
    });
    const response = await this.makeRequest('PortConfigValues', request);
    if (!response) return null;
    const responseObj = {
      OnhookVolts: response.OnhookVolts,
      OffhookCurrent: response.OffhookCurrent,
      DtmfDetectLength: response.DtmfDetectLength,
      DtmfDetectGap: response.DtmfDetectGap,
      TxGain: response.TxGain,
      RxGain: response.RxGain,
      DtmfMethod: response.DtmfMethod,
      DtmfPlaybackLevel: response.DtmfPlaybackLevel,
      RingVoltage: response.RingVoltage,
      DigitMapShortTimer: response.DigitMapShortTimer,
      CpcDuration: response.CpcDuration,
      CpcDelayTime: response.CpcDelayTime,
      JitterBufferType: response.JitterBufferType,
      JitterBufferMinDeley: response.JitterBufferMinDeley,
      JitterBufferMaxDeley: response.JitterBufferMaxDeley,
      T38Enabled: response.T38Enabled,
      ModemMode: response.ModemMode,
      VadEnable: response.VadEnable,
      ThreeWayCalling: response.ThreeWayCalling,
    };
    console.log("portconfigvalues response", responseObj);

    return responseObj;

  };
}
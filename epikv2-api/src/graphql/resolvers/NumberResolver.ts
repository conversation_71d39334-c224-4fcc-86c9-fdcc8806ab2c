import { NumberDocumentPopulated } from '@/models';
import { AlarmRelayDocument } from '@/models/alarmRelay';
import type { GraphqlContext } from '@/types';
import {
  Features,
  ListNumberInput,
  ListNumbersPaginatedResult,
  ListNumbersPaginatedResultType,
  PaginationInput,
  ValidateOperations,
} from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { Arg, Authorized, Ctx, FieldResolver, Query, Resolver, Root } from 'type-graphql';
const logger = createModuleLogger('NumberResolver');

@Resolver(NumberDocumentPopulated)
export class NumberResolver {

  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => ListNumbersPaginatedResult)
  async ListNumbers(
    @Arg('filter', () => ListNumberInput, { nullable: true }) filter: ListNumberInput,
    @Arg('pagination', () => PaginationInput, { nullable: true }) pagination: PaginationInput,
    @Ctx() { request, fastify }: GraphqlContext
  ): Promise<ListNumbersPaginatedResultType> {
    const allowed = await fastify.listAllowedCompanies(request);
    filter.isAll = allowed.isAll;
    filter.ids = allowed.ids;
    return fastify.services.numberService.listNumbers(filter, pagination);
  }

  @FieldResolver(() => AlarmRelayDocument, { nullable: true })
  async alarmRelayInfoDoc(
    @Root() number: NumberDocumentPopulated,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<AlarmRelayDocument | null> {
    if (!number.alarmRelayInfo) return null;
     return await  fastify.services.numberService.findAlarmRelayById(number.alarmRelayInfo.toString())
  }
}

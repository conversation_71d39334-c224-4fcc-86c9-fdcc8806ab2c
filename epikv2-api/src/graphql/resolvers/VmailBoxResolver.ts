import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Ctx } from 'type-graphql';
import { NumberDocument } from '@/models';
import type { GraphqlContext } from '@/types';
import { VmailBoxDocument } from '@/models/VmailBox';

@Resolver(() => VmailBoxDocument)
export class VmailBoxResolver {
  @FieldResolver(() => NumberDocument, { nullable: true })
  async numberDoc(
    @Root() vmailBox: VmailBoxDocument,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<NumberDocument | null> {
    if (!vmailBox.number) return null;
    return fastify.services.numberService.findNumberById(vmailBox.number.toString());
  }
}

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ol<PERSON>, <PERSON>, Ctx } from 'type-graphql';
import { PhoneDocument, NumberDocument } from '@/models';
import type { GraphqlContext } from '@/types';

@Resolver(() => PhoneDocument)
export class PhoneResolver {
  @FieldResolver(() => NumberDocument, { nullable: true })
  async numberDoc(
    @Root() phone: PhoneDocument,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<NumberDocument | null> {
    if (!phone.number) return null;
    return fastify.services.numberService.findNumberById(phone.number.toString());
  }
}

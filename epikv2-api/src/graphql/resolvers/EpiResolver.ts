import {
  EpiDocument,
  EpiDocumentBase,
  EpiDocumentPopulated,
  EpiPort,
  EpiPortPopulated,
  NumberDocumentPopulated,
  PortConfigTemplateDocument,
  EpiRegistrationMeta,
  LiveFieldUpdate,
  PortPhysicalMeta
} from '@/models';
import type { GraphqlContext } from '@/types';
import {
  EpiFilterInput,
  Features,
  ListEpiPaginatedResult,
  ListEpiPaginatedResultType,
  PaginationInput,
  ValidateOperations,
} from '@/types';
import { Arg, Authorized, Ctx, FieldResolver, Query, Resolver, Root, Subscription, ID } from 'type-graphql';
import { BaseLiveResolver, LiveField } from '@/utils/liveGraphqlUpdate';
import { createModuleLogger } from '@/utils/logger';

const logger = createModuleLogger('EpiResolver');


@Resolver(() => EpiDocumentPopulated)
export class EpiResolver extends BaseLiveResolver {
  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => ListEpiPaginatedResult)
  async ListEpi(
    @Arg('filter', () => EpiFilterInput, { nullable: true }) filter: EpiFilterInput,
    @Arg('pagination', () => PaginationInput, { nullable: true }) pagination: PaginationInput,
    @Ctx() { request, fastify }: GraphqlContext
  ): Promise<ListEpiPaginatedResultType> {
    const allowed = await fastify.listAllowedCompanies(request);
    filter.isAll = allowed.isAll;
    filter.ids = allowed.ids;
    return fastify.services.epiService.listEpi(filter, pagination);
  }

  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => EpiDocumentPopulated)
  async EpiDetailById(
    @Arg('id', () => String, { nullable: true }) id: String,
    @Ctx() { fastify, request }: GraphqlContext
  ): Promise<EpiDocumentPopulated> {
    const epi = await fastify.services.epiService.getEpiById(id);
    if (!epi) {
      throw new Error('Epi not found');
    }
    return epi;
  }

  @FieldResolver(() => EpiRegistrationMeta, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePortRegStatus',
    cacheField: 'registrationMeta',
    updateMethod: 'epiService.updateEpi',
  })
  async liveRegistrationMeta(
    @Root() epi: EpiDocument,
    @Ctx() context: GraphqlContext
  ): Promise<EpiRegistrationMeta | null> {
    if (!epi.assignedTo) {
      throw new Error('Epi not assigned to Epikbox');
    }
    const box = await context.fastify.services.epikBoxService.getEpikBoxById(epi.assignedTo.toString())
    if (!box) {
      throw new Error('EpikBox not found');
    }
    const payload = { _id: epi._id, macAddress: epi.macAddress, serialNumber: box.serialNumber }
    return this.handleLiveField(payload, context, 'liveRegistrationMeta');
  }

  @FieldResolver(() => [PortPhysicalMeta], { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePortPhysicalStatus',
    cacheField: 'portPhysicalMeta',
    updateMethod: 'epiService.updateEpi',
  })
  async livePortPhysicalMeta(
    @Root() epi: EpiDocument,
    @Ctx() context: GraphqlContext
  ): Promise<[PortPhysicalMeta] | null> {
    if (!epi.assignedTo) {
      throw new Error('Epi not assigned to Epikbox');
    }
    const box = await context.fastify.services.epikBoxService.getEpikBoxById(epi.assignedTo.toString())
    if (!box) {
      throw new Error('EpikBox not found');
    }
    const payload = { _id: epi._id, serialNumber: box.serialNumber, macAddress: epi.macAddress};
    return this.handleLiveField(payload, context, 'livePortPhysicalMeta');
  }

  @Subscription(() => LiveFieldUpdate, {
    topics: ({ args }) => {
      const topic = `FIELD_UPDATE_${args.id}`;
      logger.info({ topic }, 'EpiUpdateHook');
      return [topic];
    },
  })
  EpiUpdateHook(
    @Arg('id', () => ID) id: string,
    @Arg('fields', () => [String], { nullable: true }) fields: string[],
    @Root() payload: LiveFieldUpdate | null
  ): LiveFieldUpdate | null {
    logger.info('EpiUpdateHook');
    logger.debug({ id, fields, payload }, 'EpiUpdateHook');

    if (
      fields &&
      (fields[0] === '*' || (payload?.fieldName && fields.includes(payload.fieldName)))
    ) {
      return payload;
    }
    return null;
  }
}

@Resolver(EpiPortPopulated)
export class EpiPortPopulatedResolver extends BaseLiveResolver {
  @FieldResolver(() => NumberDocumentPopulated, { nullable: true })
  async assignedNumberDoc(
    @Root() port: EpiPortPopulated,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<NumberDocumentPopulated | null> {
    console.log({ port: port.assignedNumberRef });
    if (!port.assignedNumberRef) return null;
    return await fastify.services.numberService.findNumberById(port.assignedNumberRef.toString());
  }

  @FieldResolver(() => PortConfigTemplateDocument, { nullable: true })
  async appliedConfigTemplateDoc(
    @Root() port: EpiPortPopulated,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<PortConfigTemplateDocument | null> {
    console.log({ port: port.appliedConfigTemplate });
    if (!port.appliedConfigTemplate) return null;
    return await fastify.services.epiService.findPortConfigTemplateById(port.appliedConfigTemplate.toString());
  }
}

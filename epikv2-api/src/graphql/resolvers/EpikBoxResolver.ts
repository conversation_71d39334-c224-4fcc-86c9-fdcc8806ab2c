import { ForbiddenError } from '@/middleware/errorHandler';
import {
  CompanyDocument,
  CompanyModel,
  EpiDocument,
  EpiDocumentPopulated,
  LocationDocument,
  LocationModel,
  NumberDocument,
  PhoneDocument,
  EpiEntry
} from '@/models';
import {
  EdgeDeviceDetails,
  EpikBoxDocument,
  EpikBoxDocumentPopulated,
  EpikBoxFilterInput,
  LiveFieldUpdate,
  ModemInfoDocument,
  WifiStatus,
  NetworkInfo,
  PortForwardObj,
  SensorData,
  DcConnectionStatsResponse,
  VSwitchTab,
  FetchLtePerf,
} from '@/models/epikbox';
import { ListEpikBoxPaginatedResultType, PingLine } from '@/types';
import type { GraphqlContext } from '@/types/fastify';
import {
  Features,
  ListEpikBoxPaginatedResult,
  PaginationInput,
  ValidateOperations,
} from '@/types/index';
import { BaseLiveResolver, LiveField } from '@/utils/liveGraphqlUpdate';
import { createModuleLogger } from '@/utils/logger';
import type { FastifyInstance } from 'fastify';
import {
  Arg,
  Authorized,
  Ctx,
  FieldResolver,
  Query,
  Resolver,
  Root,
  Subscription
} from 'type-graphql';
import { SystemInfoDocument, DcAvgPingDocument } from '@/models/systemInfo';
const logger = createModuleLogger('EpikBoxResolver');

@Resolver(EpikBoxDocumentPopulated)
export class EpikBoxResolver {
  private companyModel: CompanyModel;
  private locationModel: LocationModel;
  private epiService;
  private epikBoxService;

  constructor(fastify: FastifyInstance) {
    this.companyModel = new CompanyModel(fastify);
    this.locationModel = new LocationModel(fastify);
    this.epiService = fastify.services.epiService;
    this.epikBoxService = fastify.services.epikBoxService;
  }

  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => EdgeDeviceDetails)
  async EpikBoxById(
    @Arg('id', () => String, { nullable: true }) id: String,
    @Ctx() { fastify, request }: GraphqlContext
  ): Promise<EdgeDeviceDetails> {
    logger.info('EpikBoxById');
    logger.debug({ id }, 'EpikBoxById');
    const epikBox = await this.epikBoxService.getEpikBoxById(id);
    if (!epikBox) {
      throw new Error('EpikBox not found');
    }
    const allowed = await fastify.requireCompanyAccess(request, epikBox.assignedTo?.toString());
    if (!allowed) {
      throw new ForbiddenError('You are not allowed to access this EpikBox');
    }
    return epikBox;
  }

  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => ListEpikBoxPaginatedResult)
  async ListEpikBoxes(
    @Arg('filter', () => EpikBoxFilterInput, { nullable: true }) filter: EpikBoxFilterInput,
    @Arg('pagination', () => PaginationInput, { nullable: true }) pagination: PaginationInput,
    @Ctx() { fastify, request, reply }: GraphqlContext
  ): Promise<ListEpikBoxPaginatedResultType> {
    logger.info('ListEpikBoxes');
    logger.debug({ filter, pagination }, 'ListEpikBoxes');
    const allowed = await fastify.listAllowedCompanies(request);
    filter.isAll = allowed.isAll;
    filter.ids = allowed.ids;
    return this.epikBoxService.listEpikBoxes(filter, pagination);
  }

  @FieldResolver(() => CompanyDocument, { nullable: true })
  async companyDoc(@Root() box: EpikBoxDocumentPopulated): Promise<CompanyDocument | null> {
    logger.info('companyDoc');
    logger.debug({ box }, 'ListEpikBoxes');
    if (!box.assignedTo) return null;
    return await this.companyModel.findById(box.assignedTo.toString());
  }

  @FieldResolver(() => LocationDocument, { nullable: true })
  async locationDoc(@Root() box: EpikBoxDocumentPopulated): Promise<LocationDocument | null> {
    logger.info('locationDoc');
    logger.debug({ box }, 'ListEpikBoxes');
    if (!box.locRef) return null;
    return await this.locationModel.findById(box.locRef.toString());
  }

  @FieldResolver(() => [EpiDocumentPopulated], { nullable: true })
  async obiDocs(@Root() box: EpikBoxDocumentPopulated): Promise<EpiDocumentPopulated[]> {
    logger.info('obiDocs');
    logger.debug({ box }, 'ListEpikBoxes');
    if (!box.Obis || box.Obis.length === 0) return [];
    return this.epiService.listEpiByIds(box.Obis);
  }

  @FieldResolver(() => [PhoneDocument], { nullable: true })
  async phoneDocs(@Root() box: EpikBoxDocumentPopulated): Promise<PhoneDocument[]> {
    logger.info('phoneDocs');
    logger.debug({ box }, 'ListEpikBoxes');
    if (!box.phones || box.phones.length === 0) return [];
    return this.epikBoxService.listPhonesByIds(box.phones);
  }
  @FieldResolver(() => NumberDocument, { nullable: true })
  async numberDoc(
    @Root() phone: PhoneDocument,
    @Ctx() { fastify }: GraphqlContext
  ): Promise<NumberDocument | null> {
    logger.info('numberDoc');
    logger.debug({ phone }, 'PhoneResolver');
    if (!phone.number) return null;
    return await fastify.services.numberService.findNumberById(phone.number.toString());
  }
}

@Resolver(() => EdgeDeviceDetails)
export class EdgeDeviceDetailsResolver extends BaseLiveResolver {
  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePowerState',
    cacheField: 'powerState',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePowerState(
    @Root() box: EpikBoxDocument, // or EdgeDeviceDetails
    @Ctx() context: GraphqlContext
  ): Promise<string> {
    return this.handleLiveField(box, context, 'livePowerState');
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveSignalStrength',
    cacheField: 'signalStrength',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveSignalStrength(
    @Root() box: EpikBoxDocument, // or EdgeDeviceDetails
    @Ctx() context: GraphqlContext
  ): Promise<string> {
    return this.handleLiveField(box, context, 'liveSignalStrength');
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveActiveInterface',
    cacheField: 'activeInterface',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveActiveInterface(
    @Root() box: EpikBoxDocument, // or EdgeDeviceDetails
    @Ctx() context: GraphqlContext
  ): Promise<string> {
    return this.handleLiveField(box, context, 'liveActiveInterface');
  }

  @FieldResolver(() => SensorData, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveSensorData',
    cacheField: 'sensorData',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveSensorData(
    @Root() box: EpikBoxDocument, // or EdgeDeviceDetails
    @Ctx() context: GraphqlContext
  ): Promise<SensorData> {
    return this.handleLiveField(box, context, 'liveSensorData');
  }

  @FieldResolver(() => ModemInfoDocument, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveModemInfo',
    cacheField: 'modemInfo',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveModemInfo(
    @Root() box: EpikBoxDocument, // or EdgeDeviceDetails
    @Ctx() context: GraphqlContext
  ): Promise<ModemInfoDocument> {
    return this.handleLiveField(box, context, 'liveModemInfo');
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveLanIp',
    cacheField: 'lanIp',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveLanIp(@Root() box: EpikBoxDocument, @Ctx() context: GraphqlContext): Promise<string> {
    logger.info('liveLanIp resolver called');

    // Check if customerProvidedIp exists first
    if (box.customerProvidedIp && box.customerProvidedIp.trim() !== '') {
      logger.info('liveLanIp: returning customerProvidedIp');
      return box.customerProvidedIp;
    }

    logger.info('liveLanIp: fetching from epikBoxService');
    const result = await this.handleLiveField(box, context, 'liveLanIp');
    logger.debug({ result }, 'liveLanIp handleLiveField result');

    return result || 'IP not available';
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePublicIp',
    cacheField: 'publicIp',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePublicIp(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<string> {
    return this.handleLiveField(box, context, 'livePublicIp');
  }

  @FieldResolver(() => Boolean, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveDeviceOnline',
    cacheField: 'deviceOnline',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveDeviceOnline(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<boolean> {
    return this.handleLiveField(box, context, 'liveDeviceOnline');
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveSimStatus',
    cacheField: 'simStatus',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveSimStatus(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<string> {
    return this.handleLiveField(box, context, 'liveSimStatus');
  }

  @FieldResolver(() => DcAvgPingDocument, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveDcAvgPing',
    cacheField: 'dcAvgPing',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveDcAvgPing(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<DcAvgPingDocument> {
    return this.handleLiveField(box, context, 'liveDcAvgPing')
  }

  @FieldResolver(() => Boolean, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveRegStatus',
    cacheField: 'registered',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveRegistered(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<boolean> {
    return this.handleLiveField(box, context, 'liveRegistered')
  }


  @FieldResolver(() => EpiEntry, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveEpis',
    cacheField: 'liveEpis',
    // updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveEpis(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<EpiEntry> {
    try {
      const response = await context.fastify.services.edgeProxyService.getLiveEpis(box.serialNumber);

      // Ensure we always return a valid EpiEntry object with non-null data
      if (response && response.data) {
        return response;
      }
      return { data: {} };
    } catch (error) {
      console.error("live Epis response:", error);
      console.error("Error fetching live epis:", error);
      return { data: {} };
    }
  }

  @FieldResolver(() => WifiStatus, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveWifiStatus',
    cacheField: 'wifiStatus',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveWifiStatus(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<WifiStatus> {
    return this.handleLiveField(box, context, 'liveWifiStatus')
  }


  @FieldResolver(() => NetworkInfo, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveNetworkInfo',
    cacheField: 'networkInfo',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveNetworkInfo(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<NetworkInfo> {
    return this.handleLiveField(box, context, 'liveNetworkInfo')
  }

  @FieldResolver(() => Date, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveDeviceNightlyUpdateTime',
    cacheField: 'nightlyUpdateTime',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveNightlyUpdateTime(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<Date> {
    return this.handleLiveField(box, context, 'liveNightlyUpdateTime')
  }

  @FieldResolver(() => [PortForwardObj], { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePortForwardList',
    cacheField: 'portForwardList',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePortForwardList(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<PortForwardObj[]> {
    return this.handleLiveField(box, context, 'livePortForwardList')
  }

  // todo need to test which fields to be update due to change in db save value and response sent back to UI
  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePriorityInterface',
    cacheField: 'priorityInterface',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePriorityInterface(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<String> {
    return this.handleLiveField(box, context, 'livePriorityInterface')
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLivePrimarySim',
    cacheField: 'primarySim',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePrimarySim(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<String> {
    return this.handleLiveField(box, context, 'livePrimarySim')
  }

  @FieldResolver(() => String, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveCurrentApn',
    cacheField: 'currentApn',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveCurrentApn(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<String> {
    return this.handleLiveField(box, context, 'liveCurrentApn')
  }


  @FieldResolver(() => Boolean, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveEpikUpdateStatus',
    cacheField: 'epikUpdateStatus',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveEpikUpdateStatus(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<Boolean> {
    return this.handleLiveField(box, context, 'liveEpikUpdateStatus')
  }


  @FieldResolver(() => SystemInfoDocument, { nullable: true })
  async sysInfo(@Root() box: EpikBoxDocument, @Ctx() context: GraphqlContext): Promise<SystemInfoDocument | null> {
    logger.info('sysInfo field resolver for EdgeDeviceDetails');
    // Get the systeminfo from the database
    const systemInfo = await context.fastify.services.epikBoxService.getSystemInfoByBoxId(box._id?.toString() || '');
    // console.log('sysInfo field resolver for EdgeDeviceDetails', systemInfo);
    return systemInfo;
  }

  @FieldResolver(() => SystemInfoDocument, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveSystemInfo',
    cacheField: 'sysInfo',
    updateMethod: 'epikBoxService.updateSystemInfoByBoxId',
  })
  async liveSysInfo(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<SystemInfoDocument> {
    return this.handleLiveField(box, context, 'liveSysInfo')
  }


  @FieldResolver(() => DcConnectionStatsResponse, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveDCConnectionStats',
    cacheField: 'dcConnectionStats',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveDcConnectionStats(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<DcConnectionStatsResponse> {
    return this.handleLiveField(box, context, 'liveDcConnectionStats')
  }

  @FieldResolver(() => Boolean, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveDnsCheck',
    cacheField: 'dnsCheck',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveDnsCheck(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<Boolean> {
    return this.handleLiveField(box, context, 'liveDnsCheck')
  }

  @FieldResolver(() => VSwitchTab, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveVSwitchTab',
    cacheField: 'vSwitchTab',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async liveVSwitchTab(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<VSwitchTab> {
    return this.handleLiveField(box, context, 'liveVSwitchTab')
  }

  @FieldResolver(() => FetchLtePerf, { nullable: true })
  @LiveField({
    remoteServiceMethod: 'edgeProxyService.getLiveFetchLtePerf',
    cacheField: 'preferredProviderTest',
    updateMethod: 'epikBoxService.updateEpikBox',
  })
  async livePreferredProviderTest(
    @Root() box: EpikBoxDocument,
    @Ctx() context: GraphqlContext
  ): Promise<FetchLtePerf> {
    return this.handleLiveField(box, context, 'livePreferredProviderTest')
  }

  @Subscription(() => LiveFieldUpdate, {
    topics: ({ args }) => {
      const topic = `FIELD_UPDATE_${args.id}`;
      logger.info({ topic }, typeof args.id);
      return [topic];
    },
  })
  EpikBoxUpdateHook(
    @Arg('id', () => String) id: string,
    @Arg('fields', () => [String], { nullable: true }) fields: string[],
    @Root() payload: LiveFieldUpdate | null
  ): LiveFieldUpdate | null {
    logger.info('EpikBoxUpdateHook');
    logger.debug({ id, fields, payload }, 'EpikBoxUpdateHook');
    if (
      fields &&
      (fields[0] === '*' || (payload?.fieldName && fields.includes(payload.fieldName)))
    ) {
      return payload;
    }
    return null;
  }

  @Subscription(() => PingLine, {
    subscribe: async ({
      args,
      context,
    }: {
      args: { serialNumber: string };
      context: GraphqlContext;
    }) => {
      const pubSub = context.pubsub;
      console.log({services:context})
      try {
        logger.info('PingBoxHook');
        const topic = `PING_${args.serialNumber}`;
        logger.debug({ topic }, 'PingBoxHook');
        // context.fastify.services.edgeProxyService.pingBox(
        //   args.serialNumber,
        //   (data: string) => {
        //     logger.debug({ data }, 'PingBoxHook');
        //     pubSub.publish(topic, { line: data, completed: false });
        //   },
        //   () => {
        //     logger.debug('PingBoxHook completed');
        //     pubSub.publish(topic, { line: null, completed: true });
        //   }
        // );
        return pubSub.subscribe(topic);
      } catch (error) {
        console.log(error)
        return pubSub.subscribe('topic');
      }
    },
  })
  async PingBoxHook(
    @Arg('serialNumber', () => String) serialNumber: string,
    @Root() payload: PingLine
  ): Promise<PingLine> {
    return payload;
  }
}

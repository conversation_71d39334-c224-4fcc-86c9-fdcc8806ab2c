import { config } from '@/config/environment';
import { UnauthorizedError } from '@/middleware/errorHandler';
import { custom<PERSON>uth<PERSON><PERSON><PERSON> } from '@/middleware/graphql';
import type { GraphqlContext } from '@/types';
import { ObjectIdScalar } from '@/types';
import { extractCookieValue } from '@/utils/helpers';
import { TypeGraphQLPubSub } from '@/utils/pubsub';
import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import mercurius from 'mercurius';
import { ObjectId } from 'mongodb';
import 'reflect-metadata';
import { buildSchema, GraphQLTimestamp } from 'type-graphql';
import { GraphQLJSONObject, GraphQLJSON } from 'graphql-scalars';
import { EdgeDeviceDetailsResolver, EpikBoxResolver } from './resolvers/EpikBoxResolver';
import { EpiPortPopulatedResolver, EpiResolver } from './resolvers/EpiResolver';
import { NumberResolver } from './resolvers/NumberResolver';
import { PhoneResolver } from './resolvers/PhoneResolver';
import { VmailBoxResolver } from './resolvers/VmailBoxResolver';

export const graphqlPlugin = fp(async function (fastify: FastifyInstance) {
  // Build the schema using type-graphql
  const pubsub = new TypeGraphQLPubSub();
  const schema = await buildSchema({
    resolvers: [
      NumberResolver,
      EpikBoxResolver,
      EpiResolver,
      EdgeDeviceDetailsResolver,
      PhoneResolver,
      EpiPortPopulatedResolver,
      VmailBoxResolver,
    ],
    emitSchemaFile: config.devMode ? 'schema.graphql' : false,
    validate: true, // Enable validation with class-validator
    authChecker: customAuthChecker,
    authMode: 'error',
    scalarsMap: [
      { type: ObjectId, scalar: ObjectIdScalar },
      { type: Date, scalar: GraphQLTimestamp },
    ],
    container: {
      get: resolverClass => {
        return new resolverClass(fastify);
      },
    },
    pubSub: pubsub,
  });
  await fastify.register(mercurius, {
    schema,
    onlyPersisted: false,
    subscription: {
      pubsub: pubsub,
      onConnect: async context => {
        console.log('onConnect', context);
        return true;
      },
      onDisconnect: connection => {
        console.log('onDisconnect');
        // Example: Clean up resources related to the disconnected connection
      },
      verifyClient: async (info, socket) => {
        // console.log('verifyClient');
        socket(true);
        return true;
      },
      context: (connectionParams, request): GraphqlContext => {
        const cookieHeader = request.headers?.cookie || '';
        const token = extractCookieValue(cookieHeader, 'v2-jwt');
        console.log({ token: connectionParams });
        // if (!token) {
        //   throw new UnauthorizedError('Unauthorized');
        // }
        // const user = fastify.jwt.verify(token) as any;
        return {
          reply: {} as any,
          request,
          fastify,
          user: {} as any,
          pubsub: pubsub,
        };
      },
    },
    graphiql: config.devMode, // Enable GraphiQL UI for development
    path: '/query',
    context: (request, reply): GraphqlContext => {
      const cookieHeader = request.headers?.cookie || '';
      const token = extractCookieValue(cookieHeader, 'v2-jwt');
      if (!token) {
        throw new UnauthorizedError('Unauthorized');
      }
      const user = fastify.jwt.verify(token) as any;
      request.user = user;
      return {
        reply,
        request,
        fastify,
        user,
        pubsub: pubsub,
      };
    },
  });
});

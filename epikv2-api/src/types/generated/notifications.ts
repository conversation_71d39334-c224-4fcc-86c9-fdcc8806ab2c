/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.21.12
 * source: notifications.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
import * as grpc_1 from "@grpc/grpc-js";
export namespace edge.v1 {
    export class DeviceRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            payload?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("payload" in data && data.payload != undefined) {
                    this.payload = data.payload;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get payload() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set payload(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            serial_number?: string;
            payload?: string;
        }): DeviceRequest {
            const message = new DeviceRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.payload != null) {
                message.payload = data.payload;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                payload?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.payload != null) {
                data.payload = this.payload;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.payload.length)
                writer.writeString(2, this.payload);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.payload = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceRequest {
            return DeviceRequest.deserialize(bytes);
        }
    }
    export class EnableDisablePortRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: number;
            enable?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
                if ("enable" in data && data.enable != undefined) {
                    this.enable = data.enable;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set port(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get enable() {
            return pb_1.Message.getFieldWithDefault(this, 3, false) as boolean;
        }
        set enable(value: boolean) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: number;
            enable?: boolean;
        }): EnableDisablePortRequest {
            const message = new EnableDisablePortRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            if (data.enable != null) {
                message.enable = data.enable;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: number;
                enable?: boolean;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            if (this.enable != null) {
                data.enable = this.enable;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port != 0)
                writer.writeInt32(2, this.port);
            if (this.enable != false)
                writer.writeBool(3, this.enable);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EnableDisablePortRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EnableDisablePortRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readInt32();
                        break;
                    case 3:
                        message.enable = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EnableDisablePortRequest {
            return EnableDisablePortRequest.deserialize(bytes);
        }
    }
    export class EnableDisablePortResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            success?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("success" in data && data.success != undefined) {
                    this.success = data.success;
                }
            }
        }
        get success() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set success(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            success?: boolean;
        }): EnableDisablePortResponse {
            const message = new EnableDisablePortResponse({});
            if (data.success != null) {
                message.success = data.success;
            }
            return message;
        }
        toObject() {
            const data: {
                success?: boolean;
            } = {};
            if (this.success != null) {
                data.success = this.success;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.success != false)
                writer.writeBool(1, this.success);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EnableDisablePortResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EnableDisablePortResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.success = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EnableDisablePortResponse {
            return EnableDisablePortResponse.deserialize(bytes);
        }
    }
    export class EpiRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            mac_address?: string;
            serial_number?: string;
            port?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("mac_address" in data && data.mac_address != undefined) {
                    this.mac_address = data.mac_address;
                }
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
            }
        }
        get mac_address() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set mac_address(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            mac_address?: string;
            serial_number?: string;
            port?: string;
        }): EpiRequest {
            const message = new EpiRequest({});
            if (data.mac_address != null) {
                message.mac_address = data.mac_address;
            }
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            return message;
        }
        toObject() {
            const data: {
                mac_address?: string;
                serial_number?: string;
                port?: string;
            } = {};
            if (this.mac_address != null) {
                data.mac_address = this.mac_address;
            }
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.mac_address.length)
                writer.writeString(1, this.mac_address);
            if (this.serial_number.length)
                writer.writeString(2, this.serial_number);
            if (this.port.length)
                writer.writeString(3, this.port);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EpiRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EpiRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.mac_address = reader.readString();
                        break;
                    case 2:
                        message.serial_number = reader.readString();
                        break;
                    case 3:
                        message.port = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EpiRequest {
            return EpiRequest.deserialize(bytes);
        }
    }
    export class PowerSourceResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            power_source?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("power_source" in data && data.power_source != undefined) {
                    this.power_source = data.power_source;
                }
            }
        }
        get power_source() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set power_source(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            power_source?: string;
        }): PowerSourceResponse {
            const message = new PowerSourceResponse({});
            if (data.power_source != null) {
                message.power_source = data.power_source;
            }
            return message;
        }
        toObject() {
            const data: {
                power_source?: string;
            } = {};
            if (this.power_source != null) {
                data.power_source = this.power_source;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.power_source.length)
                writer.writeString(1, this.power_source);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PowerSourceResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PowerSourceResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.power_source = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PowerSourceResponse {
            return PowerSourceResponse.deserialize(bytes);
        }
    }
    export class ActiveInterfaceResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            active_interface?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("active_interface" in data && data.active_interface != undefined) {
                    this.active_interface = data.active_interface;
                }
            }
        }
        get active_interface() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set active_interface(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            active_interface?: string;
        }): ActiveInterfaceResponse {
            const message = new ActiveInterfaceResponse({});
            if (data.active_interface != null) {
                message.active_interface = data.active_interface;
            }
            return message;
        }
        toObject() {
            const data: {
                active_interface?: string;
            } = {};
            if (this.active_interface != null) {
                data.active_interface = this.active_interface;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.active_interface.length)
                writer.writeString(1, this.active_interface);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ActiveInterfaceResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ActiveInterfaceResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.active_interface = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): ActiveInterfaceResponse {
            return ActiveInterfaceResponse.deserialize(bytes);
        }
    }
    export class LanIpResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            lan_ip?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("lan_ip" in data && data.lan_ip != undefined) {
                    this.lan_ip = data.lan_ip;
                }
            }
        }
        get lan_ip() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set lan_ip(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            lan_ip?: string;
        }): LanIpResponse {
            const message = new LanIpResponse({});
            if (data.lan_ip != null) {
                message.lan_ip = data.lan_ip;
            }
            return message;
        }
        toObject() {
            const data: {
                lan_ip?: string;
            } = {};
            if (this.lan_ip != null) {
                data.lan_ip = this.lan_ip;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.lan_ip.length)
                writer.writeString(1, this.lan_ip);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): LanIpResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new LanIpResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.lan_ip = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): LanIpResponse {
            return LanIpResponse.deserialize(bytes);
        }
    }
    export class PublicIpResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            public_ip?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("public_ip" in data && data.public_ip != undefined) {
                    this.public_ip = data.public_ip;
                }
            }
        }
        get public_ip() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set public_ip(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            public_ip?: string;
        }): PublicIpResponse {
            const message = new PublicIpResponse({});
            if (data.public_ip != null) {
                message.public_ip = data.public_ip;
            }
            return message;
        }
        toObject() {
            const data: {
                public_ip?: string;
            } = {};
            if (this.public_ip != null) {
                data.public_ip = this.public_ip;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.public_ip.length)
                writer.writeString(1, this.public_ip);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PublicIpResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PublicIpResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.public_ip = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PublicIpResponse {
            return PublicIpResponse.deserialize(bytes);
        }
    }
    export class SignalStrengthResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            signal_strength?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("signal_strength" in data && data.signal_strength != undefined) {
                    this.signal_strength = data.signal_strength;
                }
            }
        }
        get signal_strength() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set signal_strength(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            signal_strength?: string;
        }): SignalStrengthResponse {
            const message = new SignalStrengthResponse({});
            if (data.signal_strength != null) {
                message.signal_strength = data.signal_strength;
            }
            return message;
        }
        toObject() {
            const data: {
                signal_strength?: string;
            } = {};
            if (this.signal_strength != null) {
                data.signal_strength = this.signal_strength;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.signal_strength.length)
                writer.writeString(1, this.signal_strength);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SignalStrengthResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SignalStrengthResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.signal_strength = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SignalStrengthResponse {
            return SignalStrengthResponse.deserialize(bytes);
        }
    }
    export class SimStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            sim_status?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("sim_status" in data && data.sim_status != undefined) {
                    this.sim_status = data.sim_status;
                }
            }
        }
        get sim_status() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set sim_status(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            sim_status?: string;
        }): SimStatusResponse {
            const message = new SimStatusResponse({});
            if (data.sim_status != null) {
                message.sim_status = data.sim_status;
            }
            return message;
        }
        toObject() {
            const data: {
                sim_status?: string;
            } = {};
            if (this.sim_status != null) {
                data.sim_status = this.sim_status;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.sim_status.length)
                writer.writeString(1, this.sim_status);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SimStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SimStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.sim_status = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SimStatusResponse {
            return SimStatusResponse.deserialize(bytes);
        }
    }
    export class ModemInfo extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            manufacturer_placeholder?: string;
            manufacturer?: string;
            model_placeholder?: string;
            model?: string;
            sim_placeholder?: string;
            sim?: string;
            imei_placeholder?: string;
            imei?: string;
            carrier_placeholder?: string;
            carrier?: string;
            ipAddress_placeholder?: string;
            ipAddress?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("manufacturer_placeholder" in data && data.manufacturer_placeholder != undefined) {
                    this.manufacturer_placeholder = data.manufacturer_placeholder;
                }
                if ("manufacturer" in data && data.manufacturer != undefined) {
                    this.manufacturer = data.manufacturer;
                }
                if ("model_placeholder" in data && data.model_placeholder != undefined) {
                    this.model_placeholder = data.model_placeholder;
                }
                if ("model" in data && data.model != undefined) {
                    this.model = data.model;
                }
                if ("sim_placeholder" in data && data.sim_placeholder != undefined) {
                    this.sim_placeholder = data.sim_placeholder;
                }
                if ("sim" in data && data.sim != undefined) {
                    this.sim = data.sim;
                }
                if ("imei_placeholder" in data && data.imei_placeholder != undefined) {
                    this.imei_placeholder = data.imei_placeholder;
                }
                if ("imei" in data && data.imei != undefined) {
                    this.imei = data.imei;
                }
                if ("carrier_placeholder" in data && data.carrier_placeholder != undefined) {
                    this.carrier_placeholder = data.carrier_placeholder;
                }
                if ("carrier" in data && data.carrier != undefined) {
                    this.carrier = data.carrier;
                }
                if ("ipAddress_placeholder" in data && data.ipAddress_placeholder != undefined) {
                    this.ipAddress_placeholder = data.ipAddress_placeholder;
                }
                if ("ipAddress" in data && data.ipAddress != undefined) {
                    this.ipAddress = data.ipAddress;
                }
            }
        }
        get manufacturer_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set manufacturer_placeholder(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get manufacturer() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set manufacturer(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get model_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set model_placeholder(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get model() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set model(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get sim_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set sim_placeholder(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get sim() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set sim(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get imei_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set imei_placeholder(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get imei() {
            return pb_1.Message.getFieldWithDefault(this, 8, "") as string;
        }
        set imei(value: string) {
            pb_1.Message.setField(this, 8, value);
        }
        get carrier_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 9, "") as string;
        }
        set carrier_placeholder(value: string) {
            pb_1.Message.setField(this, 9, value);
        }
        get carrier() {
            return pb_1.Message.getFieldWithDefault(this, 10, "") as string;
        }
        set carrier(value: string) {
            pb_1.Message.setField(this, 10, value);
        }
        get ipAddress_placeholder() {
            return pb_1.Message.getFieldWithDefault(this, 11, "") as string;
        }
        set ipAddress_placeholder(value: string) {
            pb_1.Message.setField(this, 11, value);
        }
        get ipAddress() {
            return pb_1.Message.getFieldWithDefault(this, 12, "") as string;
        }
        set ipAddress(value: string) {
            pb_1.Message.setField(this, 12, value);
        }
        static fromObject(data: {
            manufacturer_placeholder?: string;
            manufacturer?: string;
            model_placeholder?: string;
            model?: string;
            sim_placeholder?: string;
            sim?: string;
            imei_placeholder?: string;
            imei?: string;
            carrier_placeholder?: string;
            carrier?: string;
            ipAddress_placeholder?: string;
            ipAddress?: string;
        }): ModemInfo {
            const message = new ModemInfo({});
            if (data.manufacturer_placeholder != null) {
                message.manufacturer_placeholder = data.manufacturer_placeholder;
            }
            if (data.manufacturer != null) {
                message.manufacturer = data.manufacturer;
            }
            if (data.model_placeholder != null) {
                message.model_placeholder = data.model_placeholder;
            }
            if (data.model != null) {
                message.model = data.model;
            }
            if (data.sim_placeholder != null) {
                message.sim_placeholder = data.sim_placeholder;
            }
            if (data.sim != null) {
                message.sim = data.sim;
            }
            if (data.imei_placeholder != null) {
                message.imei_placeholder = data.imei_placeholder;
            }
            if (data.imei != null) {
                message.imei = data.imei;
            }
            if (data.carrier_placeholder != null) {
                message.carrier_placeholder = data.carrier_placeholder;
            }
            if (data.carrier != null) {
                message.carrier = data.carrier;
            }
            if (data.ipAddress_placeholder != null) {
                message.ipAddress_placeholder = data.ipAddress_placeholder;
            }
            if (data.ipAddress != null) {
                message.ipAddress = data.ipAddress;
            }
            return message;
        }
        toObject() {
            const data: {
                manufacturer_placeholder?: string;
                manufacturer?: string;
                model_placeholder?: string;
                model?: string;
                sim_placeholder?: string;
                sim?: string;
                imei_placeholder?: string;
                imei?: string;
                carrier_placeholder?: string;
                carrier?: string;
                ipAddress_placeholder?: string;
                ipAddress?: string;
            } = {};
            if (this.manufacturer_placeholder != null) {
                data.manufacturer_placeholder = this.manufacturer_placeholder;
            }
            if (this.manufacturer != null) {
                data.manufacturer = this.manufacturer;
            }
            if (this.model_placeholder != null) {
                data.model_placeholder = this.model_placeholder;
            }
            if (this.model != null) {
                data.model = this.model;
            }
            if (this.sim_placeholder != null) {
                data.sim_placeholder = this.sim_placeholder;
            }
            if (this.sim != null) {
                data.sim = this.sim;
            }
            if (this.imei_placeholder != null) {
                data.imei_placeholder = this.imei_placeholder;
            }
            if (this.imei != null) {
                data.imei = this.imei;
            }
            if (this.carrier_placeholder != null) {
                data.carrier_placeholder = this.carrier_placeholder;
            }
            if (this.carrier != null) {
                data.carrier = this.carrier;
            }
            if (this.ipAddress_placeholder != null) {
                data.ipAddress_placeholder = this.ipAddress_placeholder;
            }
            if (this.ipAddress != null) {
                data.ipAddress = this.ipAddress;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.manufacturer_placeholder.length)
                writer.writeString(1, this.manufacturer_placeholder);
            if (this.manufacturer.length)
                writer.writeString(2, this.manufacturer);
            if (this.model_placeholder.length)
                writer.writeString(3, this.model_placeholder);
            if (this.model.length)
                writer.writeString(4, this.model);
            if (this.sim_placeholder.length)
                writer.writeString(5, this.sim_placeholder);
            if (this.sim.length)
                writer.writeString(6, this.sim);
            if (this.imei_placeholder.length)
                writer.writeString(7, this.imei_placeholder);
            if (this.imei.length)
                writer.writeString(8, this.imei);
            if (this.carrier_placeholder.length)
                writer.writeString(9, this.carrier_placeholder);
            if (this.carrier.length)
                writer.writeString(10, this.carrier);
            if (this.ipAddress_placeholder.length)
                writer.writeString(11, this.ipAddress_placeholder);
            if (this.ipAddress.length)
                writer.writeString(12, this.ipAddress);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ModemInfo {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ModemInfo();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.manufacturer_placeholder = reader.readString();
                        break;
                    case 2:
                        message.manufacturer = reader.readString();
                        break;
                    case 3:
                        message.model_placeholder = reader.readString();
                        break;
                    case 4:
                        message.model = reader.readString();
                        break;
                    case 5:
                        message.sim_placeholder = reader.readString();
                        break;
                    case 6:
                        message.sim = reader.readString();
                        break;
                    case 7:
                        message.imei_placeholder = reader.readString();
                        break;
                    case 8:
                        message.imei = reader.readString();
                        break;
                    case 9:
                        message.carrier_placeholder = reader.readString();
                        break;
                    case 10:
                        message.carrier = reader.readString();
                        break;
                    case 11:
                        message.ipAddress_placeholder = reader.readString();
                        break;
                    case 12:
                        message.ipAddress = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): ModemInfo {
            return ModemInfo.deserialize(bytes);
        }
    }
    export class ModemInfoResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            modem_info?: ModemInfo;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("modem_info" in data && data.modem_info != undefined) {
                    this.modem_info = data.modem_info;
                }
            }
        }
        get modem_info() {
            return pb_1.Message.getWrapperField(this, ModemInfo, 1) as ModemInfo;
        }
        set modem_info(value: ModemInfo) {
            pb_1.Message.setWrapperField(this, 1, value);
        }
        get has_modem_info() {
            return pb_1.Message.getField(this, 1) != null;
        }
        static fromObject(data: {
            modem_info?: ReturnType<typeof ModemInfo.prototype.toObject>;
        }): ModemInfoResponse {
            const message = new ModemInfoResponse({});
            if (data.modem_info != null) {
                message.modem_info = ModemInfo.fromObject(data.modem_info);
            }
            return message;
        }
        toObject() {
            const data: {
                modem_info?: ReturnType<typeof ModemInfo.prototype.toObject>;
            } = {};
            if (this.modem_info != null) {
                data.modem_info = this.modem_info.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_modem_info)
                writer.writeMessage(1, this.modem_info, () => this.modem_info.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ModemInfoResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ModemInfoResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.modem_info, () => message.modem_info = ModemInfo.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): ModemInfoResponse {
            return ModemInfoResponse.deserialize(bytes);
        }
    }
    export class SensorDataResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            power?: string;
            temp?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("power" in data && data.power != undefined) {
                    this.power = data.power;
                }
                if ("temp" in data && data.temp != undefined) {
                    this.temp = data.temp;
                }
            }
        }
        get power() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set power(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get temp() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set temp(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            power?: string;
            temp?: string;
        }): SensorDataResponse {
            const message = new SensorDataResponse({});
            if (data.power != null) {
                message.power = data.power;
            }
            if (data.temp != null) {
                message.temp = data.temp;
            }
            return message;
        }
        toObject() {
            const data: {
                power?: string;
                temp?: string;
            } = {};
            if (this.power != null) {
                data.power = this.power;
            }
            if (this.temp != null) {
                data.temp = this.temp;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.power.length)
                writer.writeString(1, this.power);
            if (this.temp.length)
                writer.writeString(2, this.temp);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SensorDataResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SensorDataResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.power = reader.readString();
                        break;
                    case 2:
                        message.temp = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SensorDataResponse {
            return SensorDataResponse.deserialize(bytes);
        }
    }
    export class DeviceOnlineResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            online?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("online" in data && data.online != undefined) {
                    this.online = data.online;
                }
            }
        }
        get online() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set online(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            online?: boolean;
        }): DeviceOnlineResponse {
            const message = new DeviceOnlineResponse({});
            if (data.online != null) {
                message.online = data.online;
            }
            return message;
        }
        toObject() {
            const data: {
                online?: boolean;
            } = {};
            if (this.online != null) {
                data.online = this.online;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.online != false)
                writer.writeBool(1, this.online);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceOnlineResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceOnlineResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.online = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceOnlineResponse {
            return DeviceOnlineResponse.deserialize(bytes);
        }
    }
    export class DeviceResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            body?: Uint8Array;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("body" in data && data.body != undefined) {
                    this.body = data.body;
                }
            }
        }
        get body() {
            return pb_1.Message.getFieldWithDefault(this, 1, new Uint8Array(0)) as Uint8Array;
        }
        set body(value: Uint8Array) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            body?: Uint8Array;
        }): DeviceResponse {
            const message = new DeviceResponse({});
            if (data.body != null) {
                message.body = data.body;
            }
            return message;
        }
        toObject() {
            const data: {
                body?: Uint8Array;
            } = {};
            if (this.body != null) {
                data.body = this.body;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.body.length)
                writer.writeBytes(1, this.body);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.body = reader.readBytes();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceResponse {
            return DeviceResponse.deserialize(bytes);
        }
    }
    export class AsyncRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            path?: string;
            priority?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("priority" in data && data.priority != undefined) {
                    this.priority = data.priority;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get priority() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set priority(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            path?: string;
            priority?: number;
        }): AsyncRequest {
            const message = new AsyncRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.priority != null) {
                message.priority = data.priority;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                path?: string;
                priority?: number;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.priority != null) {
                data.priority = this.priority;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.path.length)
                writer.writeString(2, this.path);
            if (this.priority != 0)
                writer.writeInt32(3, this.priority);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AsyncRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AsyncRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.path = reader.readString();
                        break;
                    case 3:
                        message.priority = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AsyncRequest {
            return AsyncRequest.deserialize(bytes);
        }
    }
    export class AsyncResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            status?: string;
            estimated_completion_seconds?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("estimated_completion_seconds" in data && data.estimated_completion_seconds != undefined) {
                    this.estimated_completion_seconds = data.estimated_completion_seconds;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get estimated_completion_seconds() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set estimated_completion_seconds(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            request_id?: string;
            status?: string;
            estimated_completion_seconds?: number;
        }): AsyncResponse {
            const message = new AsyncResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.estimated_completion_seconds != null) {
                message.estimated_completion_seconds = data.estimated_completion_seconds;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                status?: string;
                estimated_completion_seconds?: number;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.estimated_completion_seconds != null) {
                data.estimated_completion_seconds = this.estimated_completion_seconds;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (this.estimated_completion_seconds != 0)
                writer.writeInt64(3, this.estimated_completion_seconds);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AsyncResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AsyncResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    case 3:
                        message.estimated_completion_seconds = reader.readInt64();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AsyncResponse {
            return AsyncResponse.deserialize(bytes);
        }
    }
    export class StatusRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            request_id?: string;
        }): StatusRequest {
            const message = new StatusRequest({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): StatusRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new StatusRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): StatusRequest {
            return StatusRequest.deserialize(bytes);
        }
    }
    export class StatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            status?: string;
            status_code?: number;
            body?: Uint8Array;
            error?: string;
            created_at?: number;
            completed_at?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("status_code" in data && data.status_code != undefined) {
                    this.status_code = data.status_code;
                }
                if ("body" in data && data.body != undefined) {
                    this.body = data.body;
                }
                if ("error" in data && data.error != undefined) {
                    this.error = data.error;
                }
                if ("created_at" in data && data.created_at != undefined) {
                    this.created_at = data.created_at;
                }
                if ("completed_at" in data && data.completed_at != undefined) {
                    this.completed_at = data.completed_at;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get status_code() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set status_code(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get body() {
            return pb_1.Message.getFieldWithDefault(this, 4, new Uint8Array(0)) as Uint8Array;
        }
        set body(value: Uint8Array) {
            pb_1.Message.setField(this, 4, value);
        }
        get error() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set error(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get created_at() {
            return pb_1.Message.getFieldWithDefault(this, 7, 0) as number;
        }
        set created_at(value: number) {
            pb_1.Message.setField(this, 7, value);
        }
        get completed_at() {
            return pb_1.Message.getFieldWithDefault(this, 8, 0) as number;
        }
        set completed_at(value: number) {
            pb_1.Message.setField(this, 8, value);
        }
        static fromObject(data: {
            request_id?: string;
            status?: string;
            status_code?: number;
            body?: Uint8Array;
            error?: string;
            created_at?: number;
            completed_at?: number;
        }): StatusResponse {
            const message = new StatusResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.status_code != null) {
                message.status_code = data.status_code;
            }
            if (data.body != null) {
                message.body = data.body;
            }
            if (data.error != null) {
                message.error = data.error;
            }
            if (data.created_at != null) {
                message.created_at = data.created_at;
            }
            if (data.completed_at != null) {
                message.completed_at = data.completed_at;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                status?: string;
                status_code?: number;
                body?: Uint8Array;
                error?: string;
                created_at?: number;
                completed_at?: number;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.status_code != null) {
                data.status_code = this.status_code;
            }
            if (this.body != null) {
                data.body = this.body;
            }
            if (this.error != null) {
                data.error = this.error;
            }
            if (this.created_at != null) {
                data.created_at = this.created_at;
            }
            if (this.completed_at != null) {
                data.completed_at = this.completed_at;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (this.status_code != 0)
                writer.writeInt32(3, this.status_code);
            if (this.body.length)
                writer.writeBytes(4, this.body);
            if (this.error.length)
                writer.writeString(6, this.error);
            if (this.created_at != 0)
                writer.writeInt64(7, this.created_at);
            if (this.completed_at != 0)
                writer.writeInt64(8, this.completed_at);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): StatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new StatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    case 3:
                        message.status_code = reader.readInt32();
                        break;
                    case 4:
                        message.body = reader.readBytes();
                        break;
                    case 6:
                        message.error = reader.readString();
                        break;
                    case 7:
                        message.created_at = reader.readInt64();
                        break;
                    case 8:
                        message.completed_at = reader.readInt64();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): StatusResponse {
            return StatusResponse.deserialize(bytes);
        }
    }
    export class CancelRequestMessage extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            request_id?: string;
        }): CancelRequestMessage {
            const message = new CancelRequestMessage({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CancelRequestMessage {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CancelRequestMessage();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CancelRequestMessage {
            return CancelRequestMessage.deserialize(bytes);
        }
    }
    export class CancelResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            cancelled?: boolean;
            message?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("cancelled" in data && data.cancelled != undefined) {
                    this.cancelled = data.cancelled;
                }
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get cancelled() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set cancelled(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        get message() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set message(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            request_id?: string;
            cancelled?: boolean;
            message?: string;
        }): CancelResponse {
            const message = new CancelResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.cancelled != null) {
                message.cancelled = data.cancelled;
            }
            if (data.message != null) {
                message.message = data.message;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                cancelled?: boolean;
                message?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.cancelled != null) {
                data.cancelled = this.cancelled;
            }
            if (this.message != null) {
                data.message = this.message;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.cancelled != false)
                writer.writeBool(2, this.cancelled);
            if (this.message.length)
                writer.writeString(3, this.message);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CancelResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CancelResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.cancelled = reader.readBool();
                        break;
                    case 3:
                        message.message = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CancelResponse {
            return CancelResponse.deserialize(bytes);
        }
    }
    export class DashboardEndpoint extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            path?: string;
            identifier?: string;
            timeout_seconds?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("identifier" in data && data.identifier != undefined) {
                    this.identifier = data.identifier;
                }
                if ("timeout_seconds" in data && data.timeout_seconds != undefined) {
                    this.timeout_seconds = data.timeout_seconds;
                }
            }
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get identifier() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set identifier(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get timeout_seconds() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set timeout_seconds(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            path?: string;
            identifier?: string;
            timeout_seconds?: number;
        }): DashboardEndpoint {
            const message = new DashboardEndpoint({});
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.identifier != null) {
                message.identifier = data.identifier;
            }
            if (data.timeout_seconds != null) {
                message.timeout_seconds = data.timeout_seconds;
            }
            return message;
        }
        toObject() {
            const data: {
                path?: string;
                identifier?: string;
                timeout_seconds?: number;
            } = {};
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.identifier != null) {
                data.identifier = this.identifier;
            }
            if (this.timeout_seconds != null) {
                data.timeout_seconds = this.timeout_seconds;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.path.length)
                writer.writeString(1, this.path);
            if (this.identifier.length)
                writer.writeString(2, this.identifier);
            if (this.timeout_seconds != 0)
                writer.writeInt32(3, this.timeout_seconds);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DashboardEndpoint {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DashboardEndpoint();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.path = reader.readString();
                        break;
                    case 2:
                        message.identifier = reader.readString();
                        break;
                    case 3:
                        message.timeout_seconds = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DashboardEndpoint {
            return DashboardEndpoint.deserialize(bytes);
        }
    }
    export class DashboardRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            serial_number?: string;
        }): DashboardRequest {
            const message = new DashboardRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DashboardRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DashboardRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DashboardRequest {
            return DashboardRequest.deserialize(bytes);
        }
    }
    export class DashboardEndpointResult extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            identifier?: string;
            path?: string;
            status_code?: number;
            body?: Uint8Array;
            error?: string;
            duration_ms?: number;
            success?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("identifier" in data && data.identifier != undefined) {
                    this.identifier = data.identifier;
                }
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("status_code" in data && data.status_code != undefined) {
                    this.status_code = data.status_code;
                }
                if ("body" in data && data.body != undefined) {
                    this.body = data.body;
                }
                if ("error" in data && data.error != undefined) {
                    this.error = data.error;
                }
                if ("duration_ms" in data && data.duration_ms != undefined) {
                    this.duration_ms = data.duration_ms;
                }
                if ("success" in data && data.success != undefined) {
                    this.success = data.success;
                }
            }
        }
        get identifier() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set identifier(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get status_code() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set status_code(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get body() {
            return pb_1.Message.getFieldWithDefault(this, 4, new Uint8Array(0)) as Uint8Array;
        }
        set body(value: Uint8Array) {
            pb_1.Message.setField(this, 4, value);
        }
        get error() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set error(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get duration_ms() {
            return pb_1.Message.getFieldWithDefault(this, 7, 0) as number;
        }
        set duration_ms(value: number) {
            pb_1.Message.setField(this, 7, value);
        }
        get success() {
            return pb_1.Message.getFieldWithDefault(this, 8, false) as boolean;
        }
        set success(value: boolean) {
            pb_1.Message.setField(this, 8, value);
        }
        static fromObject(data: {
            identifier?: string;
            path?: string;
            status_code?: number;
            body?: Uint8Array;
            error?: string;
            duration_ms?: number;
            success?: boolean;
        }): DashboardEndpointResult {
            const message = new DashboardEndpointResult({});
            if (data.identifier != null) {
                message.identifier = data.identifier;
            }
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.status_code != null) {
                message.status_code = data.status_code;
            }
            if (data.body != null) {
                message.body = data.body;
            }
            if (data.error != null) {
                message.error = data.error;
            }
            if (data.duration_ms != null) {
                message.duration_ms = data.duration_ms;
            }
            if (data.success != null) {
                message.success = data.success;
            }
            return message;
        }
        toObject() {
            const data: {
                identifier?: string;
                path?: string;
                status_code?: number;
                body?: Uint8Array;
                error?: string;
                duration_ms?: number;
                success?: boolean;
            } = {};
            if (this.identifier != null) {
                data.identifier = this.identifier;
            }
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.status_code != null) {
                data.status_code = this.status_code;
            }
            if (this.body != null) {
                data.body = this.body;
            }
            if (this.error != null) {
                data.error = this.error;
            }
            if (this.duration_ms != null) {
                data.duration_ms = this.duration_ms;
            }
            if (this.success != null) {
                data.success = this.success;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.identifier.length)
                writer.writeString(1, this.identifier);
            if (this.path.length)
                writer.writeString(2, this.path);
            if (this.status_code != 0)
                writer.writeInt32(3, this.status_code);
            if (this.body.length)
                writer.writeBytes(4, this.body);
            if (this.error.length)
                writer.writeString(6, this.error);
            if (this.duration_ms != 0)
                writer.writeInt64(7, this.duration_ms);
            if (this.success != false)
                writer.writeBool(8, this.success);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DashboardEndpointResult {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DashboardEndpointResult();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.identifier = reader.readString();
                        break;
                    case 2:
                        message.path = reader.readString();
                        break;
                    case 3:
                        message.status_code = reader.readInt32();
                        break;
                    case 4:
                        message.body = reader.readBytes();
                        break;
                    case 6:
                        message.error = reader.readString();
                        break;
                    case 7:
                        message.duration_ms = reader.readInt64();
                        break;
                    case 8:
                        message.success = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DashboardEndpointResult {
            return DashboardEndpointResult.deserialize(bytes);
        }
    }
    export class DashboardResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            results?: DashboardEndpointResult[];
            total_endpoints?: number;
            successful_endpoints?: number;
            failed_endpoints?: number;
            total_duration_ms?: number;
            overall_success?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [2], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("results" in data && data.results != undefined) {
                    this.results = data.results;
                }
                if ("total_endpoints" in data && data.total_endpoints != undefined) {
                    this.total_endpoints = data.total_endpoints;
                }
                if ("successful_endpoints" in data && data.successful_endpoints != undefined) {
                    this.successful_endpoints = data.successful_endpoints;
                }
                if ("failed_endpoints" in data && data.failed_endpoints != undefined) {
                    this.failed_endpoints = data.failed_endpoints;
                }
                if ("total_duration_ms" in data && data.total_duration_ms != undefined) {
                    this.total_duration_ms = data.total_duration_ms;
                }
                if ("overall_success" in data && data.overall_success != undefined) {
                    this.overall_success = data.overall_success;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get results() {
            return pb_1.Message.getRepeatedWrapperField(this, DashboardEndpointResult, 2) as DashboardEndpointResult[];
        }
        set results(value: DashboardEndpointResult[]) {
            pb_1.Message.setRepeatedWrapperField(this, 2, value);
        }
        get total_endpoints() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set total_endpoints(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get successful_endpoints() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set successful_endpoints(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get failed_endpoints() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set failed_endpoints(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        get total_duration_ms() {
            return pb_1.Message.getFieldWithDefault(this, 6, 0) as number;
        }
        set total_duration_ms(value: number) {
            pb_1.Message.setField(this, 6, value);
        }
        get overall_success() {
            return pb_1.Message.getFieldWithDefault(this, 7, false) as boolean;
        }
        set overall_success(value: boolean) {
            pb_1.Message.setField(this, 7, value);
        }
        static fromObject(data: {
            serial_number?: string;
            results?: ReturnType<typeof DashboardEndpointResult.prototype.toObject>[];
            total_endpoints?: number;
            successful_endpoints?: number;
            failed_endpoints?: number;
            total_duration_ms?: number;
            overall_success?: boolean;
        }): DashboardResponse {
            const message = new DashboardResponse({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.results != null) {
                message.results = data.results.map(item => DashboardEndpointResult.fromObject(item));
            }
            if (data.total_endpoints != null) {
                message.total_endpoints = data.total_endpoints;
            }
            if (data.successful_endpoints != null) {
                message.successful_endpoints = data.successful_endpoints;
            }
            if (data.failed_endpoints != null) {
                message.failed_endpoints = data.failed_endpoints;
            }
            if (data.total_duration_ms != null) {
                message.total_duration_ms = data.total_duration_ms;
            }
            if (data.overall_success != null) {
                message.overall_success = data.overall_success;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                results?: ReturnType<typeof DashboardEndpointResult.prototype.toObject>[];
                total_endpoints?: number;
                successful_endpoints?: number;
                failed_endpoints?: number;
                total_duration_ms?: number;
                overall_success?: boolean;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.results != null) {
                data.results = this.results.map((item: DashboardEndpointResult) => item.toObject());
            }
            if (this.total_endpoints != null) {
                data.total_endpoints = this.total_endpoints;
            }
            if (this.successful_endpoints != null) {
                data.successful_endpoints = this.successful_endpoints;
            }
            if (this.failed_endpoints != null) {
                data.failed_endpoints = this.failed_endpoints;
            }
            if (this.total_duration_ms != null) {
                data.total_duration_ms = this.total_duration_ms;
            }
            if (this.overall_success != null) {
                data.overall_success = this.overall_success;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.results.length)
                writer.writeRepeatedMessage(2, this.results, (item: DashboardEndpointResult) => item.serialize(writer));
            if (this.total_endpoints != 0)
                writer.writeInt32(3, this.total_endpoints);
            if (this.successful_endpoints != 0)
                writer.writeInt32(4, this.successful_endpoints);
            if (this.failed_endpoints != 0)
                writer.writeInt32(5, this.failed_endpoints);
            if (this.total_duration_ms != 0)
                writer.writeInt64(6, this.total_duration_ms);
            if (this.overall_success != false)
                writer.writeBool(7, this.overall_success);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DashboardResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DashboardResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        reader.readMessage(message.results, () => pb_1.Message.addToRepeatedWrapperField(message, 2, DashboardEndpointResult.deserialize(reader), DashboardEndpointResult));
                        break;
                    case 3:
                        message.total_endpoints = reader.readInt32();
                        break;
                    case 4:
                        message.successful_endpoints = reader.readInt32();
                        break;
                    case 5:
                        message.failed_endpoints = reader.readInt32();
                        break;
                    case 6:
                        message.total_duration_ms = reader.readInt64();
                        break;
                    case 7:
                        message.overall_success = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DashboardResponse {
            return DashboardResponse.deserialize(bytes);
        }
    }
    export class EchoRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            message?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
            }
        }
        get message() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set message(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            message?: string;
        }): EchoRequest {
            const message = new EchoRequest({});
            if (data.message != null) {
                message.message = data.message;
            }
            return message;
        }
        toObject() {
            const data: {
                message?: string;
            } = {};
            if (this.message != null) {
                data.message = this.message;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.message.length)
                writer.writeString(1, this.message);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EchoRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EchoRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.message = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EchoRequest {
            return EchoRequest.deserialize(bytes);
        }
    }
    export class EchoResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            message?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
            }
        }
        get message() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set message(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            message?: string;
        }): EchoResponse {
            const message = new EchoResponse({});
            if (data.message != null) {
                message.message = data.message;
            }
            return message;
        }
        toObject() {
            const data: {
                message?: string;
            } = {};
            if (this.message != null) {
                data.message = this.message;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.message.length)
                writer.writeString(1, this.message);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EchoResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EchoResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.message = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EchoResponse {
            return EchoResponse.deserialize(bytes);
        }
    }
    export class DcAvgPingResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            atPingAvg?: string;
            bestDC?: string;
            bestLatency?: string;
            chPingAvg?: string;
            dlPingAvg?: string;
            laPingAvg?: string;
            nyPingAvg?: string;
            timeUpdate?: string;
            error?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("atPingAvg" in data && data.atPingAvg != undefined) {
                    this.atPingAvg = data.atPingAvg;
                }
                if ("bestDC" in data && data.bestDC != undefined) {
                    this.bestDC = data.bestDC;
                }
                if ("bestLatency" in data && data.bestLatency != undefined) {
                    this.bestLatency = data.bestLatency;
                }
                if ("chPingAvg" in data && data.chPingAvg != undefined) {
                    this.chPingAvg = data.chPingAvg;
                }
                if ("dlPingAvg" in data && data.dlPingAvg != undefined) {
                    this.dlPingAvg = data.dlPingAvg;
                }
                if ("laPingAvg" in data && data.laPingAvg != undefined) {
                    this.laPingAvg = data.laPingAvg;
                }
                if ("nyPingAvg" in data && data.nyPingAvg != undefined) {
                    this.nyPingAvg = data.nyPingAvg;
                }
                if ("timeUpdate" in data && data.timeUpdate != undefined) {
                    this.timeUpdate = data.timeUpdate;
                }
                if ("error" in data && data.error != undefined) {
                    this.error = data.error;
                }
            }
        }
        get atPingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set atPingAvg(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get bestDC() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set bestDC(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get bestLatency() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set bestLatency(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get chPingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set chPingAvg(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get dlPingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set dlPingAvg(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get laPingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set laPingAvg(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get nyPingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set nyPingAvg(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get timeUpdate() {
            return pb_1.Message.getFieldWithDefault(this, 8, "") as string;
        }
        set timeUpdate(value: string) {
            pb_1.Message.setField(this, 8, value);
        }
        get error() {
            return pb_1.Message.getFieldWithDefault(this, 9, "") as string;
        }
        set error(value: string) {
            pb_1.Message.setField(this, 9, value);
        }
        static fromObject(data: {
            atPingAvg?: string;
            bestDC?: string;
            bestLatency?: string;
            chPingAvg?: string;
            dlPingAvg?: string;
            laPingAvg?: string;
            nyPingAvg?: string;
            timeUpdate?: string;
            error?: string;
        }): DcAvgPingResponse {
            const message = new DcAvgPingResponse({});
            if (data.atPingAvg != null) {
                message.atPingAvg = data.atPingAvg;
            }
            if (data.bestDC != null) {
                message.bestDC = data.bestDC;
            }
            if (data.bestLatency != null) {
                message.bestLatency = data.bestLatency;
            }
            if (data.chPingAvg != null) {
                message.chPingAvg = data.chPingAvg;
            }
            if (data.dlPingAvg != null) {
                message.dlPingAvg = data.dlPingAvg;
            }
            if (data.laPingAvg != null) {
                message.laPingAvg = data.laPingAvg;
            }
            if (data.nyPingAvg != null) {
                message.nyPingAvg = data.nyPingAvg;
            }
            if (data.timeUpdate != null) {
                message.timeUpdate = data.timeUpdate;
            }
            if (data.error != null) {
                message.error = data.error;
            }
            return message;
        }
        toObject() {
            const data: {
                atPingAvg?: string;
                bestDC?: string;
                bestLatency?: string;
                chPingAvg?: string;
                dlPingAvg?: string;
                laPingAvg?: string;
                nyPingAvg?: string;
                timeUpdate?: string;
                error?: string;
            } = {};
            if (this.atPingAvg != null) {
                data.atPingAvg = this.atPingAvg;
            }
            if (this.bestDC != null) {
                data.bestDC = this.bestDC;
            }
            if (this.bestLatency != null) {
                data.bestLatency = this.bestLatency;
            }
            if (this.chPingAvg != null) {
                data.chPingAvg = this.chPingAvg;
            }
            if (this.dlPingAvg != null) {
                data.dlPingAvg = this.dlPingAvg;
            }
            if (this.laPingAvg != null) {
                data.laPingAvg = this.laPingAvg;
            }
            if (this.nyPingAvg != null) {
                data.nyPingAvg = this.nyPingAvg;
            }
            if (this.timeUpdate != null) {
                data.timeUpdate = this.timeUpdate;
            }
            if (this.error != null) {
                data.error = this.error;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.atPingAvg.length)
                writer.writeString(1, this.atPingAvg);
            if (this.bestDC.length)
                writer.writeString(2, this.bestDC);
            if (this.bestLatency.length)
                writer.writeString(3, this.bestLatency);
            if (this.chPingAvg.length)
                writer.writeString(4, this.chPingAvg);
            if (this.dlPingAvg.length)
                writer.writeString(5, this.dlPingAvg);
            if (this.laPingAvg.length)
                writer.writeString(6, this.laPingAvg);
            if (this.nyPingAvg.length)
                writer.writeString(7, this.nyPingAvg);
            if (this.timeUpdate.length)
                writer.writeString(8, this.timeUpdate);
            if (this.error.length)
                writer.writeString(9, this.error);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DcAvgPingResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DcAvgPingResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.atPingAvg = reader.readString();
                        break;
                    case 2:
                        message.bestDC = reader.readString();
                        break;
                    case 3:
                        message.bestLatency = reader.readString();
                        break;
                    case 4:
                        message.chPingAvg = reader.readString();
                        break;
                    case 5:
                        message.dlPingAvg = reader.readString();
                        break;
                    case 6:
                        message.laPingAvg = reader.readString();
                        break;
                    case 7:
                        message.nyPingAvg = reader.readString();
                        break;
                    case 8:
                        message.timeUpdate = reader.readString();
                        break;
                    case 9:
                        message.error = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DcAvgPingResponse {
            return DcAvgPingResponse.deserialize(bytes);
        }
    }
    export class PingMessage extends pb_1.Message {
        #one_of_decls: number[][] = [[1, 2]];
        constructor(data?: any[] | ({} & (({
            command?: PingCommand;
            line?: never;
        } | {
            command?: never;
            line?: PingLine;
        })))) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("command" in data && data.command != undefined) {
                    this.command = data.command;
                }
                if ("line" in data && data.line != undefined) {
                    this.line = data.line;
                }
            }
        }
        get command() {
            return pb_1.Message.getWrapperField(this, PingCommand, 1) as PingCommand;
        }
        set command(value: PingCommand) {
            pb_1.Message.setOneofWrapperField(this, 1, this.#one_of_decls[0], value);
        }
        get has_command() {
            return pb_1.Message.getField(this, 1) != null;
        }
        get line() {
            return pb_1.Message.getWrapperField(this, PingLine, 2) as PingLine;
        }
        set line(value: PingLine) {
            pb_1.Message.setOneofWrapperField(this, 2, this.#one_of_decls[0], value);
        }
        get has_line() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get payload() {
            const cases: {
                [index: number]: "none" | "command" | "line";
            } = {
                0: "none",
                1: "command",
                2: "line"
            };
            return cases[pb_1.Message.computeOneofCase(this, [1, 2])];
        }
        static fromObject(data: {
            command?: ReturnType<typeof PingCommand.prototype.toObject>;
            line?: ReturnType<typeof PingLine.prototype.toObject>;
        }): PingMessage {
            const message = new PingMessage({});
            if (data.command != null) {
                message.command = PingCommand.fromObject(data.command);
            }
            if (data.line != null) {
                message.line = PingLine.fromObject(data.line);
            }
            return message;
        }
        toObject() {
            const data: {
                command?: ReturnType<typeof PingCommand.prototype.toObject>;
                line?: ReturnType<typeof PingLine.prototype.toObject>;
            } = {};
            if (this.command != null) {
                data.command = this.command.toObject();
            }
            if (this.line != null) {
                data.line = this.line.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_command)
                writer.writeMessage(1, this.command, () => this.command.serialize(writer));
            if (this.has_line)
                writer.writeMessage(2, this.line, () => this.line.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PingMessage {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PingMessage();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.command, () => message.command = PingCommand.deserialize(reader));
                        break;
                    case 2:
                        reader.readMessage(message.line, () => message.line = PingLine.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PingMessage {
            return PingMessage.deserialize(bytes);
        }
    }
    export class PingCommand extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            stop?: boolean;
            ip?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("stop" in data && data.stop != undefined) {
                    this.stop = data.stop;
                }
                if ("ip" in data && data.ip != undefined) {
                    this.ip = data.ip;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get stop() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set stop(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        get ip() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set ip(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            stop?: boolean;
            ip?: string;
        }): PingCommand {
            const message = new PingCommand({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.stop != null) {
                message.stop = data.stop;
            }
            if (data.ip != null) {
                message.ip = data.ip;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                stop?: boolean;
                ip?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.stop != null) {
                data.stop = this.stop;
            }
            if (this.ip != null) {
                data.ip = this.ip;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.stop != false)
                writer.writeBool(2, this.stop);
            if (this.ip.length)
                writer.writeString(3, this.ip);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PingCommand {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PingCommand();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.stop = reader.readBool();
                        break;
                    case 3:
                        message.ip = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PingCommand {
            return PingCommand.deserialize(bytes);
        }
    }
    export class PingLine extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            line?: string;
            completed?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("line" in data && data.line != undefined) {
                    this.line = data.line;
                }
                if ("completed" in data && data.completed != undefined) {
                    this.completed = data.completed;
                }
            }
        }
        get line() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set line(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get completed() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set completed(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            line?: string;
            completed?: boolean;
        }): PingLine {
            const message = new PingLine({});
            if (data.line != null) {
                message.line = data.line;
            }
            if (data.completed != null) {
                message.completed = data.completed;
            }
            return message;
        }
        toObject() {
            const data: {
                line?: string;
                completed?: boolean;
            } = {};
            if (this.line != null) {
                data.line = this.line;
            }
            if (this.completed != null) {
                data.completed = this.completed;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.line.length)
                writer.writeString(1, this.line);
            if (this.completed != false)
                writer.writeBool(2, this.completed);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PingLine {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PingLine();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.line = reader.readString();
                        break;
                    case 2:
                        message.completed = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PingLine {
            return PingLine.deserialize(bytes);
        }
    }
    export class RegStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            registered?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("registered" in data && data.registered != undefined) {
                    this.registered = data.registered;
                }
            }
        }
        get registered() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set registered(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            registered?: boolean;
        }): RegStatusResponse {
            const message = new RegStatusResponse({});
            if (data.registered != null) {
                message.registered = data.registered;
            }
            return message;
        }
        toObject() {
            const data: {
                registered?: boolean;
            } = {};
            if (this.registered != null) {
                data.registered = this.registered;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.registered != false)
                writer.writeBool(1, this.registered);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): RegStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new RegStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.registered = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): RegStatusResponse {
            return RegStatusResponse.deserialize(bytes);
        }
    }
    export class WanInfo extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            place_holder?: string;
            ip?: string;
            subnet?: string;
            gateway?: string;
            dns?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("place_holder" in data && data.place_holder != undefined) {
                    this.place_holder = data.place_holder;
                }
                if ("ip" in data && data.ip != undefined) {
                    this.ip = data.ip;
                }
                if ("subnet" in data && data.subnet != undefined) {
                    this.subnet = data.subnet;
                }
                if ("gateway" in data && data.gateway != undefined) {
                    this.gateway = data.gateway;
                }
                if ("dns" in data && data.dns != undefined) {
                    this.dns = data.dns;
                }
            }
        }
        get place_holder() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set place_holder(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get ip() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set ip(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get subnet() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set subnet(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get gateway() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set gateway(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get dns() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set dns(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        static fromObject(data: {
            place_holder?: string;
            ip?: string;
            subnet?: string;
            gateway?: string;
            dns?: string;
        }): WanInfo {
            const message = new WanInfo({});
            if (data.place_holder != null) {
                message.place_holder = data.place_holder;
            }
            if (data.ip != null) {
                message.ip = data.ip;
            }
            if (data.subnet != null) {
                message.subnet = data.subnet;
            }
            if (data.gateway != null) {
                message.gateway = data.gateway;
            }
            if (data.dns != null) {
                message.dns = data.dns;
            }
            return message;
        }
        toObject() {
            const data: {
                place_holder?: string;
                ip?: string;
                subnet?: string;
                gateway?: string;
                dns?: string;
            } = {};
            if (this.place_holder != null) {
                data.place_holder = this.place_holder;
            }
            if (this.ip != null) {
                data.ip = this.ip;
            }
            if (this.subnet != null) {
                data.subnet = this.subnet;
            }
            if (this.gateway != null) {
                data.gateway = this.gateway;
            }
            if (this.dns != null) {
                data.dns = this.dns;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.place_holder.length)
                writer.writeString(1, this.place_holder);
            if (this.ip.length)
                writer.writeString(2, this.ip);
            if (this.subnet.length)
                writer.writeString(3, this.subnet);
            if (this.gateway.length)
                writer.writeString(4, this.gateway);
            if (this.dns.length)
                writer.writeString(5, this.dns);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): WanInfo {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new WanInfo();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.place_holder = reader.readString();
                        break;
                    case 2:
                        message.ip = reader.readString();
                        break;
                    case 3:
                        message.subnet = reader.readString();
                        break;
                    case 4:
                        message.gateway = reader.readString();
                        break;
                    case 5:
                        message.dns = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): WanInfo {
            return WanInfo.deserialize(bytes);
        }
    }
    export class Sp1ServiceStatus extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: string;
            call_state?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("call_state" in data && data.call_state != undefined) {
                    this.call_state = data.call_state;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get call_state() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set call_state(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: string;
            call_state?: string;
        }): Sp1ServiceStatus {
            const message = new Sp1ServiceStatus({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.call_state != null) {
                message.call_state = data.call_state;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: string;
                call_state?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.call_state != null) {
                data.call_state = this.call_state;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status.length)
                writer.writeString(1, this.status);
            if (this.call_state.length)
                writer.writeString(2, this.call_state);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Sp1ServiceStatus {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Sp1ServiceStatus();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readString();
                        break;
                    case 2:
                        message.call_state = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Sp1ServiceStatus {
            return Sp1ServiceStatus.deserialize(bytes);
        }
    }
    export class Sp2ServiceStatus extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: string;
            call_state?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("call_state" in data && data.call_state != undefined) {
                    this.call_state = data.call_state;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get call_state() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set call_state(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: string;
            call_state?: string;
        }): Sp2ServiceStatus {
            const message = new Sp2ServiceStatus({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.call_state != null) {
                message.call_state = data.call_state;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: string;
                call_state?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.call_state != null) {
                data.call_state = this.call_state;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status.length)
                writer.writeString(1, this.status);
            if (this.call_state.length)
                writer.writeString(2, this.call_state);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Sp2ServiceStatus {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new Sp2ServiceStatus();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readString();
                        break;
                    case 2:
                        message.call_state = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): Sp2ServiceStatus {
            return Sp2ServiceStatus.deserialize(bytes);
        }
    }
    export class ObiTalkServiceStatus extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            place_holder?: string;
            status?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("place_holder" in data && data.place_holder != undefined) {
                    this.place_holder = data.place_holder;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
            }
        }
        get place_holder() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set place_holder(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            place_holder?: string;
            status?: string;
        }): ObiTalkServiceStatus {
            const message = new ObiTalkServiceStatus({});
            if (data.place_holder != null) {
                message.place_holder = data.place_holder;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            return message;
        }
        toObject() {
            const data: {
                place_holder?: string;
                status?: string;
            } = {};
            if (this.place_holder != null) {
                data.place_holder = this.place_holder;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.place_holder.length)
                writer.writeString(1, this.place_holder);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ObiTalkServiceStatus {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ObiTalkServiceStatus();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.place_holder = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): ObiTalkServiceStatus {
            return ObiTalkServiceStatus.deserialize(bytes);
        }
    }
    export class PortRegStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            wanInfo?: WanInfo;
            sp1ServiceStatus?: Sp1ServiceStatus;
            sp2ServiceStatus?: Sp2ServiceStatus;
            obiTalkServiceStatus?: ObiTalkServiceStatus;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("wanInfo" in data && data.wanInfo != undefined) {
                    this.wanInfo = data.wanInfo;
                }
                if ("sp1ServiceStatus" in data && data.sp1ServiceStatus != undefined) {
                    this.sp1ServiceStatus = data.sp1ServiceStatus;
                }
                if ("sp2ServiceStatus" in data && data.sp2ServiceStatus != undefined) {
                    this.sp2ServiceStatus = data.sp2ServiceStatus;
                }
                if ("obiTalkServiceStatus" in data && data.obiTalkServiceStatus != undefined) {
                    this.obiTalkServiceStatus = data.obiTalkServiceStatus;
                }
            }
        }
        get wanInfo() {
            return pb_1.Message.getWrapperField(this, WanInfo, 1) as WanInfo;
        }
        set wanInfo(value: WanInfo) {
            pb_1.Message.setWrapperField(this, 1, value);
        }
        get has_wanInfo() {
            return pb_1.Message.getField(this, 1) != null;
        }
        get sp1ServiceStatus() {
            return pb_1.Message.getWrapperField(this, Sp1ServiceStatus, 2) as Sp1ServiceStatus;
        }
        set sp1ServiceStatus(value: Sp1ServiceStatus) {
            pb_1.Message.setWrapperField(this, 2, value);
        }
        get has_sp1ServiceStatus() {
            return pb_1.Message.getField(this, 2) != null;
        }
        get sp2ServiceStatus() {
            return pb_1.Message.getWrapperField(this, Sp2ServiceStatus, 3) as Sp2ServiceStatus;
        }
        set sp2ServiceStatus(value: Sp2ServiceStatus) {
            pb_1.Message.setWrapperField(this, 3, value);
        }
        get has_sp2ServiceStatus() {
            return pb_1.Message.getField(this, 3) != null;
        }
        get obiTalkServiceStatus() {
            return pb_1.Message.getWrapperField(this, ObiTalkServiceStatus, 4) as ObiTalkServiceStatus;
        }
        set obiTalkServiceStatus(value: ObiTalkServiceStatus) {
            pb_1.Message.setWrapperField(this, 4, value);
        }
        get has_obiTalkServiceStatus() {
            return pb_1.Message.getField(this, 4) != null;
        }
        static fromObject(data: {
            wanInfo?: ReturnType<typeof WanInfo.prototype.toObject>;
            sp1ServiceStatus?: ReturnType<typeof Sp1ServiceStatus.prototype.toObject>;
            sp2ServiceStatus?: ReturnType<typeof Sp2ServiceStatus.prototype.toObject>;
            obiTalkServiceStatus?: ReturnType<typeof ObiTalkServiceStatus.prototype.toObject>;
        }): PortRegStatusResponse {
            const message = new PortRegStatusResponse({});
            if (data.wanInfo != null) {
                message.wanInfo = WanInfo.fromObject(data.wanInfo);
            }
            if (data.sp1ServiceStatus != null) {
                message.sp1ServiceStatus = Sp1ServiceStatus.fromObject(data.sp1ServiceStatus);
            }
            if (data.sp2ServiceStatus != null) {
                message.sp2ServiceStatus = Sp2ServiceStatus.fromObject(data.sp2ServiceStatus);
            }
            if (data.obiTalkServiceStatus != null) {
                message.obiTalkServiceStatus = ObiTalkServiceStatus.fromObject(data.obiTalkServiceStatus);
            }
            return message;
        }
        toObject() {
            const data: {
                wanInfo?: ReturnType<typeof WanInfo.prototype.toObject>;
                sp1ServiceStatus?: ReturnType<typeof Sp1ServiceStatus.prototype.toObject>;
                sp2ServiceStatus?: ReturnType<typeof Sp2ServiceStatus.prototype.toObject>;
                obiTalkServiceStatus?: ReturnType<typeof ObiTalkServiceStatus.prototype.toObject>;
            } = {};
            if (this.wanInfo != null) {
                data.wanInfo = this.wanInfo.toObject();
            }
            if (this.sp1ServiceStatus != null) {
                data.sp1ServiceStatus = this.sp1ServiceStatus.toObject();
            }
            if (this.sp2ServiceStatus != null) {
                data.sp2ServiceStatus = this.sp2ServiceStatus.toObject();
            }
            if (this.obiTalkServiceStatus != null) {
                data.obiTalkServiceStatus = this.obiTalkServiceStatus.toObject();
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.has_wanInfo)
                writer.writeMessage(1, this.wanInfo, () => this.wanInfo.serialize(writer));
            if (this.has_sp1ServiceStatus)
                writer.writeMessage(2, this.sp1ServiceStatus, () => this.sp1ServiceStatus.serialize(writer));
            if (this.has_sp2ServiceStatus)
                writer.writeMessage(3, this.sp2ServiceStatus, () => this.sp2ServiceStatus.serialize(writer));
            if (this.has_obiTalkServiceStatus)
                writer.writeMessage(4, this.obiTalkServiceStatus, () => this.obiTalkServiceStatus.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortRegStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortRegStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.wanInfo, () => message.wanInfo = WanInfo.deserialize(reader));
                        break;
                    case 2:
                        reader.readMessage(message.sp1ServiceStatus, () => message.sp1ServiceStatus = Sp1ServiceStatus.deserialize(reader));
                        break;
                    case 3:
                        reader.readMessage(message.sp2ServiceStatus, () => message.sp2ServiceStatus = Sp2ServiceStatus.deserialize(reader));
                        break;
                    case 4:
                        reader.readMessage(message.obiTalkServiceStatus, () => message.obiTalkServiceStatus = ObiTalkServiceStatus.deserialize(reader));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortRegStatusResponse {
            return PortRegStatusResponse.deserialize(bytes);
        }
    }
    export class PortStatus extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            name?: string;
            state?: string;
            loopCurrent?: string;
            vbat?: string;
            tipRingVoltage?: string;
            lastCallerInfo?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("name" in data && data.name != undefined) {
                    this.name = data.name;
                }
                if ("state" in data && data.state != undefined) {
                    this.state = data.state;
                }
                if ("loopCurrent" in data && data.loopCurrent != undefined) {
                    this.loopCurrent = data.loopCurrent;
                }
                if ("vbat" in data && data.vbat != undefined) {
                    this.vbat = data.vbat;
                }
                if ("tipRingVoltage" in data && data.tipRingVoltage != undefined) {
                    this.tipRingVoltage = data.tipRingVoltage;
                }
                if ("lastCallerInfo" in data && data.lastCallerInfo != undefined) {
                    this.lastCallerInfo = data.lastCallerInfo;
                }
            }
        }
        get name() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set name(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get state() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set state(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get loopCurrent() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set loopCurrent(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get vbat() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set vbat(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get tipRingVoltage() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set tipRingVoltage(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get lastCallerInfo() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set lastCallerInfo(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        static fromObject(data: {
            name?: string;
            state?: string;
            loopCurrent?: string;
            vbat?: string;
            tipRingVoltage?: string;
            lastCallerInfo?: string;
        }): PortStatus {
            const message = new PortStatus({});
            if (data.name != null) {
                message.name = data.name;
            }
            if (data.state != null) {
                message.state = data.state;
            }
            if (data.loopCurrent != null) {
                message.loopCurrent = data.loopCurrent;
            }
            if (data.vbat != null) {
                message.vbat = data.vbat;
            }
            if (data.tipRingVoltage != null) {
                message.tipRingVoltage = data.tipRingVoltage;
            }
            if (data.lastCallerInfo != null) {
                message.lastCallerInfo = data.lastCallerInfo;
            }
            return message;
        }
        toObject() {
            const data: {
                name?: string;
                state?: string;
                loopCurrent?: string;
                vbat?: string;
                tipRingVoltage?: string;
                lastCallerInfo?: string;
            } = {};
            if (this.name != null) {
                data.name = this.name;
            }
            if (this.state != null) {
                data.state = this.state;
            }
            if (this.loopCurrent != null) {
                data.loopCurrent = this.loopCurrent;
            }
            if (this.vbat != null) {
                data.vbat = this.vbat;
            }
            if (this.tipRingVoltage != null) {
                data.tipRingVoltage = this.tipRingVoltage;
            }
            if (this.lastCallerInfo != null) {
                data.lastCallerInfo = this.lastCallerInfo;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.name.length)
                writer.writeString(1, this.name);
            if (this.state.length)
                writer.writeString(2, this.state);
            if (this.loopCurrent.length)
                writer.writeString(3, this.loopCurrent);
            if (this.vbat.length)
                writer.writeString(4, this.vbat);
            if (this.tipRingVoltage.length)
                writer.writeString(5, this.tipRingVoltage);
            if (this.lastCallerInfo.length)
                writer.writeString(6, this.lastCallerInfo);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortStatus {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortStatus();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.name = reader.readString();
                        break;
                    case 2:
                        message.state = reader.readString();
                        break;
                    case 3:
                        message.loopCurrent = reader.readString();
                        break;
                    case 4:
                        message.vbat = reader.readString();
                        break;
                    case 5:
                        message.tipRingVoltage = reader.readString();
                        break;
                    case 6:
                        message.lastCallerInfo = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortStatus {
            return PortStatus.deserialize(bytes);
        }
    }
    export class PortStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            portStatus?: PortStatus[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("portStatus" in data && data.portStatus != undefined) {
                    this.portStatus = data.portStatus;
                }
            }
        }
        get portStatus() {
            return pb_1.Message.getRepeatedWrapperField(this, PortStatus, 1) as PortStatus[];
        }
        set portStatus(value: PortStatus[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            portStatus?: ReturnType<typeof PortStatus.prototype.toObject>[];
        }): PortStatusResponse {
            const message = new PortStatusResponse({});
            if (data.portStatus != null) {
                message.portStatus = data.portStatus.map(item => PortStatus.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                portStatus?: ReturnType<typeof PortStatus.prototype.toObject>[];
            } = {};
            if (this.portStatus != null) {
                data.portStatus = this.portStatus.map((item: PortStatus) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.portStatus.length)
                writer.writeRepeatedMessage(1, this.portStatus, (item: PortStatus) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.portStatus, () => pb_1.Message.addToRepeatedWrapperField(message, 1, PortStatus.deserialize(reader), PortStatus));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortStatusResponse {
            return PortStatusResponse.deserialize(bytes);
        }
    }
    export class LiveEpisResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            epis?: Map<string, string>;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("epis" in data && data.epis != undefined) {
                    this.epis = data.epis;
                }
            }
            if (!this.epis)
                this.epis = new Map();
        }
        get epis() {
            return pb_1.Message.getField(this, 1) as any as Map<string, string>;
        }
        set epis(value: Map<string, string>) {
            pb_1.Message.setField(this, 1, value as any);
        }
        static fromObject(data: {
            epis?: {
                [key: string]: string;
            };
        }): LiveEpisResponse {
            const message = new LiveEpisResponse({});
            if (typeof data.epis == "object") {
                message.epis = new Map(Object.entries(data.epis));
            }
            return message;
        }
        toObject() {
            const data: {
                epis?: {
                    [key: string]: string;
                };
            } = {};
            if (this.epis != null) {
                data.epis = (Object.fromEntries)(this.epis);
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            for (const [key, value] of this.epis) {
                writer.writeMessage(1, this.epis, () => {
                    writer.writeString(1, key);
                    writer.writeString(2, value);
                });
            }
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): LiveEpisResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new LiveEpisResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message, () => pb_1.Map.deserializeBinary(message.epis as any, reader, reader.readString, reader.readString));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): LiveEpisResponse {
            return LiveEpisResponse.deserialize(bytes);
        }
    }
    export class WifiStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            error_msg?: string;
            gateway?: string;
            ip?: string;
            mode?: string;
            password?: string;
            SSID?: string;
            sec_mode?: string;
            status?: string;
            subnet?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("error_msg" in data && data.error_msg != undefined) {
                    this.error_msg = data.error_msg;
                }
                if ("gateway" in data && data.gateway != undefined) {
                    this.gateway = data.gateway;
                }
                if ("ip" in data && data.ip != undefined) {
                    this.ip = data.ip;
                }
                if ("mode" in data && data.mode != undefined) {
                    this.mode = data.mode;
                }
                if ("password" in data && data.password != undefined) {
                    this.password = data.password;
                }
                if ("SSID" in data && data.SSID != undefined) {
                    this.SSID = data.SSID;
                }
                if ("sec_mode" in data && data.sec_mode != undefined) {
                    this.sec_mode = data.sec_mode;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("subnet" in data && data.subnet != undefined) {
                    this.subnet = data.subnet;
                }
            }
        }
        get error_msg() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set error_msg(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get gateway() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set gateway(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get ip() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set ip(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get mode() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set mode(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get password() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set password(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get SSID() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set SSID(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get sec_mode() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set sec_mode(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 8, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 8, value);
        }
        get subnet() {
            return pb_1.Message.getFieldWithDefault(this, 9, "") as string;
        }
        set subnet(value: string) {
            pb_1.Message.setField(this, 9, value);
        }
        static fromObject(data: {
            error_msg?: string;
            gateway?: string;
            ip?: string;
            mode?: string;
            password?: string;
            SSID?: string;
            sec_mode?: string;
            status?: string;
            subnet?: string;
        }): WifiStatusResponse {
            const message = new WifiStatusResponse({});
            if (data.error_msg != null) {
                message.error_msg = data.error_msg;
            }
            if (data.gateway != null) {
                message.gateway = data.gateway;
            }
            if (data.ip != null) {
                message.ip = data.ip;
            }
            if (data.mode != null) {
                message.mode = data.mode;
            }
            if (data.password != null) {
                message.password = data.password;
            }
            if (data.SSID != null) {
                message.SSID = data.SSID;
            }
            if (data.sec_mode != null) {
                message.sec_mode = data.sec_mode;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.subnet != null) {
                message.subnet = data.subnet;
            }
            return message;
        }
        toObject() {
            const data: {
                error_msg?: string;
                gateway?: string;
                ip?: string;
                mode?: string;
                password?: string;
                SSID?: string;
                sec_mode?: string;
                status?: string;
                subnet?: string;
            } = {};
            if (this.error_msg != null) {
                data.error_msg = this.error_msg;
            }
            if (this.gateway != null) {
                data.gateway = this.gateway;
            }
            if (this.ip != null) {
                data.ip = this.ip;
            }
            if (this.mode != null) {
                data.mode = this.mode;
            }
            if (this.password != null) {
                data.password = this.password;
            }
            if (this.SSID != null) {
                data.SSID = this.SSID;
            }
            if (this.sec_mode != null) {
                data.sec_mode = this.sec_mode;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.subnet != null) {
                data.subnet = this.subnet;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.error_msg.length)
                writer.writeString(1, this.error_msg);
            if (this.gateway.length)
                writer.writeString(2, this.gateway);
            if (this.ip.length)
                writer.writeString(3, this.ip);
            if (this.mode.length)
                writer.writeString(4, this.mode);
            if (this.password.length)
                writer.writeString(5, this.password);
            if (this.SSID.length)
                writer.writeString(6, this.SSID);
            if (this.sec_mode.length)
                writer.writeString(7, this.sec_mode);
            if (this.status.length)
                writer.writeString(8, this.status);
            if (this.subnet.length)
                writer.writeString(9, this.subnet);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): WifiStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new WifiStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.error_msg = reader.readString();
                        break;
                    case 2:
                        message.gateway = reader.readString();
                        break;
                    case 3:
                        message.ip = reader.readString();
                        break;
                    case 4:
                        message.mode = reader.readString();
                        break;
                    case 5:
                        message.password = reader.readString();
                        break;
                    case 6:
                        message.SSID = reader.readString();
                        break;
                    case 7:
                        message.sec_mode = reader.readString();
                        break;
                    case 8:
                        message.status = reader.readString();
                        break;
                    case 9:
                        message.subnet = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): WifiStatusResponse {
            return WifiStatusResponse.deserialize(bytes);
        }
    }
    export class NetworkInterfaceObj extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            interface?: string;
            internet?: string;
            icmp?: string;
            wg?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("interface" in data && data.interface != undefined) {
                    this.interface = data.interface;
                }
                if ("internet" in data && data.internet != undefined) {
                    this.internet = data.internet;
                }
                if ("icmp" in data && data.icmp != undefined) {
                    this.icmp = data.icmp;
                }
                if ("wg" in data && data.wg != undefined) {
                    this.wg = data.wg;
                }
            }
        }
        get interface() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set interface(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get internet() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set internet(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get icmp() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set icmp(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get wg() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set wg(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            interface?: string;
            internet?: string;
            icmp?: string;
            wg?: string;
        }): NetworkInterfaceObj {
            const message = new NetworkInterfaceObj({});
            if (data.interface != null) {
                message.interface = data.interface;
            }
            if (data.internet != null) {
                message.internet = data.internet;
            }
            if (data.icmp != null) {
                message.icmp = data.icmp;
            }
            if (data.wg != null) {
                message.wg = data.wg;
            }
            return message;
        }
        toObject() {
            const data: {
                interface?: string;
                internet?: string;
                icmp?: string;
                wg?: string;
            } = {};
            if (this.interface != null) {
                data.interface = this.interface;
            }
            if (this.internet != null) {
                data.internet = this.internet;
            }
            if (this.icmp != null) {
                data.icmp = this.icmp;
            }
            if (this.wg != null) {
                data.wg = this.wg;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.interface.length)
                writer.writeString(1, this.interface);
            if (this.internet.length)
                writer.writeString(2, this.internet);
            if (this.icmp.length)
                writer.writeString(3, this.icmp);
            if (this.wg.length)
                writer.writeString(4, this.wg);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): NetworkInterfaceObj {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new NetworkInterfaceObj();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.interface = reader.readString();
                        break;
                    case 2:
                        message.internet = reader.readString();
                        break;
                    case 3:
                        message.icmp = reader.readString();
                        break;
                    case 4:
                        message.wg = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): NetworkInterfaceObj {
            return NetworkInterfaceObj.deserialize(bytes);
        }
    }
    export class NetworkInfoResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            dns?: string;
            error?: string;
            interfaces?: NetworkInterfaceObj[];
            timestamp?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("dns" in data && data.dns != undefined) {
                    this.dns = data.dns;
                }
                if ("error" in data && data.error != undefined) {
                    this.error = data.error;
                }
                if ("interfaces" in data && data.interfaces != undefined) {
                    this.interfaces = data.interfaces;
                }
                if ("timestamp" in data && data.timestamp != undefined) {
                    this.timestamp = data.timestamp;
                }
            }
        }
        get dns() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set dns(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get error() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set error(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get interfaces() {
            return pb_1.Message.getRepeatedWrapperField(this, NetworkInterfaceObj, 3) as NetworkInterfaceObj[];
        }
        set interfaces(value: NetworkInterfaceObj[]) {
            pb_1.Message.setRepeatedWrapperField(this, 3, value);
        }
        get timestamp() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set timestamp(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            dns?: string;
            error?: string;
            interfaces?: ReturnType<typeof NetworkInterfaceObj.prototype.toObject>[];
            timestamp?: string;
        }): NetworkInfoResponse {
            const message = new NetworkInfoResponse({});
            if (data.dns != null) {
                message.dns = data.dns;
            }
            if (data.error != null) {
                message.error = data.error;
            }
            if (data.interfaces != null) {
                message.interfaces = data.interfaces.map(item => NetworkInterfaceObj.fromObject(item));
            }
            if (data.timestamp != null) {
                message.timestamp = data.timestamp;
            }
            return message;
        }
        toObject() {
            const data: {
                dns?: string;
                error?: string;
                interfaces?: ReturnType<typeof NetworkInterfaceObj.prototype.toObject>[];
                timestamp?: string;
            } = {};
            if (this.dns != null) {
                data.dns = this.dns;
            }
            if (this.error != null) {
                data.error = this.error;
            }
            if (this.interfaces != null) {
                data.interfaces = this.interfaces.map((item: NetworkInterfaceObj) => item.toObject());
            }
            if (this.timestamp != null) {
                data.timestamp = this.timestamp;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.dns.length)
                writer.writeString(1, this.dns);
            if (this.error.length)
                writer.writeString(2, this.error);
            if (this.interfaces.length)
                writer.writeRepeatedMessage(3, this.interfaces, (item: NetworkInterfaceObj) => item.serialize(writer));
            if (this.timestamp.length)
                writer.writeString(4, this.timestamp);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): NetworkInfoResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new NetworkInfoResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.dns = reader.readString();
                        break;
                    case 2:
                        message.error = reader.readString();
                        break;
                    case 3:
                        reader.readMessage(message.interfaces, () => pb_1.Message.addToRepeatedWrapperField(message, 3, NetworkInterfaceObj.deserialize(reader), NetworkInterfaceObj));
                        break;
                    case 4:
                        message.timestamp = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): NetworkInfoResponse {
            return NetworkInfoResponse.deserialize(bytes);
        }
    }
    export class DeviceNightlyUpdateTimeResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            device_update_time?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("device_update_time" in data && data.device_update_time != undefined) {
                    this.device_update_time = data.device_update_time;
                }
            }
        }
        get device_update_time() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set device_update_time(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            device_update_time?: string;
        }): DeviceNightlyUpdateTimeResponse {
            const message = new DeviceNightlyUpdateTimeResponse({});
            if (data.device_update_time != null) {
                message.device_update_time = data.device_update_time;
            }
            return message;
        }
        toObject() {
            const data: {
                device_update_time?: string;
            } = {};
            if (this.device_update_time != null) {
                data.device_update_time = this.device_update_time;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.device_update_time.length)
                writer.writeString(1, this.device_update_time);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceNightlyUpdateTimeResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceNightlyUpdateTimeResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.device_update_time = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceNightlyUpdateTimeResponse {
            return DeviceNightlyUpdateTimeResponse.deserialize(bytes);
        }
    }
    export class PortForwardObj extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            srcIP?: string;
            srcStartPort?: number;
            srcEndPort?: number;
            dstIP?: string;
            dstStartPort?: number;
            dstEndPort?: number;
            proto?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("srcIP" in data && data.srcIP != undefined) {
                    this.srcIP = data.srcIP;
                }
                if ("srcStartPort" in data && data.srcStartPort != undefined) {
                    this.srcStartPort = data.srcStartPort;
                }
                if ("srcEndPort" in data && data.srcEndPort != undefined) {
                    this.srcEndPort = data.srcEndPort;
                }
                if ("dstIP" in data && data.dstIP != undefined) {
                    this.dstIP = data.dstIP;
                }
                if ("dstStartPort" in data && data.dstStartPort != undefined) {
                    this.dstStartPort = data.dstStartPort;
                }
                if ("dstEndPort" in data && data.dstEndPort != undefined) {
                    this.dstEndPort = data.dstEndPort;
                }
                if ("proto" in data && data.proto != undefined) {
                    this.proto = data.proto;
                }
            }
        }
        get srcIP() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set srcIP(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get srcStartPort() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set srcStartPort(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get srcEndPort() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set srcEndPort(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get dstIP() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set dstIP(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get dstStartPort() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set dstStartPort(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        get dstEndPort() {
            return pb_1.Message.getFieldWithDefault(this, 6, 0) as number;
        }
        set dstEndPort(value: number) {
            pb_1.Message.setField(this, 6, value);
        }
        get proto() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set proto(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        static fromObject(data: {
            srcIP?: string;
            srcStartPort?: number;
            srcEndPort?: number;
            dstIP?: string;
            dstStartPort?: number;
            dstEndPort?: number;
            proto?: string;
        }): PortForwardObj {
            const message = new PortForwardObj({});
            if (data.srcIP != null) {
                message.srcIP = data.srcIP;
            }
            if (data.srcStartPort != null) {
                message.srcStartPort = data.srcStartPort;
            }
            if (data.srcEndPort != null) {
                message.srcEndPort = data.srcEndPort;
            }
            if (data.dstIP != null) {
                message.dstIP = data.dstIP;
            }
            if (data.dstStartPort != null) {
                message.dstStartPort = data.dstStartPort;
            }
            if (data.dstEndPort != null) {
                message.dstEndPort = data.dstEndPort;
            }
            if (data.proto != null) {
                message.proto = data.proto;
            }
            return message;
        }
        toObject() {
            const data: {
                srcIP?: string;
                srcStartPort?: number;
                srcEndPort?: number;
                dstIP?: string;
                dstStartPort?: number;
                dstEndPort?: number;
                proto?: string;
            } = {};
            if (this.srcIP != null) {
                data.srcIP = this.srcIP;
            }
            if (this.srcStartPort != null) {
                data.srcStartPort = this.srcStartPort;
            }
            if (this.srcEndPort != null) {
                data.srcEndPort = this.srcEndPort;
            }
            if (this.dstIP != null) {
                data.dstIP = this.dstIP;
            }
            if (this.dstStartPort != null) {
                data.dstStartPort = this.dstStartPort;
            }
            if (this.dstEndPort != null) {
                data.dstEndPort = this.dstEndPort;
            }
            if (this.proto != null) {
                data.proto = this.proto;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.srcIP.length)
                writer.writeString(1, this.srcIP);
            if (this.srcStartPort != 0)
                writer.writeInt32(2, this.srcStartPort);
            if (this.srcEndPort != 0)
                writer.writeInt32(3, this.srcEndPort);
            if (this.dstIP.length)
                writer.writeString(4, this.dstIP);
            if (this.dstStartPort != 0)
                writer.writeInt32(5, this.dstStartPort);
            if (this.dstEndPort != 0)
                writer.writeInt32(6, this.dstEndPort);
            if (this.proto.length)
                writer.writeString(7, this.proto);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortForwardObj {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortForwardObj();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.srcIP = reader.readString();
                        break;
                    case 2:
                        message.srcStartPort = reader.readInt32();
                        break;
                    case 3:
                        message.srcEndPort = reader.readInt32();
                        break;
                    case 4:
                        message.dstIP = reader.readString();
                        break;
                    case 5:
                        message.dstStartPort = reader.readInt32();
                        break;
                    case 6:
                        message.dstEndPort = reader.readInt32();
                        break;
                    case 7:
                        message.proto = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortForwardObj {
            return PortForwardObj.deserialize(bytes);
        }
    }
    export class PortForwardListResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            portForwardList?: PortForwardObj[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("portForwardList" in data && data.portForwardList != undefined) {
                    this.portForwardList = data.portForwardList;
                }
            }
        }
        get portForwardList() {
            return pb_1.Message.getRepeatedWrapperField(this, PortForwardObj, 1) as PortForwardObj[];
        }
        set portForwardList(value: PortForwardObj[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        static fromObject(data: {
            portForwardList?: ReturnType<typeof PortForwardObj.prototype.toObject>[];
        }): PortForwardListResponse {
            const message = new PortForwardListResponse({});
            if (data.portForwardList != null) {
                message.portForwardList = data.portForwardList.map(item => PortForwardObj.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                portForwardList?: ReturnType<typeof PortForwardObj.prototype.toObject>[];
            } = {};
            if (this.portForwardList != null) {
                data.portForwardList = this.portForwardList.map((item: PortForwardObj) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.portForwardList.length)
                writer.writeRepeatedMessage(1, this.portForwardList, (item: PortForwardObj) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortForwardListResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortForwardListResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.portForwardList, () => pb_1.Message.addToRepeatedWrapperField(message, 1, PortForwardObj.deserialize(reader), PortForwardObj));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortForwardListResponse {
            return PortForwardListResponse.deserialize(bytes);
        }
    }
    export class PriorityInterfaceResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            priorityInterface?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("priorityInterface" in data && data.priorityInterface != undefined) {
                    this.priorityInterface = data.priorityInterface;
                }
            }
        }
        get priorityInterface() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set priorityInterface(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            priorityInterface?: string;
        }): PriorityInterfaceResponse {
            const message = new PriorityInterfaceResponse({});
            if (data.priorityInterface != null) {
                message.priorityInterface = data.priorityInterface;
            }
            return message;
        }
        toObject() {
            const data: {
                priorityInterface?: string;
            } = {};
            if (this.priorityInterface != null) {
                data.priorityInterface = this.priorityInterface;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.priorityInterface.length)
                writer.writeString(1, this.priorityInterface);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PriorityInterfaceResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PriorityInterfaceResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.priorityInterface = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PriorityInterfaceResponse {
            return PriorityInterfaceResponse.deserialize(bytes);
        }
    }
    export class PrimarySimResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            primarySim?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("primarySim" in data && data.primarySim != undefined) {
                    this.primarySim = data.primarySim;
                }
            }
        }
        get primarySim() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set primarySim(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            primarySim?: string;
        }): PrimarySimResponse {
            const message = new PrimarySimResponse({});
            if (data.primarySim != null) {
                message.primarySim = data.primarySim;
            }
            return message;
        }
        toObject() {
            const data: {
                primarySim?: string;
            } = {};
            if (this.primarySim != null) {
                data.primarySim = this.primarySim;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.primarySim.length)
                writer.writeString(1, this.primarySim);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PrimarySimResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PrimarySimResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.primarySim = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PrimarySimResponse {
            return PrimarySimResponse.deserialize(bytes);
        }
    }
    export class CurrentApnResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            apn?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("apn" in data && data.apn != undefined) {
                    this.apn = data.apn;
                }
            }
        }
        get apn() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set apn(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            apn?: string;
        }): CurrentApnResponse {
            const message = new CurrentApnResponse({});
            if (data.apn != null) {
                message.apn = data.apn;
            }
            return message;
        }
        toObject() {
            const data: {
                apn?: string;
            } = {};
            if (this.apn != null) {
                data.apn = this.apn;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.apn.length)
                writer.writeString(1, this.apn);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CurrentApnResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CurrentApnResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.apn = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CurrentApnResponse {
            return CurrentApnResponse.deserialize(bytes);
        }
    }
    export class EpikUpdateStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set status(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            status?: boolean;
        }): EpikUpdateStatusResponse {
            const message = new EpikUpdateStatusResponse({});
            if (data.status != null) {
                message.status = data.status;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: boolean;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status != false)
                writer.writeBool(1, this.status);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): EpikUpdateStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new EpikUpdateStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): EpikUpdateStatusResponse {
            return EpikUpdateStatusResponse.deserialize(bytes);
        }
    }
    export class SystemInfoResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            sysInfo?: Map<string, string>;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("sysInfo" in data && data.sysInfo != undefined) {
                    this.sysInfo = data.sysInfo;
                }
            }
            if (!this.sysInfo)
                this.sysInfo = new Map();
        }
        get sysInfo() {
            return pb_1.Message.getField(this, 1) as any as Map<string, string>;
        }
        set sysInfo(value: Map<string, string>) {
            pb_1.Message.setField(this, 1, value as any);
        }
        static fromObject(data: {
            sysInfo?: {
                [key: string]: string;
            };
        }): SystemInfoResponse {
            const message = new SystemInfoResponse({});
            if (typeof data.sysInfo == "object") {
                message.sysInfo = new Map(Object.entries(data.sysInfo));
            }
            return message;
        }
        toObject() {
            const data: {
                sysInfo?: {
                    [key: string]: string;
                };
            } = {};
            if (this.sysInfo != null) {
                data.sysInfo = (Object.fromEntries)(this.sysInfo);
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            for (const [key, value] of this.sysInfo) {
                writer.writeMessage(1, this.sysInfo, () => {
                    writer.writeString(1, key);
                    writer.writeString(2, value);
                });
            }
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SystemInfoResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SystemInfoResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message, () => pb_1.Map.deserializeBinary(message.sysInfo as any, reader, reader.readString, reader.readString));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SystemInfoResponse {
            return SystemInfoResponse.deserialize(bytes);
        }
    }
    export class SshCommandRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            command?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("command" in data && data.command != undefined) {
                    this.command = data.command;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get command() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set command(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            serial_number?: string;
            command?: string;
        }): SshCommandRequest {
            const message = new SshCommandRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.command != null) {
                message.command = data.command;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                command?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.command != null) {
                data.command = this.command;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.command.length)
                writer.writeString(2, this.command);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SshCommandRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SshCommandRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.command = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SshCommandRequest {
            return SshCommandRequest.deserialize(bytes);
        }
    }
    export class SshCommandResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            output?: string;
            error_message?: string;
            exit_code?: number;
            duration_ms?: number;
            success?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("output" in data && data.output != undefined) {
                    this.output = data.output;
                }
                if ("error_message" in data && data.error_message != undefined) {
                    this.error_message = data.error_message;
                }
                if ("exit_code" in data && data.exit_code != undefined) {
                    this.exit_code = data.exit_code;
                }
                if ("duration_ms" in data && data.duration_ms != undefined) {
                    this.duration_ms = data.duration_ms;
                }
                if ("success" in data && data.success != undefined) {
                    this.success = data.success;
                }
            }
        }
        get output() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set output(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get error_message() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set error_message(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get exit_code() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set exit_code(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get duration_ms() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set duration_ms(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get success() {
            return pb_1.Message.getFieldWithDefault(this, 5, false) as boolean;
        }
        set success(value: boolean) {
            pb_1.Message.setField(this, 5, value);
        }
        static fromObject(data: {
            output?: string;
            error_message?: string;
            exit_code?: number;
            duration_ms?: number;
            success?: boolean;
        }): SshCommandResponse {
            const message = new SshCommandResponse({});
            if (data.output != null) {
                message.output = data.output;
            }
            if (data.error_message != null) {
                message.error_message = data.error_message;
            }
            if (data.exit_code != null) {
                message.exit_code = data.exit_code;
            }
            if (data.duration_ms != null) {
                message.duration_ms = data.duration_ms;
            }
            if (data.success != null) {
                message.success = data.success;
            }
            return message;
        }
        toObject() {
            const data: {
                output?: string;
                error_message?: string;
                exit_code?: number;
                duration_ms?: number;
                success?: boolean;
            } = {};
            if (this.output != null) {
                data.output = this.output;
            }
            if (this.error_message != null) {
                data.error_message = this.error_message;
            }
            if (this.exit_code != null) {
                data.exit_code = this.exit_code;
            }
            if (this.duration_ms != null) {
                data.duration_ms = this.duration_ms;
            }
            if (this.success != null) {
                data.success = this.success;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.output.length)
                writer.writeString(1, this.output);
            if (this.error_message.length)
                writer.writeString(2, this.error_message);
            if (this.exit_code != 0)
                writer.writeInt32(3, this.exit_code);
            if (this.duration_ms != 0)
                writer.writeInt64(4, this.duration_ms);
            if (this.success != false)
                writer.writeBool(5, this.success);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SshCommandResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SshCommandResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.output = reader.readString();
                        break;
                    case 2:
                        message.error_message = reader.readString();
                        break;
                    case 3:
                        message.exit_code = reader.readInt32();
                        break;
                    case 4:
                        message.duration_ms = reader.readInt64();
                        break;
                    case 5:
                        message.success = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SshCommandResponse {
            return SshCommandResponse.deserialize(bytes);
        }
    }
    export class DCConnectionStatsResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            dc_connection_stats?: Map<string, string>;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("dc_connection_stats" in data && data.dc_connection_stats != undefined) {
                    this.dc_connection_stats = data.dc_connection_stats;
                }
            }
            if (!this.dc_connection_stats)
                this.dc_connection_stats = new Map();
        }
        get dc_connection_stats() {
            return pb_1.Message.getField(this, 1) as any as Map<string, string>;
        }
        set dc_connection_stats(value: Map<string, string>) {
            pb_1.Message.setField(this, 1, value as any);
        }
        static fromObject(data: {
            dc_connection_stats?: {
                [key: string]: string;
            };
        }): DCConnectionStatsResponse {
            const message = new DCConnectionStatsResponse({});
            if (typeof data.dc_connection_stats == "object") {
                message.dc_connection_stats = new Map(Object.entries(data.dc_connection_stats));
            }
            return message;
        }
        toObject() {
            const data: {
                dc_connection_stats?: {
                    [key: string]: string;
                };
            } = {};
            if (this.dc_connection_stats != null) {
                data.dc_connection_stats = (Object.fromEntries)(this.dc_connection_stats);
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            for (const [key, value] of this.dc_connection_stats) {
                writer.writeMessage(1, this.dc_connection_stats, () => {
                    writer.writeString(1, key);
                    writer.writeString(2, value);
                });
            }
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DCConnectionStatsResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DCConnectionStatsResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message, () => pb_1.Map.deserializeBinary(message.dc_connection_stats as any, reader, reader.readString, reader.readString));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DCConnectionStatsResponse {
            return DCConnectionStatsResponse.deserialize(bytes);
        }
    }
    export class DnsCheckResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            dns?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("dns" in data && data.dns != undefined) {
                    this.dns = data.dns;
                }
            }
        }
        get dns() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set dns(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            dns?: boolean;
        }): DnsCheckResponse {
            const message = new DnsCheckResponse({});
            if (data.dns != null) {
                message.dns = data.dns;
            }
            return message;
        }
        toObject() {
            const data: {
                dns?: boolean;
            } = {};
            if (this.dns != null) {
                data.dns = this.dns;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.dns != false)
                writer.writeBool(1, this.dns);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DnsCheckResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DnsCheckResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.dns = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DnsCheckResponse {
            return DnsCheckResponse.deserialize(bytes);
        }
    }
    export class PortInfo extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            port?: string;
            calledId?: string;
            recording?: string;
            trunkType?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
                if ("calledId" in data && data.calledId != undefined) {
                    this.calledId = data.calledId;
                }
                if ("recording" in data && data.recording != undefined) {
                    this.recording = data.recording;
                }
                if ("trunkType" in data && data.trunkType != undefined) {
                    this.trunkType = data.trunkType;
                }
            }
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get calledId() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set calledId(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get recording() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set recording(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get trunkType() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set trunkType(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            port?: string;
            calledId?: string;
            recording?: string;
            trunkType?: string;
        }): PortInfo {
            const message = new PortInfo({});
            if (data.port != null) {
                message.port = data.port;
            }
            if (data.calledId != null) {
                message.calledId = data.calledId;
            }
            if (data.recording != null) {
                message.recording = data.recording;
            }
            if (data.trunkType != null) {
                message.trunkType = data.trunkType;
            }
            return message;
        }
        toObject() {
            const data: {
                port?: string;
                calledId?: string;
                recording?: string;
                trunkType?: string;
            } = {};
            if (this.port != null) {
                data.port = this.port;
            }
            if (this.calledId != null) {
                data.calledId = this.calledId;
            }
            if (this.recording != null) {
                data.recording = this.recording;
            }
            if (this.trunkType != null) {
                data.trunkType = this.trunkType;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.port.length)
                writer.writeString(1, this.port);
            if (this.calledId.length)
                writer.writeString(2, this.calledId);
            if (this.recording.length)
                writer.writeString(3, this.recording);
            if (this.trunkType.length)
                writer.writeString(4, this.trunkType);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortInfo {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortInfo();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.port = reader.readString();
                        break;
                    case 2:
                        message.calledId = reader.readString();
                        break;
                    case 3:
                        message.recording = reader.readString();
                        break;
                    case 4:
                        message.trunkType = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortInfo {
            return PortInfo.deserialize(bytes);
        }
    }
    export class VSwitchTabResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            registered?: boolean;
            registerationConfigCreated?: boolean;
            portsInfo?: PortInfo[];
            portsConfigCreated?: string[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3, 4], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("registered" in data && data.registered != undefined) {
                    this.registered = data.registered;
                }
                if ("registerationConfigCreated" in data && data.registerationConfigCreated != undefined) {
                    this.registerationConfigCreated = data.registerationConfigCreated;
                }
                if ("portsInfo" in data && data.portsInfo != undefined) {
                    this.portsInfo = data.portsInfo;
                }
                if ("portsConfigCreated" in data && data.portsConfigCreated != undefined) {
                    this.portsConfigCreated = data.portsConfigCreated;
                }
            }
        }
        get registered() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set registered(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get registerationConfigCreated() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set registerationConfigCreated(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        get portsInfo() {
            return pb_1.Message.getRepeatedWrapperField(this, PortInfo, 3) as PortInfo[];
        }
        set portsInfo(value: PortInfo[]) {
            pb_1.Message.setRepeatedWrapperField(this, 3, value);
        }
        get portsConfigCreated() {
            return pb_1.Message.getFieldWithDefault(this, 4, []) as string[];
        }
        set portsConfigCreated(value: string[]) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            registered?: boolean;
            registerationConfigCreated?: boolean;
            portsInfo?: ReturnType<typeof PortInfo.prototype.toObject>[];
            portsConfigCreated?: string[];
        }): VSwitchTabResponse {
            const message = new VSwitchTabResponse({});
            if (data.registered != null) {
                message.registered = data.registered;
            }
            if (data.registerationConfigCreated != null) {
                message.registerationConfigCreated = data.registerationConfigCreated;
            }
            if (data.portsInfo != null) {
                message.portsInfo = data.portsInfo.map(item => PortInfo.fromObject(item));
            }
            if (data.portsConfigCreated != null) {
                message.portsConfigCreated = data.portsConfigCreated;
            }
            return message;
        }
        toObject() {
            const data: {
                registered?: boolean;
                registerationConfigCreated?: boolean;
                portsInfo?: ReturnType<typeof PortInfo.prototype.toObject>[];
                portsConfigCreated?: string[];
            } = {};
            if (this.registered != null) {
                data.registered = this.registered;
            }
            if (this.registerationConfigCreated != null) {
                data.registerationConfigCreated = this.registerationConfigCreated;
            }
            if (this.portsInfo != null) {
                data.portsInfo = this.portsInfo.map((item: PortInfo) => item.toObject());
            }
            if (this.portsConfigCreated != null) {
                data.portsConfigCreated = this.portsConfigCreated;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.registered != false)
                writer.writeBool(1, this.registered);
            if (this.registerationConfigCreated != false)
                writer.writeBool(2, this.registerationConfigCreated);
            if (this.portsInfo.length)
                writer.writeRepeatedMessage(3, this.portsInfo, (item: PortInfo) => item.serialize(writer));
            if (this.portsConfigCreated.length)
                writer.writeRepeatedString(4, this.portsConfigCreated);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): VSwitchTabResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new VSwitchTabResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.registered = reader.readBool();
                        break;
                    case 2:
                        message.registerationConfigCreated = reader.readBool();
                        break;
                    case 3:
                        reader.readMessage(message.portsInfo, () => pb_1.Message.addToRepeatedWrapperField(message, 3, PortInfo.deserialize(reader), PortInfo));
                        break;
                    case 4:
                        pb_1.Message.addToRepeatedField(message, 4, reader.readString());
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): VSwitchTabResponse {
            return VSwitchTabResponse.deserialize(bytes);
        }
    }
    export class LteAnalyzerResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            Busy?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("Busy" in data && data.Busy != undefined) {
                    this.Busy = data.Busy;
                }
            }
        }
        get Busy() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set Busy(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            Busy?: boolean;
        }): LteAnalyzerResponse {
            const message = new LteAnalyzerResponse({});
            if (data.Busy != null) {
                message.Busy = data.Busy;
            }
            return message;
        }
        toObject() {
            const data: {
                Busy?: boolean;
            } = {};
            if (this.Busy != null) {
                data.Busy = this.Busy;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.Busy != false)
                writer.writeBool(1, this.Busy);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): LteAnalyzerResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new LteAnalyzerResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.Busy = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): LteAnalyzerResponse {
            return LteAnalyzerResponse.deserialize(bytes);
        }
    }
    export class SpeedTestResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            downloadSpeed?: string;
            uploadSpeed?: string;
            latency?: string;
            jitter?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("downloadSpeed" in data && data.downloadSpeed != undefined) {
                    this.downloadSpeed = data.downloadSpeed;
                }
                if ("uploadSpeed" in data && data.uploadSpeed != undefined) {
                    this.uploadSpeed = data.uploadSpeed;
                }
                if ("latency" in data && data.latency != undefined) {
                    this.latency = data.latency;
                }
                if ("jitter" in data && data.jitter != undefined) {
                    this.jitter = data.jitter;
                }
            }
        }
        get downloadSpeed() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set downloadSpeed(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get uploadSpeed() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set uploadSpeed(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get latency() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set latency(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get jitter() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set jitter(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            downloadSpeed?: string;
            uploadSpeed?: string;
            latency?: string;
            jitter?: string;
        }): SpeedTestResponse {
            const message = new SpeedTestResponse({});
            if (data.downloadSpeed != null) {
                message.downloadSpeed = data.downloadSpeed;
            }
            if (data.uploadSpeed != null) {
                message.uploadSpeed = data.uploadSpeed;
            }
            if (data.latency != null) {
                message.latency = data.latency;
            }
            if (data.jitter != null) {
                message.jitter = data.jitter;
            }
            return message;
        }
        toObject() {
            const data: {
                downloadSpeed?: string;
                uploadSpeed?: string;
                latency?: string;
                jitter?: string;
            } = {};
            if (this.downloadSpeed != null) {
                data.downloadSpeed = this.downloadSpeed;
            }
            if (this.uploadSpeed != null) {
                data.uploadSpeed = this.uploadSpeed;
            }
            if (this.latency != null) {
                data.latency = this.latency;
            }
            if (this.jitter != null) {
                data.jitter = this.jitter;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.downloadSpeed.length)
                writer.writeString(1, this.downloadSpeed);
            if (this.uploadSpeed.length)
                writer.writeString(2, this.uploadSpeed);
            if (this.latency.length)
                writer.writeString(3, this.latency);
            if (this.jitter.length)
                writer.writeString(4, this.jitter);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SpeedTestResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SpeedTestResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.downloadSpeed = reader.readString();
                        break;
                    case 2:
                        message.uploadSpeed = reader.readString();
                        break;
                    case 3:
                        message.latency = reader.readString();
                        break;
                    case 4:
                        message.jitter = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SpeedTestResponse {
            return SpeedTestResponse.deserialize(bytes);
        }
    }
    export class InitLtePerfResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            initiated?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("initiated" in data && data.initiated != undefined) {
                    this.initiated = data.initiated;
                }
            }
        }
        get initiated() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set initiated(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            initiated?: string;
        }): InitLtePerfResponse {
            const message = new InitLtePerfResponse({});
            if (data.initiated != null) {
                message.initiated = data.initiated;
            }
            return message;
        }
        toObject() {
            const data: {
                initiated?: string;
            } = {};
            if (this.initiated != null) {
                data.initiated = this.initiated;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.initiated.length)
                writer.writeString(1, this.initiated);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): InitLtePerfResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new InitLtePerfResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.initiated = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): InitLtePerfResponse {
            return InitLtePerfResponse.deserialize(bytes);
        }
    }
    export class SimPingInfo extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            Error?: string;
            Jitter?: number;
            PacketLoss?: number;
            PingAvg?: number;
            SIM?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("Error" in data && data.Error != undefined) {
                    this.Error = data.Error;
                }
                if ("Jitter" in data && data.Jitter != undefined) {
                    this.Jitter = data.Jitter;
                }
                if ("PacketLoss" in data && data.PacketLoss != undefined) {
                    this.PacketLoss = data.PacketLoss;
                }
                if ("PingAvg" in data && data.PingAvg != undefined) {
                    this.PingAvg = data.PingAvg;
                }
                if ("SIM" in data && data.SIM != undefined) {
                    this.SIM = data.SIM;
                }
            }
        }
        get Error() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set Error(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get Jitter() {
            return pb_1.Message.getFieldWithDefault(this, 2, 0) as number;
        }
        set Jitter(value: number) {
            pb_1.Message.setField(this, 2, value);
        }
        get PacketLoss() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set PacketLoss(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get PingAvg() {
            return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
        }
        set PingAvg(value: number) {
            pb_1.Message.setField(this, 4, value);
        }
        get SIM() {
            return pb_1.Message.getFieldWithDefault(this, 5, 0) as number;
        }
        set SIM(value: number) {
            pb_1.Message.setField(this, 5, value);
        }
        static fromObject(data: {
            Error?: string;
            Jitter?: number;
            PacketLoss?: number;
            PingAvg?: number;
            SIM?: number;
        }): SimPingInfo {
            const message = new SimPingInfo({});
            if (data.Error != null) {
                message.Error = data.Error;
            }
            if (data.Jitter != null) {
                message.Jitter = data.Jitter;
            }
            if (data.PacketLoss != null) {
                message.PacketLoss = data.PacketLoss;
            }
            if (data.PingAvg != null) {
                message.PingAvg = data.PingAvg;
            }
            if (data.SIM != null) {
                message.SIM = data.SIM;
            }
            return message;
        }
        toObject() {
            const data: {
                Error?: string;
                Jitter?: number;
                PacketLoss?: number;
                PingAvg?: number;
                SIM?: number;
            } = {};
            if (this.Error != null) {
                data.Error = this.Error;
            }
            if (this.Jitter != null) {
                data.Jitter = this.Jitter;
            }
            if (this.PacketLoss != null) {
                data.PacketLoss = this.PacketLoss;
            }
            if (this.PingAvg != null) {
                data.PingAvg = this.PingAvg;
            }
            if (this.SIM != null) {
                data.SIM = this.SIM;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.Error.length)
                writer.writeString(1, this.Error);
            if (this.Jitter != 0)
                writer.writeDouble(2, this.Jitter);
            if (this.PacketLoss != 0)
                writer.writeDouble(3, this.PacketLoss);
            if (this.PingAvg != 0)
                writer.writeDouble(4, this.PingAvg);
            if (this.SIM != 0)
                writer.writeInt32(5, this.SIM);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): SimPingInfo {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new SimPingInfo();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.Error = reader.readString();
                        break;
                    case 2:
                        message.Jitter = reader.readDouble();
                        break;
                    case 3:
                        message.PacketLoss = reader.readDouble();
                        break;
                    case 4:
                        message.PingAvg = reader.readDouble();
                        break;
                    case 5:
                        message.SIM = reader.readInt32();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): SimPingInfo {
            return SimPingInfo.deserialize(bytes);
        }
    }
    export class FetchLtePerfResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            SimsInfo?: SimPingInfo[];
            TimeStamp?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("SimsInfo" in data && data.SimsInfo != undefined) {
                    this.SimsInfo = data.SimsInfo;
                }
                if ("TimeStamp" in data && data.TimeStamp != undefined) {
                    this.TimeStamp = data.TimeStamp;
                }
            }
        }
        get SimsInfo() {
            return pb_1.Message.getRepeatedWrapperField(this, SimPingInfo, 1) as SimPingInfo[];
        }
        set SimsInfo(value: SimPingInfo[]) {
            pb_1.Message.setRepeatedWrapperField(this, 1, value);
        }
        get TimeStamp() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set TimeStamp(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            SimsInfo?: ReturnType<typeof SimPingInfo.prototype.toObject>[];
            TimeStamp?: string;
        }): FetchLtePerfResponse {
            const message = new FetchLtePerfResponse({});
            if (data.SimsInfo != null) {
                message.SimsInfo = data.SimsInfo.map(item => SimPingInfo.fromObject(item));
            }
            if (data.TimeStamp != null) {
                message.TimeStamp = data.TimeStamp;
            }
            return message;
        }
        toObject() {
            const data: {
                SimsInfo?: ReturnType<typeof SimPingInfo.prototype.toObject>[];
                TimeStamp?: string;
            } = {};
            if (this.SimsInfo != null) {
                data.SimsInfo = this.SimsInfo.map((item: SimPingInfo) => item.toObject());
            }
            if (this.TimeStamp != null) {
                data.TimeStamp = this.TimeStamp;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.SimsInfo.length)
                writer.writeRepeatedMessage(1, this.SimsInfo, (item: SimPingInfo) => item.serialize(writer));
            if (this.TimeStamp.length)
                writer.writeString(2, this.TimeStamp);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): FetchLtePerfResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new FetchLtePerfResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        reader.readMessage(message.SimsInfo, () => pb_1.Message.addToRepeatedWrapperField(message, 1, SimPingInfo.deserialize(reader), SimPingInfo));
                        break;
                    case 2:
                        message.TimeStamp = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): FetchLtePerfResponse {
            return FetchLtePerfResponse.deserialize(bytes);
        }
    }
    export class PortConfigValuesResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            OnhookVolts?: string;
            OffhookCurrent?: string;
            DtmfDetectLength?: string;
            DtmfDetectGap?: string;
            TxGain?: string;
            RxGain?: string;
            DtmfMethod?: string;
            DtmfPlaybackLevel?: string;
            RingVoltage?: string;
            DigitMapShortTimer?: string;
            CpcDuration?: string;
            CpcDelayTime?: string;
            JitterBufferType?: string;
            JitterBufferMinDeley?: string;
            JitterBufferMaxDeley?: string;
            T38Enabled?: string;
            ModemMode?: string;
            VadEnable?: string;
            ThreeWayCalling?: string;
            SilenceDetectSensitivity?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("OnhookVolts" in data && data.OnhookVolts != undefined) {
                    this.OnhookVolts = data.OnhookVolts;
                }
                if ("OffhookCurrent" in data && data.OffhookCurrent != undefined) {
                    this.OffhookCurrent = data.OffhookCurrent;
                }
                if ("DtmfDetectLength" in data && data.DtmfDetectLength != undefined) {
                    this.DtmfDetectLength = data.DtmfDetectLength;
                }
                if ("DtmfDetectGap" in data && data.DtmfDetectGap != undefined) {
                    this.DtmfDetectGap = data.DtmfDetectGap;
                }
                if ("TxGain" in data && data.TxGain != undefined) {
                    this.TxGain = data.TxGain;
                }
                if ("RxGain" in data && data.RxGain != undefined) {
                    this.RxGain = data.RxGain;
                }
                if ("DtmfMethod" in data && data.DtmfMethod != undefined) {
                    this.DtmfMethod = data.DtmfMethod;
                }
                if ("DtmfPlaybackLevel" in data && data.DtmfPlaybackLevel != undefined) {
                    this.DtmfPlaybackLevel = data.DtmfPlaybackLevel;
                }
                if ("RingVoltage" in data && data.RingVoltage != undefined) {
                    this.RingVoltage = data.RingVoltage;
                }
                if ("DigitMapShortTimer" in data && data.DigitMapShortTimer != undefined) {
                    this.DigitMapShortTimer = data.DigitMapShortTimer;
                }
                if ("CpcDuration" in data && data.CpcDuration != undefined) {
                    this.CpcDuration = data.CpcDuration;
                }
                if ("CpcDelayTime" in data && data.CpcDelayTime != undefined) {
                    this.CpcDelayTime = data.CpcDelayTime;
                }
                if ("JitterBufferType" in data && data.JitterBufferType != undefined) {
                    this.JitterBufferType = data.JitterBufferType;
                }
                if ("JitterBufferMinDeley" in data && data.JitterBufferMinDeley != undefined) {
                    this.JitterBufferMinDeley = data.JitterBufferMinDeley;
                }
                if ("JitterBufferMaxDeley" in data && data.JitterBufferMaxDeley != undefined) {
                    this.JitterBufferMaxDeley = data.JitterBufferMaxDeley;
                }
                if ("T38Enabled" in data && data.T38Enabled != undefined) {
                    this.T38Enabled = data.T38Enabled;
                }
                if ("ModemMode" in data && data.ModemMode != undefined) {
                    this.ModemMode = data.ModemMode;
                }
                if ("VadEnable" in data && data.VadEnable != undefined) {
                    this.VadEnable = data.VadEnable;
                }
                if ("ThreeWayCalling" in data && data.ThreeWayCalling != undefined) {
                    this.ThreeWayCalling = data.ThreeWayCalling;
                }
                if ("SilenceDetectSensitivity" in data && data.SilenceDetectSensitivity != undefined) {
                    this.SilenceDetectSensitivity = data.SilenceDetectSensitivity;
                }
            }
        }
        get OnhookVolts() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set OnhookVolts(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get OffhookCurrent() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set OffhookCurrent(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get DtmfDetectLength() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set DtmfDetectLength(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        get DtmfDetectGap() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set DtmfDetectGap(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get TxGain() {
            return pb_1.Message.getFieldWithDefault(this, 5, "") as string;
        }
        set TxGain(value: string) {
            pb_1.Message.setField(this, 5, value);
        }
        get RxGain() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set RxGain(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get DtmfMethod() {
            return pb_1.Message.getFieldWithDefault(this, 7, "") as string;
        }
        set DtmfMethod(value: string) {
            pb_1.Message.setField(this, 7, value);
        }
        get DtmfPlaybackLevel() {
            return pb_1.Message.getFieldWithDefault(this, 8, "") as string;
        }
        set DtmfPlaybackLevel(value: string) {
            pb_1.Message.setField(this, 8, value);
        }
        get RingVoltage() {
            return pb_1.Message.getFieldWithDefault(this, 9, "") as string;
        }
        set RingVoltage(value: string) {
            pb_1.Message.setField(this, 9, value);
        }
        get DigitMapShortTimer() {
            return pb_1.Message.getFieldWithDefault(this, 10, "") as string;
        }
        set DigitMapShortTimer(value: string) {
            pb_1.Message.setField(this, 10, value);
        }
        get CpcDuration() {
            return pb_1.Message.getFieldWithDefault(this, 11, "") as string;
        }
        set CpcDuration(value: string) {
            pb_1.Message.setField(this, 11, value);
        }
        get CpcDelayTime() {
            return pb_1.Message.getFieldWithDefault(this, 12, "") as string;
        }
        set CpcDelayTime(value: string) {
            pb_1.Message.setField(this, 12, value);
        }
        get JitterBufferType() {
            return pb_1.Message.getFieldWithDefault(this, 13, "") as string;
        }
        set JitterBufferType(value: string) {
            pb_1.Message.setField(this, 13, value);
        }
        get JitterBufferMinDeley() {
            return pb_1.Message.getFieldWithDefault(this, 14, "") as string;
        }
        set JitterBufferMinDeley(value: string) {
            pb_1.Message.setField(this, 14, value);
        }
        get JitterBufferMaxDeley() {
            return pb_1.Message.getFieldWithDefault(this, 15, "") as string;
        }
        set JitterBufferMaxDeley(value: string) {
            pb_1.Message.setField(this, 15, value);
        }
        get T38Enabled() {
            return pb_1.Message.getFieldWithDefault(this, 16, "") as string;
        }
        set T38Enabled(value: string) {
            pb_1.Message.setField(this, 16, value);
        }
        get ModemMode() {
            return pb_1.Message.getFieldWithDefault(this, 17, "") as string;
        }
        set ModemMode(value: string) {
            pb_1.Message.setField(this, 17, value);
        }
        get VadEnable() {
            return pb_1.Message.getFieldWithDefault(this, 18, "") as string;
        }
        set VadEnable(value: string) {
            pb_1.Message.setField(this, 18, value);
        }
        get ThreeWayCalling() {
            return pb_1.Message.getFieldWithDefault(this, 19, "") as string;
        }
        set ThreeWayCalling(value: string) {
            pb_1.Message.setField(this, 19, value);
        }
        get SilenceDetectSensitivity() {
            return pb_1.Message.getFieldWithDefault(this, 20, "") as string;
        }
        set SilenceDetectSensitivity(value: string) {
            pb_1.Message.setField(this, 20, value);
        }
        static fromObject(data: {
            OnhookVolts?: string;
            OffhookCurrent?: string;
            DtmfDetectLength?: string;
            DtmfDetectGap?: string;
            TxGain?: string;
            RxGain?: string;
            DtmfMethod?: string;
            DtmfPlaybackLevel?: string;
            RingVoltage?: string;
            DigitMapShortTimer?: string;
            CpcDuration?: string;
            CpcDelayTime?: string;
            JitterBufferType?: string;
            JitterBufferMinDeley?: string;
            JitterBufferMaxDeley?: string;
            T38Enabled?: string;
            ModemMode?: string;
            VadEnable?: string;
            ThreeWayCalling?: string;
            SilenceDetectSensitivity?: string;
        }): PortConfigValuesResponse {
            const message = new PortConfigValuesResponse({});
            if (data.OnhookVolts != null) {
                message.OnhookVolts = data.OnhookVolts;
            }
            if (data.OffhookCurrent != null) {
                message.OffhookCurrent = data.OffhookCurrent;
            }
            if (data.DtmfDetectLength != null) {
                message.DtmfDetectLength = data.DtmfDetectLength;
            }
            if (data.DtmfDetectGap != null) {
                message.DtmfDetectGap = data.DtmfDetectGap;
            }
            if (data.TxGain != null) {
                message.TxGain = data.TxGain;
            }
            if (data.RxGain != null) {
                message.RxGain = data.RxGain;
            }
            if (data.DtmfMethod != null) {
                message.DtmfMethod = data.DtmfMethod;
            }
            if (data.DtmfPlaybackLevel != null) {
                message.DtmfPlaybackLevel = data.DtmfPlaybackLevel;
            }
            if (data.RingVoltage != null) {
                message.RingVoltage = data.RingVoltage;
            }
            if (data.DigitMapShortTimer != null) {
                message.DigitMapShortTimer = data.DigitMapShortTimer;
            }
            if (data.CpcDuration != null) {
                message.CpcDuration = data.CpcDuration;
            }
            if (data.CpcDelayTime != null) {
                message.CpcDelayTime = data.CpcDelayTime;
            }
            if (data.JitterBufferType != null) {
                message.JitterBufferType = data.JitterBufferType;
            }
            if (data.JitterBufferMinDeley != null) {
                message.JitterBufferMinDeley = data.JitterBufferMinDeley;
            }
            if (data.JitterBufferMaxDeley != null) {
                message.JitterBufferMaxDeley = data.JitterBufferMaxDeley;
            }
            if (data.T38Enabled != null) {
                message.T38Enabled = data.T38Enabled;
            }
            if (data.ModemMode != null) {
                message.ModemMode = data.ModemMode;
            }
            if (data.VadEnable != null) {
                message.VadEnable = data.VadEnable;
            }
            if (data.ThreeWayCalling != null) {
                message.ThreeWayCalling = data.ThreeWayCalling;
            }
            if (data.SilenceDetectSensitivity != null) {
                message.SilenceDetectSensitivity = data.SilenceDetectSensitivity;
            }
            return message;
        }
        toObject() {
            const data: {
                OnhookVolts?: string;
                OffhookCurrent?: string;
                DtmfDetectLength?: string;
                DtmfDetectGap?: string;
                TxGain?: string;
                RxGain?: string;
                DtmfMethod?: string;
                DtmfPlaybackLevel?: string;
                RingVoltage?: string;
                DigitMapShortTimer?: string;
                CpcDuration?: string;
                CpcDelayTime?: string;
                JitterBufferType?: string;
                JitterBufferMinDeley?: string;
                JitterBufferMaxDeley?: string;
                T38Enabled?: string;
                ModemMode?: string;
                VadEnable?: string;
                ThreeWayCalling?: string;
                SilenceDetectSensitivity?: string;
            } = {};
            if (this.OnhookVolts != null) {
                data.OnhookVolts = this.OnhookVolts;
            }
            if (this.OffhookCurrent != null) {
                data.OffhookCurrent = this.OffhookCurrent;
            }
            if (this.DtmfDetectLength != null) {
                data.DtmfDetectLength = this.DtmfDetectLength;
            }
            if (this.DtmfDetectGap != null) {
                data.DtmfDetectGap = this.DtmfDetectGap;
            }
            if (this.TxGain != null) {
                data.TxGain = this.TxGain;
            }
            if (this.RxGain != null) {
                data.RxGain = this.RxGain;
            }
            if (this.DtmfMethod != null) {
                data.DtmfMethod = this.DtmfMethod;
            }
            if (this.DtmfPlaybackLevel != null) {
                data.DtmfPlaybackLevel = this.DtmfPlaybackLevel;
            }
            if (this.RingVoltage != null) {
                data.RingVoltage = this.RingVoltage;
            }
            if (this.DigitMapShortTimer != null) {
                data.DigitMapShortTimer = this.DigitMapShortTimer;
            }
            if (this.CpcDuration != null) {
                data.CpcDuration = this.CpcDuration;
            }
            if (this.CpcDelayTime != null) {
                data.CpcDelayTime = this.CpcDelayTime;
            }
            if (this.JitterBufferType != null) {
                data.JitterBufferType = this.JitterBufferType;
            }
            if (this.JitterBufferMinDeley != null) {
                data.JitterBufferMinDeley = this.JitterBufferMinDeley;
            }
            if (this.JitterBufferMaxDeley != null) {
                data.JitterBufferMaxDeley = this.JitterBufferMaxDeley;
            }
            if (this.T38Enabled != null) {
                data.T38Enabled = this.T38Enabled;
            }
            if (this.ModemMode != null) {
                data.ModemMode = this.ModemMode;
            }
            if (this.VadEnable != null) {
                data.VadEnable = this.VadEnable;
            }
            if (this.ThreeWayCalling != null) {
                data.ThreeWayCalling = this.ThreeWayCalling;
            }
            if (this.SilenceDetectSensitivity != null) {
                data.SilenceDetectSensitivity = this.SilenceDetectSensitivity;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.OnhookVolts.length)
                writer.writeString(1, this.OnhookVolts);
            if (this.OffhookCurrent.length)
                writer.writeString(2, this.OffhookCurrent);
            if (this.DtmfDetectLength.length)
                writer.writeString(3, this.DtmfDetectLength);
            if (this.DtmfDetectGap.length)
                writer.writeString(4, this.DtmfDetectGap);
            if (this.TxGain.length)
                writer.writeString(5, this.TxGain);
            if (this.RxGain.length)
                writer.writeString(6, this.RxGain);
            if (this.DtmfMethod.length)
                writer.writeString(7, this.DtmfMethod);
            if (this.DtmfPlaybackLevel.length)
                writer.writeString(8, this.DtmfPlaybackLevel);
            if (this.RingVoltage.length)
                writer.writeString(9, this.RingVoltage);
            if (this.DigitMapShortTimer.length)
                writer.writeString(10, this.DigitMapShortTimer);
            if (this.CpcDuration.length)
                writer.writeString(11, this.CpcDuration);
            if (this.CpcDelayTime.length)
                writer.writeString(12, this.CpcDelayTime);
            if (this.JitterBufferType.length)
                writer.writeString(13, this.JitterBufferType);
            if (this.JitterBufferMinDeley.length)
                writer.writeString(14, this.JitterBufferMinDeley);
            if (this.JitterBufferMaxDeley.length)
                writer.writeString(15, this.JitterBufferMaxDeley);
            if (this.T38Enabled.length)
                writer.writeString(16, this.T38Enabled);
            if (this.ModemMode.length)
                writer.writeString(17, this.ModemMode);
            if (this.VadEnable.length)
                writer.writeString(18, this.VadEnable);
            if (this.ThreeWayCalling.length)
                writer.writeString(19, this.ThreeWayCalling);
            if (this.SilenceDetectSensitivity.length)
                writer.writeString(20, this.SilenceDetectSensitivity);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): PortConfigValuesResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new PortConfigValuesResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.OnhookVolts = reader.readString();
                        break;
                    case 2:
                        message.OffhookCurrent = reader.readString();
                        break;
                    case 3:
                        message.DtmfDetectLength = reader.readString();
                        break;
                    case 4:
                        message.DtmfDetectGap = reader.readString();
                        break;
                    case 5:
                        message.TxGain = reader.readString();
                        break;
                    case 6:
                        message.RxGain = reader.readString();
                        break;
                    case 7:
                        message.DtmfMethod = reader.readString();
                        break;
                    case 8:
                        message.DtmfPlaybackLevel = reader.readString();
                        break;
                    case 9:
                        message.RingVoltage = reader.readString();
                        break;
                    case 10:
                        message.DigitMapShortTimer = reader.readString();
                        break;
                    case 11:
                        message.CpcDuration = reader.readString();
                        break;
                    case 12:
                        message.CpcDelayTime = reader.readString();
                        break;
                    case 13:
                        message.JitterBufferType = reader.readString();
                        break;
                    case 14:
                        message.JitterBufferMinDeley = reader.readString();
                        break;
                    case 15:
                        message.JitterBufferMaxDeley = reader.readString();
                        break;
                    case 16:
                        message.T38Enabled = reader.readString();
                        break;
                    case 17:
                        message.ModemMode = reader.readString();
                        break;
                    case 18:
                        message.VadEnable = reader.readString();
                        break;
                    case 19:
                        message.ThreeWayCalling = reader.readString();
                        break;
                    case 20:
                        message.SilenceDetectSensitivity = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): PortConfigValuesResponse {
            return PortConfigValuesResponse.deserialize(bytes);
        }
    }
    interface GrpcUnaryServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
    }
    interface GrpcStreamServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
        (message: P, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
    }
    interface GrpWritableServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
    }
    interface GrpcChunkServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
        (options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
    }
    interface GrpcPromiseServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): Promise<R>;
        (message: P, options?: grpc_1.CallOptions): Promise<R>;
    }
    export abstract class UnimplementedEdgeDeviceProxyService {
        static definition = {
            PingBox: {
                path: "/edge.v1.EdgeDeviceProxy/PingBox",
                requestStream: true,
                responseStream: true,
                requestSerialize: (message: PingMessage) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => PingMessage.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PingMessage) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PingMessage.deserialize(new Uint8Array(bytes))
            },
            UnaryEcho: {
                path: "/edge.v1.EdgeDeviceProxy/UnaryEcho",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: EchoRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => EchoRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: EchoResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => EchoResponse.deserialize(new Uint8Array(bytes))
            },
            PowerSource: {
                path: "/edge.v1.EdgeDeviceProxy/PowerSource",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PowerSourceResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PowerSourceResponse.deserialize(new Uint8Array(bytes))
            },
            ActiveInterface: {
                path: "/edge.v1.EdgeDeviceProxy/ActiveInterface",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: ActiveInterfaceResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => ActiveInterfaceResponse.deserialize(new Uint8Array(bytes))
            },
            LanIp: {
                path: "/edge.v1.EdgeDeviceProxy/LanIp",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: LanIpResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => LanIpResponse.deserialize(new Uint8Array(bytes))
            },
            PublicIp: {
                path: "/edge.v1.EdgeDeviceProxy/PublicIp",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PublicIpResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PublicIpResponse.deserialize(new Uint8Array(bytes))
            },
            SignalStrength: {
                path: "/edge.v1.EdgeDeviceProxy/SignalStrength",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SignalStrengthResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SignalStrengthResponse.deserialize(new Uint8Array(bytes))
            },
            SimStatus: {
                path: "/edge.v1.EdgeDeviceProxy/SimStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SimStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SimStatusResponse.deserialize(new Uint8Array(bytes))
            },
            ModemInfo: {
                path: "/edge.v1.EdgeDeviceProxy/ModemInfo",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: ModemInfoResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => ModemInfoResponse.deserialize(new Uint8Array(bytes))
            },
            SensorData: {
                path: "/edge.v1.EdgeDeviceProxy/SensorData",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SensorDataResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SensorDataResponse.deserialize(new Uint8Array(bytes))
            },
            DeviceOnline: {
                path: "/edge.v1.EdgeDeviceProxy/DeviceOnline",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DeviceOnlineResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DeviceOnlineResponse.deserialize(new Uint8Array(bytes))
            },
            DcAvgPing: {
                path: "/edge.v1.EdgeDeviceProxy/DcAvgPing",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DcAvgPingResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DcAvgPingResponse.deserialize(new Uint8Array(bytes))
            },
            RegStatus: {
                path: "/edge.v1.EdgeDeviceProxy/RegStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: RegStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => RegStatusResponse.deserialize(new Uint8Array(bytes))
            },
            PortRegStatus: {
                path: "/edge.v1.EdgeDeviceProxy/PortRegStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: EpiRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => EpiRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PortRegStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PortRegStatusResponse.deserialize(new Uint8Array(bytes))
            },
            PortPhysicalStatus: {
                path: "/edge.v1.EdgeDeviceProxy/PortPhysicalStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: EpiRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => EpiRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PortStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PortStatusResponse.deserialize(new Uint8Array(bytes))
            },
            EnableDisablePort: {
                path: "/edge.v1.EdgeDeviceProxy/EnableDisablePort",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: EnableDisablePortRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => EnableDisablePortRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: EnableDisablePortResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => EnableDisablePortResponse.deserialize(new Uint8Array(bytes))
            },
            LiveEpis: {
                path: "/edge.v1.EdgeDeviceProxy/LiveEpis",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: LiveEpisResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => LiveEpisResponse.deserialize(new Uint8Array(bytes))
            },
            WifiStatus: {
                path: "/edge.v1.EdgeDeviceProxy/WifiStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: WifiStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => WifiStatusResponse.deserialize(new Uint8Array(bytes))
            },
            NetworkInfo: {
                path: "/edge.v1.EdgeDeviceProxy/NetworkInfo",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: NetworkInfoResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => NetworkInfoResponse.deserialize(new Uint8Array(bytes))
            },
            DeviceNightlyUpdateTime: {
                path: "/edge.v1.EdgeDeviceProxy/DeviceNightlyUpdateTime",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DeviceNightlyUpdateTimeResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DeviceNightlyUpdateTimeResponse.deserialize(new Uint8Array(bytes))
            },
            PortForwardList: {
                path: "/edge.v1.EdgeDeviceProxy/PortForwardList",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PortForwardListResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PortForwardListResponse.deserialize(new Uint8Array(bytes))
            },
            PriorityInterface: {
                path: "/edge.v1.EdgeDeviceProxy/PriorityInterface",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PriorityInterfaceResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PriorityInterfaceResponse.deserialize(new Uint8Array(bytes))
            },
            PrimarySim: {
                path: "/edge.v1.EdgeDeviceProxy/PrimarySim",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PrimarySimResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PrimarySimResponse.deserialize(new Uint8Array(bytes))
            },
            CurrentApn: {
                path: "/edge.v1.EdgeDeviceProxy/CurrentApn",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: CurrentApnResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => CurrentApnResponse.deserialize(new Uint8Array(bytes))
            },
            EpikUpdateStatus: {
                path: "/edge.v1.EdgeDeviceProxy/EpikUpdateStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: EpikUpdateStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => EpikUpdateStatusResponse.deserialize(new Uint8Array(bytes))
            },
            SystemInfo: {
                path: "/edge.v1.EdgeDeviceProxy/SystemInfo",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SystemInfoResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SystemInfoResponse.deserialize(new Uint8Array(bytes))
            },
            DCConnectionStats: {
                path: "/edge.v1.EdgeDeviceProxy/DCConnectionStats",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DCConnectionStatsResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DCConnectionStatsResponse.deserialize(new Uint8Array(bytes))
            },
            DnsCheck: {
                path: "/edge.v1.EdgeDeviceProxy/DnsCheck",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DnsCheckResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DnsCheckResponse.deserialize(new Uint8Array(bytes))
            },
            VSwitchTab: {
                path: "/edge.v1.EdgeDeviceProxy/VSwitchTab",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: VSwitchTabResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => VSwitchTabResponse.deserialize(new Uint8Array(bytes))
            },
            LteAnalyzer: {
                path: "/edge.v1.EdgeDeviceProxy/LteAnalyzer",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: LteAnalyzerResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => LteAnalyzerResponse.deserialize(new Uint8Array(bytes))
            },
            SpeedTest: {
                path: "/edge.v1.EdgeDeviceProxy/SpeedTest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SpeedTestResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SpeedTestResponse.deserialize(new Uint8Array(bytes))
            },
            InitLtePerf: {
                path: "/edge.v1.EdgeDeviceProxy/InitLtePerf",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: InitLtePerfResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => InitLtePerfResponse.deserialize(new Uint8Array(bytes))
            },
            FetchLtePerf: {
                path: "/edge.v1.EdgeDeviceProxy/FetchLtePerf",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: FetchLtePerfResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => FetchLtePerfResponse.deserialize(new Uint8Array(bytes))
            },
            PortConfigValues: {
                path: "/edge.v1.EdgeDeviceProxy/PortConfigValues",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: EpiRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => EpiRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: PortConfigValuesResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => PortConfigValuesResponse.deserialize(new Uint8Array(bytes))
            },
            HandleRequest: {
                path: "/edge.v1.EdgeDeviceProxy/HandleRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DeviceResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DeviceResponse.deserialize(new Uint8Array(bytes))
            },
            EnqueueRequest: {
                path: "/edge.v1.EdgeDeviceProxy/EnqueueRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: AsyncRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => AsyncRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: AsyncResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => AsyncResponse.deserialize(new Uint8Array(bytes))
            },
            GetRequestStatus: {
                path: "/edge.v1.EdgeDeviceProxy/GetRequestStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: StatusRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => StatusRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StatusResponse.deserialize(new Uint8Array(bytes))
            },
            CancelRequest: {
                path: "/edge.v1.EdgeDeviceProxy/CancelRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: CancelRequestMessage) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => CancelRequestMessage.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: CancelResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => CancelResponse.deserialize(new Uint8Array(bytes))
            },
            DashboardData: {
                path: "/edge.v1.EdgeDeviceProxy/DashboardData",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DashboardRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DashboardRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DashboardResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DashboardResponse.deserialize(new Uint8Array(bytes))
            },
            ExecuteSshCommand: {
                path: "/edge.v1.EdgeDeviceProxy/ExecuteSshCommand",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: SshCommandRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => SshCommandRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SshCommandResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SshCommandResponse.deserialize(new Uint8Array(bytes))
            },
            ExecuteSshCommandAsync: {
                path: "/edge.v1.EdgeDeviceProxy/ExecuteSshCommandAsync",
                requestStream: false,
                responseStream: true,
                requestSerialize: (message: SshCommandRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => SshCommandRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: SshCommandResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => SshCommandResponse.deserialize(new Uint8Array(bytes))
            }
        };
        [method: string]: grpc_1.UntypedHandleCall;
        abstract PingBox(call: grpc_1.ServerDuplexStream<PingMessage, PingMessage>): void;
        abstract UnaryEcho(call: grpc_1.ServerUnaryCall<EchoRequest, EchoResponse>, callback: grpc_1.sendUnaryData<EchoResponse>): void;
        abstract PowerSource(call: grpc_1.ServerUnaryCall<DeviceRequest, PowerSourceResponse>, callback: grpc_1.sendUnaryData<PowerSourceResponse>): void;
        abstract ActiveInterface(call: grpc_1.ServerUnaryCall<DeviceRequest, ActiveInterfaceResponse>, callback: grpc_1.sendUnaryData<ActiveInterfaceResponse>): void;
        abstract LanIp(call: grpc_1.ServerUnaryCall<DeviceRequest, LanIpResponse>, callback: grpc_1.sendUnaryData<LanIpResponse>): void;
        abstract PublicIp(call: grpc_1.ServerUnaryCall<DeviceRequest, PublicIpResponse>, callback: grpc_1.sendUnaryData<PublicIpResponse>): void;
        abstract SignalStrength(call: grpc_1.ServerUnaryCall<DeviceRequest, SignalStrengthResponse>, callback: grpc_1.sendUnaryData<SignalStrengthResponse>): void;
        abstract SimStatus(call: grpc_1.ServerUnaryCall<DeviceRequest, SimStatusResponse>, callback: grpc_1.sendUnaryData<SimStatusResponse>): void;
        abstract ModemInfo(call: grpc_1.ServerUnaryCall<DeviceRequest, ModemInfoResponse>, callback: grpc_1.sendUnaryData<ModemInfoResponse>): void;
        abstract SensorData(call: grpc_1.ServerUnaryCall<DeviceRequest, SensorDataResponse>, callback: grpc_1.sendUnaryData<SensorDataResponse>): void;
        abstract DeviceOnline(call: grpc_1.ServerUnaryCall<DeviceRequest, DeviceOnlineResponse>, callback: grpc_1.sendUnaryData<DeviceOnlineResponse>): void;
        abstract DcAvgPing(call: grpc_1.ServerUnaryCall<DeviceRequest, DcAvgPingResponse>, callback: grpc_1.sendUnaryData<DcAvgPingResponse>): void;
        abstract RegStatus(call: grpc_1.ServerUnaryCall<DeviceRequest, RegStatusResponse>, callback: grpc_1.sendUnaryData<RegStatusResponse>): void;
        abstract PortRegStatus(call: grpc_1.ServerUnaryCall<EpiRequest, PortRegStatusResponse>, callback: grpc_1.sendUnaryData<PortRegStatusResponse>): void;
        abstract PortPhysicalStatus(call: grpc_1.ServerUnaryCall<EpiRequest, PortStatusResponse>, callback: grpc_1.sendUnaryData<PortStatusResponse>): void;
        abstract EnableDisablePort(call: grpc_1.ServerUnaryCall<EnableDisablePortRequest, EnableDisablePortResponse>, callback: grpc_1.sendUnaryData<EnableDisablePortResponse>): void;
        abstract LiveEpis(call: grpc_1.ServerUnaryCall<DeviceRequest, LiveEpisResponse>, callback: grpc_1.sendUnaryData<LiveEpisResponse>): void;
        abstract WifiStatus(call: grpc_1.ServerUnaryCall<DeviceRequest, WifiStatusResponse>, callback: grpc_1.sendUnaryData<WifiStatusResponse>): void;
        abstract NetworkInfo(call: grpc_1.ServerUnaryCall<DeviceRequest, NetworkInfoResponse>, callback: grpc_1.sendUnaryData<NetworkInfoResponse>): void;
        abstract DeviceNightlyUpdateTime(call: grpc_1.ServerUnaryCall<DeviceRequest, DeviceNightlyUpdateTimeResponse>, callback: grpc_1.sendUnaryData<DeviceNightlyUpdateTimeResponse>): void;
        abstract PortForwardList(call: grpc_1.ServerUnaryCall<DeviceRequest, PortForwardListResponse>, callback: grpc_1.sendUnaryData<PortForwardListResponse>): void;
        abstract PriorityInterface(call: grpc_1.ServerUnaryCall<DeviceRequest, PriorityInterfaceResponse>, callback: grpc_1.sendUnaryData<PriorityInterfaceResponse>): void;
        abstract PrimarySim(call: grpc_1.ServerUnaryCall<DeviceRequest, PrimarySimResponse>, callback: grpc_1.sendUnaryData<PrimarySimResponse>): void;
        abstract CurrentApn(call: grpc_1.ServerUnaryCall<DeviceRequest, CurrentApnResponse>, callback: grpc_1.sendUnaryData<CurrentApnResponse>): void;
        abstract EpikUpdateStatus(call: grpc_1.ServerUnaryCall<DeviceRequest, EpikUpdateStatusResponse>, callback: grpc_1.sendUnaryData<EpikUpdateStatusResponse>): void;
        abstract SystemInfo(call: grpc_1.ServerUnaryCall<DeviceRequest, SystemInfoResponse>, callback: grpc_1.sendUnaryData<SystemInfoResponse>): void;
        abstract DCConnectionStats(call: grpc_1.ServerUnaryCall<DeviceRequest, DCConnectionStatsResponse>, callback: grpc_1.sendUnaryData<DCConnectionStatsResponse>): void;
        abstract DnsCheck(call: grpc_1.ServerUnaryCall<DeviceRequest, DnsCheckResponse>, callback: grpc_1.sendUnaryData<DnsCheckResponse>): void;
        abstract VSwitchTab(call: grpc_1.ServerUnaryCall<DeviceRequest, VSwitchTabResponse>, callback: grpc_1.sendUnaryData<VSwitchTabResponse>): void;
        abstract LteAnalyzer(call: grpc_1.ServerUnaryCall<DeviceRequest, LteAnalyzerResponse>, callback: grpc_1.sendUnaryData<LteAnalyzerResponse>): void;
        abstract SpeedTest(call: grpc_1.ServerUnaryCall<DeviceRequest, SpeedTestResponse>, callback: grpc_1.sendUnaryData<SpeedTestResponse>): void;
        abstract InitLtePerf(call: grpc_1.ServerUnaryCall<DeviceRequest, InitLtePerfResponse>, callback: grpc_1.sendUnaryData<InitLtePerfResponse>): void;
        abstract FetchLtePerf(call: grpc_1.ServerUnaryCall<DeviceRequest, FetchLtePerfResponse>, callback: grpc_1.sendUnaryData<FetchLtePerfResponse>): void;
        abstract PortConfigValues(call: grpc_1.ServerUnaryCall<EpiRequest, PortConfigValuesResponse>, callback: grpc_1.sendUnaryData<PortConfigValuesResponse>): void;
        abstract HandleRequest(call: grpc_1.ServerUnaryCall<DeviceRequest, DeviceResponse>, callback: grpc_1.sendUnaryData<DeviceResponse>): void;
        abstract EnqueueRequest(call: grpc_1.ServerUnaryCall<AsyncRequest, AsyncResponse>, callback: grpc_1.sendUnaryData<AsyncResponse>): void;
        abstract GetRequestStatus(call: grpc_1.ServerUnaryCall<StatusRequest, StatusResponse>, callback: grpc_1.sendUnaryData<StatusResponse>): void;
        abstract CancelRequest(call: grpc_1.ServerUnaryCall<CancelRequestMessage, CancelResponse>, callback: grpc_1.sendUnaryData<CancelResponse>): void;
        abstract DashboardData(call: grpc_1.ServerUnaryCall<DashboardRequest, DashboardResponse>, callback: grpc_1.sendUnaryData<DashboardResponse>): void;
        abstract ExecuteSshCommand(call: grpc_1.ServerUnaryCall<SshCommandRequest, SshCommandResponse>, callback: grpc_1.sendUnaryData<SshCommandResponse>): void;
        abstract ExecuteSshCommandAsync(call: grpc_1.ServerWritableStream<SshCommandRequest, SshCommandResponse>): void;
    }
    export class EdgeDeviceProxyClient extends grpc_1.makeGenericClientConstructor(UnimplementedEdgeDeviceProxyService.definition, "EdgeDeviceProxy", {}) {
        constructor(address: string, credentials: grpc_1.ChannelCredentials, options?: Partial<grpc_1.ChannelOptions>) {
            super(address, credentials, options);
        }
        PingBox: GrpcChunkServiceInterface<PingMessage, PingMessage> = (metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<PingMessage, PingMessage> => {
            return super.PingBox(metadata, options);
        };
        UnaryEcho: GrpcUnaryServiceInterface<EchoRequest, EchoResponse> = (message: EchoRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<EchoResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<EchoResponse>, callback?: grpc_1.requestCallback<EchoResponse>): grpc_1.ClientUnaryCall => {
            return super.UnaryEcho(message, metadata, options, callback);
        };
        PowerSource: GrpcUnaryServiceInterface<DeviceRequest, PowerSourceResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PowerSourceResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PowerSourceResponse>, callback?: grpc_1.requestCallback<PowerSourceResponse>): grpc_1.ClientUnaryCall => {
            return super.PowerSource(message, metadata, options, callback);
        };
        ActiveInterface: GrpcUnaryServiceInterface<DeviceRequest, ActiveInterfaceResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<ActiveInterfaceResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<ActiveInterfaceResponse>, callback?: grpc_1.requestCallback<ActiveInterfaceResponse>): grpc_1.ClientUnaryCall => {
            return super.ActiveInterface(message, metadata, options, callback);
        };
        LanIp: GrpcUnaryServiceInterface<DeviceRequest, LanIpResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<LanIpResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<LanIpResponse>, callback?: grpc_1.requestCallback<LanIpResponse>): grpc_1.ClientUnaryCall => {
            return super.LanIp(message, metadata, options, callback);
        };
        PublicIp: GrpcUnaryServiceInterface<DeviceRequest, PublicIpResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PublicIpResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PublicIpResponse>, callback?: grpc_1.requestCallback<PublicIpResponse>): grpc_1.ClientUnaryCall => {
            return super.PublicIp(message, metadata, options, callback);
        };
        SignalStrength: GrpcUnaryServiceInterface<DeviceRequest, SignalStrengthResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SignalStrengthResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SignalStrengthResponse>, callback?: grpc_1.requestCallback<SignalStrengthResponse>): grpc_1.ClientUnaryCall => {
            return super.SignalStrength(message, metadata, options, callback);
        };
        SimStatus: GrpcUnaryServiceInterface<DeviceRequest, SimStatusResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SimStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SimStatusResponse>, callback?: grpc_1.requestCallback<SimStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.SimStatus(message, metadata, options, callback);
        };
        ModemInfo: GrpcUnaryServiceInterface<DeviceRequest, ModemInfoResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<ModemInfoResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<ModemInfoResponse>, callback?: grpc_1.requestCallback<ModemInfoResponse>): grpc_1.ClientUnaryCall => {
            return super.ModemInfo(message, metadata, options, callback);
        };
        SensorData: GrpcUnaryServiceInterface<DeviceRequest, SensorDataResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SensorDataResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SensorDataResponse>, callback?: grpc_1.requestCallback<SensorDataResponse>): grpc_1.ClientUnaryCall => {
            return super.SensorData(message, metadata, options, callback);
        };
        DeviceOnline: GrpcUnaryServiceInterface<DeviceRequest, DeviceOnlineResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DeviceOnlineResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DeviceOnlineResponse>, callback?: grpc_1.requestCallback<DeviceOnlineResponse>): grpc_1.ClientUnaryCall => {
            return super.DeviceOnline(message, metadata, options, callback);
        };
        DcAvgPing: GrpcUnaryServiceInterface<DeviceRequest, DcAvgPingResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DcAvgPingResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DcAvgPingResponse>, callback?: grpc_1.requestCallback<DcAvgPingResponse>): grpc_1.ClientUnaryCall => {
            return super.DcAvgPing(message, metadata, options, callback);
        };
        RegStatus: GrpcUnaryServiceInterface<DeviceRequest, RegStatusResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<RegStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<RegStatusResponse>, callback?: grpc_1.requestCallback<RegStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.RegStatus(message, metadata, options, callback);
        };
        PortRegStatus: GrpcUnaryServiceInterface<EpiRequest, PortRegStatusResponse> = (message: EpiRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PortRegStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PortRegStatusResponse>, callback?: grpc_1.requestCallback<PortRegStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.PortRegStatus(message, metadata, options, callback);
        };
        PortPhysicalStatus: GrpcUnaryServiceInterface<EpiRequest, PortStatusResponse> = (message: EpiRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PortStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PortStatusResponse>, callback?: grpc_1.requestCallback<PortStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.PortPhysicalStatus(message, metadata, options, callback);
        };
        EnableDisablePort: GrpcUnaryServiceInterface<EnableDisablePortRequest, EnableDisablePortResponse> = (message: EnableDisablePortRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<EnableDisablePortResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<EnableDisablePortResponse>, callback?: grpc_1.requestCallback<EnableDisablePortResponse>): grpc_1.ClientUnaryCall => {
            return super.EnableDisablePort(message, metadata, options, callback);
        };
        LiveEpis: GrpcUnaryServiceInterface<DeviceRequest, LiveEpisResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<LiveEpisResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<LiveEpisResponse>, callback?: grpc_1.requestCallback<LiveEpisResponse>): grpc_1.ClientUnaryCall => {
            return super.LiveEpis(message, metadata, options, callback);
        };
        WifiStatus: GrpcUnaryServiceInterface<DeviceRequest, WifiStatusResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<WifiStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<WifiStatusResponse>, callback?: grpc_1.requestCallback<WifiStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.WifiStatus(message, metadata, options, callback);
        };
        NetworkInfo: GrpcUnaryServiceInterface<DeviceRequest, NetworkInfoResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<NetworkInfoResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<NetworkInfoResponse>, callback?: grpc_1.requestCallback<NetworkInfoResponse>): grpc_1.ClientUnaryCall => {
            return super.NetworkInfo(message, metadata, options, callback);
        };
        DeviceNightlyUpdateTime: GrpcUnaryServiceInterface<DeviceRequest, DeviceNightlyUpdateTimeResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DeviceNightlyUpdateTimeResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DeviceNightlyUpdateTimeResponse>, callback?: grpc_1.requestCallback<DeviceNightlyUpdateTimeResponse>): grpc_1.ClientUnaryCall => {
            return super.DeviceNightlyUpdateTime(message, metadata, options, callback);
        };
        PortForwardList: GrpcUnaryServiceInterface<DeviceRequest, PortForwardListResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PortForwardListResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PortForwardListResponse>, callback?: grpc_1.requestCallback<PortForwardListResponse>): grpc_1.ClientUnaryCall => {
            return super.PortForwardList(message, metadata, options, callback);
        };
        PriorityInterface: GrpcUnaryServiceInterface<DeviceRequest, PriorityInterfaceResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PriorityInterfaceResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PriorityInterfaceResponse>, callback?: grpc_1.requestCallback<PriorityInterfaceResponse>): grpc_1.ClientUnaryCall => {
            return super.PriorityInterface(message, metadata, options, callback);
        };
        PrimarySim: GrpcUnaryServiceInterface<DeviceRequest, PrimarySimResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PrimarySimResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PrimarySimResponse>, callback?: grpc_1.requestCallback<PrimarySimResponse>): grpc_1.ClientUnaryCall => {
            return super.PrimarySim(message, metadata, options, callback);
        };
        CurrentApn: GrpcUnaryServiceInterface<DeviceRequest, CurrentApnResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<CurrentApnResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<CurrentApnResponse>, callback?: grpc_1.requestCallback<CurrentApnResponse>): grpc_1.ClientUnaryCall => {
            return super.CurrentApn(message, metadata, options, callback);
        };
        EpikUpdateStatus: GrpcUnaryServiceInterface<DeviceRequest, EpikUpdateStatusResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<EpikUpdateStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<EpikUpdateStatusResponse>, callback?: grpc_1.requestCallback<EpikUpdateStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.EpikUpdateStatus(message, metadata, options, callback);
        };
        SystemInfo: GrpcUnaryServiceInterface<DeviceRequest, SystemInfoResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SystemInfoResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SystemInfoResponse>, callback?: grpc_1.requestCallback<SystemInfoResponse>): grpc_1.ClientUnaryCall => {
            return super.SystemInfo(message, metadata, options, callback);
        };
        DCConnectionStats: GrpcUnaryServiceInterface<DeviceRequest, DCConnectionStatsResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DCConnectionStatsResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DCConnectionStatsResponse>, callback?: grpc_1.requestCallback<DCConnectionStatsResponse>): grpc_1.ClientUnaryCall => {
            return super.DCConnectionStats(message, metadata, options, callback);
        };
        DnsCheck: GrpcUnaryServiceInterface<DeviceRequest, DnsCheckResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DnsCheckResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DnsCheckResponse>, callback?: grpc_1.requestCallback<DnsCheckResponse>): grpc_1.ClientUnaryCall => {
            return super.DnsCheck(message, metadata, options, callback);
        };
        VSwitchTab: GrpcUnaryServiceInterface<DeviceRequest, VSwitchTabResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<VSwitchTabResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<VSwitchTabResponse>, callback?: grpc_1.requestCallback<VSwitchTabResponse>): grpc_1.ClientUnaryCall => {
            return super.VSwitchTab(message, metadata, options, callback);
        };
        LteAnalyzer: GrpcUnaryServiceInterface<DeviceRequest, LteAnalyzerResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<LteAnalyzerResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<LteAnalyzerResponse>, callback?: grpc_1.requestCallback<LteAnalyzerResponse>): grpc_1.ClientUnaryCall => {
            return super.LteAnalyzer(message, metadata, options, callback);
        };
        SpeedTest: GrpcUnaryServiceInterface<DeviceRequest, SpeedTestResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SpeedTestResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SpeedTestResponse>, callback?: grpc_1.requestCallback<SpeedTestResponse>): grpc_1.ClientUnaryCall => {
            return super.SpeedTest(message, metadata, options, callback);
        };
        InitLtePerf: GrpcUnaryServiceInterface<DeviceRequest, InitLtePerfResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<InitLtePerfResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<InitLtePerfResponse>, callback?: grpc_1.requestCallback<InitLtePerfResponse>): grpc_1.ClientUnaryCall => {
            return super.InitLtePerf(message, metadata, options, callback);
        };
        FetchLtePerf: GrpcUnaryServiceInterface<DeviceRequest, FetchLtePerfResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<FetchLtePerfResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<FetchLtePerfResponse>, callback?: grpc_1.requestCallback<FetchLtePerfResponse>): grpc_1.ClientUnaryCall => {
            return super.FetchLtePerf(message, metadata, options, callback);
        };
        PortConfigValues: GrpcUnaryServiceInterface<EpiRequest, PortConfigValuesResponse> = (message: EpiRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<PortConfigValuesResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<PortConfigValuesResponse>, callback?: grpc_1.requestCallback<PortConfigValuesResponse>): grpc_1.ClientUnaryCall => {
            return super.PortConfigValues(message, metadata, options, callback);
        };
        HandleRequest: GrpcUnaryServiceInterface<DeviceRequest, DeviceResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DeviceResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DeviceResponse>, callback?: grpc_1.requestCallback<DeviceResponse>): grpc_1.ClientUnaryCall => {
            return super.HandleRequest(message, metadata, options, callback);
        };
        EnqueueRequest: GrpcUnaryServiceInterface<AsyncRequest, AsyncResponse> = (message: AsyncRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<AsyncResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<AsyncResponse>, callback?: grpc_1.requestCallback<AsyncResponse>): grpc_1.ClientUnaryCall => {
            return super.EnqueueRequest(message, metadata, options, callback);
        };
        GetRequestStatus: GrpcUnaryServiceInterface<StatusRequest, StatusResponse> = (message: StatusRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<StatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<StatusResponse>, callback?: grpc_1.requestCallback<StatusResponse>): grpc_1.ClientUnaryCall => {
            return super.GetRequestStatus(message, metadata, options, callback);
        };
        CancelRequest: GrpcUnaryServiceInterface<CancelRequestMessage, CancelResponse> = (message: CancelRequestMessage, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<CancelResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<CancelResponse>, callback?: grpc_1.requestCallback<CancelResponse>): grpc_1.ClientUnaryCall => {
            return super.CancelRequest(message, metadata, options, callback);
        };
        DashboardData: GrpcUnaryServiceInterface<DashboardRequest, DashboardResponse> = (message: DashboardRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DashboardResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DashboardResponse>, callback?: grpc_1.requestCallback<DashboardResponse>): grpc_1.ClientUnaryCall => {
            return super.DashboardData(message, metadata, options, callback);
        };
        ExecuteSshCommand: GrpcUnaryServiceInterface<SshCommandRequest, SshCommandResponse> = (message: SshCommandRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<SshCommandResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<SshCommandResponse>, callback?: grpc_1.requestCallback<SshCommandResponse>): grpc_1.ClientUnaryCall => {
            return super.ExecuteSshCommand(message, metadata, options, callback);
        };
        ExecuteSshCommandAsync: GrpcStreamServiceInterface<SshCommandRequest, SshCommandResponse> = (message: SshCommandRequest, metadata?: grpc_1.Metadata | grpc_1.CallOptions, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<SshCommandResponse> => {
            return super.ExecuteSshCommandAsync(message, metadata, options);
        };
    }
}

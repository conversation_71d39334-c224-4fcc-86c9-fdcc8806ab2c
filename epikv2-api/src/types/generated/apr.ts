/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.21.12
 * source: apr.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
import * as grpc_1 from "@grpc/grpc-js";
export namespace apr.v1 {
    export class APRToggleRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: string;
            enable?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
                if ("enable" in data && data.enable != undefined) {
                    this.enable = data.enable;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get enable() {
            return pb_1.Message.getFieldWithDefault(this, 3, false) as boolean;
        }
        set enable(value: boolean) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: string;
            enable?: boolean;
        }): APRToggleRequest {
            const message = new APRToggleRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            if (data.enable != null) {
                message.enable = data.enable;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: string;
                enable?: boolean;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            if (this.enable != null) {
                data.enable = this.enable;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port.length)
                writer.writeString(2, this.port);
            if (this.enable != false)
                writer.writeBool(3, this.enable);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRToggleRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRToggleRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readString();
                        break;
                    case 3:
                        message.enable = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRToggleRequest {
            return APRToggleRequest.deserialize(bytes);
        }
    }
    export class APRToggleResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: boolean;
            response?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("response" in data && data.response != undefined) {
                    this.response = data.response;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set status(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get response() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set response(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: boolean;
            response?: string;
        }): APRToggleResponse {
            const message = new APRToggleResponse({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.response != null) {
                message.response = data.response;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: boolean;
                response?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.response != null) {
                data.response = this.response;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status != false)
                writer.writeBool(1, this.status);
            if (this.response.length)
                writer.writeString(2, this.response);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRToggleResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRToggleResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readBool();
                        break;
                    case 2:
                        message.response = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRToggleResponse {
            return APRToggleResponse.deserialize(bytes);
        }
    }
    export class InitiateAprRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: string;
            companion_port?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
                if ("companion_port" in data && data.companion_port != undefined) {
                    this.companion_port = data.companion_port;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get companion_port() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set companion_port(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: string;
            companion_port?: string;
        }): InitiateAprRequest {
            const message = new InitiateAprRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            if (data.companion_port != null) {
                message.companion_port = data.companion_port;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: string;
                companion_port?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            if (this.companion_port != null) {
                data.companion_port = this.companion_port;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port.length)
                writer.writeString(2, this.port);
            if (this.companion_port.length)
                writer.writeString(3, this.companion_port);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): InitiateAprRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new InitiateAprRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readString();
                        break;
                    case 3:
                        message.companion_port = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): InitiateAprRequest {
            return InitiateAprRequest.deserialize(bytes);
        }
    }
    export class InitiateAprResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: boolean;
            response?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("response" in data && data.response != undefined) {
                    this.response = data.response;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set status(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get response() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set response(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: boolean;
            response?: string;
        }): InitiateAprResponse {
            const message = new InitiateAprResponse({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.response != null) {
                message.response = data.response;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: boolean;
                response?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.response != null) {
                data.response = this.response;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status != false)
                writer.writeBool(1, this.status);
            if (this.response.length)
                writer.writeString(2, this.response);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): InitiateAprResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new InitiateAprResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readBool();
                        break;
                    case 2:
                        message.response = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): InitiateAprResponse {
            return InitiateAprResponse.deserialize(bytes);
        }
    }
    export class APRTestResultRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: string;
        }): APRTestResultRequest {
            const message = new APRTestResultRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port.length)
                writer.writeString(2, this.port);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRTestResultRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRTestResultRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRTestResultRequest {
            return APRTestResultRequest.deserialize(bytes);
        }
    }
    export class APRTestResultResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            result?: boolean;
            response?: string;
            timestamp?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("result" in data && data.result != undefined) {
                    this.result = data.result;
                }
                if ("response" in data && data.response != undefined) {
                    this.response = data.response;
                }
                if ("timestamp" in data && data.timestamp != undefined) {
                    this.timestamp = data.timestamp;
                }
            }
        }
        get result() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set result(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get response() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set response(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get timestamp() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set timestamp(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            result?: boolean;
            response?: string;
            timestamp?: string;
        }): APRTestResultResponse {
            const message = new APRTestResultResponse({});
            if (data.result != null) {
                message.result = data.result;
            }
            if (data.response != null) {
                message.response = data.response;
            }
            if (data.timestamp != null) {
                message.timestamp = data.timestamp;
            }
            return message;
        }
        toObject() {
            const data: {
                result?: boolean;
                response?: string;
                timestamp?: string;
            } = {};
            if (this.result != null) {
                data.result = this.result;
            }
            if (this.response != null) {
                data.response = this.response;
            }
            if (this.timestamp != null) {
                data.timestamp = this.timestamp;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.result != false)
                writer.writeBool(1, this.result);
            if (this.response.length)
                writer.writeString(2, this.response);
            if (this.timestamp.length)
                writer.writeString(3, this.timestamp);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRTestResultResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRTestResultResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.result = reader.readBool();
                        break;
                    case 2:
                        message.response = reader.readString();
                        break;
                    case 3:
                        message.timestamp = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRTestResultResponse {
            return APRTestResultResponse.deserialize(bytes);
        }
    }
    export class APRStatusRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: string;
        }): APRStatusRequest {
            const message = new APRStatusRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port.length)
                writer.writeString(2, this.port);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRStatusRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRStatusRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRStatusRequest {
            return APRStatusRequest.deserialize(bytes);
        }
    }
    export class APRStatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status?: boolean;
            response?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("response" in data && data.response != undefined) {
                    this.response = data.response;
                }
            }
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
        }
        set status(value: boolean) {
            pb_1.Message.setField(this, 1, value);
        }
        get response() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set response(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        static fromObject(data: {
            status?: boolean;
            response?: string;
        }): APRStatusResponse {
            const message = new APRStatusResponse({});
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.response != null) {
                message.response = data.response;
            }
            return message;
        }
        toObject() {
            const data: {
                status?: boolean;
                response?: string;
            } = {};
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.response != null) {
                data.response = this.response;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status != false)
                writer.writeBool(1, this.status);
            if (this.response.length)
                writer.writeString(2, this.response);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRStatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRStatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status = reader.readBool();
                        break;
                    case 2:
                        message.response = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRStatusResponse {
            return APRStatusResponse.deserialize(bytes);
        }
    }
    export class APRTestStartRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            port?: string;
            companion_port?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("port" in data && data.port != undefined) {
                    this.port = data.port;
                }
                if ("companion_port" in data && data.companion_port != undefined) {
                    this.companion_port = data.companion_port;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get port() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set port(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get companion_port() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set companion_port(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            serial_number?: string;
            port?: string;
            companion_port?: string;
        }): APRTestStartRequest {
            const message = new APRTestStartRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.port != null) {
                message.port = data.port;
            }
            if (data.companion_port != null) {
                message.companion_port = data.companion_port;
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                port?: string;
                companion_port?: string;
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.port != null) {
                data.port = this.port;
            }
            if (this.companion_port != null) {
                data.companion_port = this.companion_port;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.port.length)
                writer.writeString(2, this.port);
            if (this.companion_port.length)
                writer.writeString(3, this.companion_port);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRTestStartRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRTestStartRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.port = reader.readString();
                        break;
                    case 3:
                        message.companion_port = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRTestStartRequest {
            return APRTestStartRequest.deserialize(bytes);
        }
    }
    export class APRTestStartResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            test_id?: string;
            status?: string;
            start_time?: number;
            message?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("test_id" in data && data.test_id != undefined) {
                    this.test_id = data.test_id;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("start_time" in data && data.start_time != undefined) {
                    this.start_time = data.start_time;
                }
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
            }
        }
        get test_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set test_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get start_time() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set start_time(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get message() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set message(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        static fromObject(data: {
            test_id?: string;
            status?: string;
            start_time?: number;
            message?: string;
        }): APRTestStartResponse {
            const message = new APRTestStartResponse({});
            if (data.test_id != null) {
                message.test_id = data.test_id;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.start_time != null) {
                message.start_time = data.start_time;
            }
            if (data.message != null) {
                message.message = data.message;
            }
            return message;
        }
        toObject() {
            const data: {
                test_id?: string;
                status?: string;
                start_time?: number;
                message?: string;
            } = {};
            if (this.test_id != null) {
                data.test_id = this.test_id;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.start_time != null) {
                data.start_time = this.start_time;
            }
            if (this.message != null) {
                data.message = this.message;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.test_id.length)
                writer.writeString(1, this.test_id);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (this.start_time != 0)
                writer.writeInt64(3, this.start_time);
            if (this.message.length)
                writer.writeString(4, this.message);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): APRTestStartResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new APRTestStartResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.test_id = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    case 3:
                        message.start_time = reader.readInt64();
                        break;
                    case 4:
                        message.message = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): APRTestStartResponse {
            return APRTestStartResponse.deserialize(bytes);
        }
    }
    interface GrpcUnaryServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
    }
    interface GrpcStreamServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
        (message: P, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
    }
    interface GrpWritableServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
    }
    interface GrpcChunkServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
        (options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
    }
    interface GrpcPromiseServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): Promise<R>;
        (message: P, options?: grpc_1.CallOptions): Promise<R>;
    }
    export abstract class UnimplementedAPRServiceService {
        static definition = {
            ToggleAPR: {
                path: "/apr.v1.APRService/ToggleAPR",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: APRToggleRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => APRToggleRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: APRToggleResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => APRToggleResponse.deserialize(new Uint8Array(bytes))
            },
            APRStatus: {
                path: "/apr.v1.APRService/APRStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: APRStatusRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => APRStatusRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: APRStatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => APRStatusResponse.deserialize(new Uint8Array(bytes))
            },
            APRTestResults: {
                path: "/apr.v1.APRService/APRTestResults",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: APRTestResultRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => APRTestResultRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: APRTestResultResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => APRTestResultResponse.deserialize(new Uint8Array(bytes))
            },
            InitiateAprTest: {
                path: "/apr.v1.APRService/InitiateAprTest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: InitiateAprRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => InitiateAprRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: InitiateAprResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => InitiateAprResponse.deserialize(new Uint8Array(bytes))
            }
        };
        [method: string]: grpc_1.UntypedHandleCall;
        abstract ToggleAPR(call: grpc_1.ServerUnaryCall<APRToggleRequest, APRToggleResponse>, callback: grpc_1.sendUnaryData<APRToggleResponse>): void;
        abstract APRStatus(call: grpc_1.ServerUnaryCall<APRStatusRequest, APRStatusResponse>, callback: grpc_1.sendUnaryData<APRStatusResponse>): void;
        abstract APRTestResults(call: grpc_1.ServerUnaryCall<APRTestResultRequest, APRTestResultResponse>, callback: grpc_1.sendUnaryData<APRTestResultResponse>): void;
        abstract InitiateAprTest(call: grpc_1.ServerUnaryCall<InitiateAprRequest, InitiateAprResponse>, callback: grpc_1.sendUnaryData<InitiateAprResponse>): void;
    }
    export class APRServiceClient extends grpc_1.makeGenericClientConstructor(UnimplementedAPRServiceService.definition, "APRService", {}) {
        constructor(address: string, credentials: grpc_1.ChannelCredentials, options?: Partial<grpc_1.ChannelOptions>) {
            super(address, credentials, options);
        }
        ToggleAPR: GrpcUnaryServiceInterface<APRToggleRequest, APRToggleResponse> = (message: APRToggleRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<APRToggleResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<APRToggleResponse>, callback?: grpc_1.requestCallback<APRToggleResponse>): grpc_1.ClientUnaryCall => {
            return super.ToggleAPR(message, metadata, options, callback);
        };
        APRStatus: GrpcUnaryServiceInterface<APRStatusRequest, APRStatusResponse> = (message: APRStatusRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<APRStatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<APRStatusResponse>, callback?: grpc_1.requestCallback<APRStatusResponse>): grpc_1.ClientUnaryCall => {
            return super.APRStatus(message, metadata, options, callback);
        };
        APRTestResults: GrpcUnaryServiceInterface<APRTestResultRequest, APRTestResultResponse> = (message: APRTestResultRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<APRTestResultResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<APRTestResultResponse>, callback?: grpc_1.requestCallback<APRTestResultResponse>): grpc_1.ClientUnaryCall => {
            return super.APRTestResults(message, metadata, options, callback);
        };
        InitiateAprTest: GrpcUnaryServiceInterface<InitiateAprRequest, InitiateAprResponse> = (message: InitiateAprRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<InitiateAprResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<InitiateAprResponse>, callback?: grpc_1.requestCallback<InitiateAprResponse>): grpc_1.ClientUnaryCall => {
            return super.InitiateAprTest(message, metadata, options, callback);
        };
    }
}

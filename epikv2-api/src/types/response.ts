import { ObjectType, Field } from 'type-graphql';
export interface JSONResponse<T> {
  status: 'success' | 'error';
  data?: T;
  error?: ErrorResponseData;
}

export interface ErrorResponseData {
  message: string;
  statusCode: number;
  code?: string;
  details?: unknown;
  timestamp: string;
  path: string;
  requestId?: string;
}

export interface LiveFieldOptions {
  remoteServiceMethod: string;
  cacheField?: string;
  updateMethod?: string;
}

@ObjectType()
export class PingLine {
  @Field({ nullable: true })
  line?: string;

  @Field({ nullable: true })
  completed?: boolean;
}


import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { NumberDocument } from './number';

@ObjectType()
export class PhoneDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true }) 
  displayName: string;

  @Field(() => String, { nullable: true }) 
  extension: string

  @Field(() => String, { nullable: true }) 
  model: string

  @Field(() => ObjectId, { nullable: true })
  number?: ObjectId;

  @Field(() => NumberDocument, { nullable: true })
  numberDoc?: NumberDocument;

}

export class PhoneModel extends BaseModel<PhoneDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'phones');
    }
}
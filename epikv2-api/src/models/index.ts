import { config } from '@/config/environment';
import { createModuleLogger, dbLogger } from '@/utils/logger';
import mongodb from '@fastify/mongodb';
import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { AlarmRelayModel } from './alarmRelay';
import { BoxTimerModel } from './boxTimer';
import { BoxTimerSessionModel } from './boxTimerSession';
import { CompanyModel } from './company';
import {
  DiagnosticSuiteExecutionModel,
  DiagnosticTestExecutionModel,
  DiagnosticTestModel,
} from './diagnosticTest';
import { EpiModel } from './epi';
import { EpikBoxModel } from './epikbox';
import { NotesModel } from './notes';
import { NumberModel } from './number';
import { PhoneModel } from './phone';
import { PortConfigTemplateModel } from './portConfigTemplate';
import { SystemInfoModel } from './systemInfo';
import { E911HistoryModel } from './e911History';
import { VmailBoxModel } from './VmailBox';

const logger = createModuleLogger('models');

export enum ModelName {
  EPIK_BOX = 'epikboxes',
  Epi = 'obis',
  Company = 'companiesv2',
  Phone = 'phones',
  Number = 'numbers',
  AlarmRelay = 'alarmrelays',
  BoxTimer = 'boxtimers',
  BoxTimerSession = 'boxtimersessions',
  Notes = 'notes',
  PortConfigTemplate = 'portconfigtemplates',
  DiagnosticTest = 'diagnostictests',
  DiagnosticTestExecution = 'diagnostictestexecutions',
  DiagnosticSuiteExecution = 'diagnosticsuiteexecutions',
  SystemInfo = 'systemInfo',
  E911History = 'e911history',
  VmailBox = 'vmailboxes',
}

export type ModelInstanceMap = {
  [ModelName.EPIK_BOX]: EpikBoxModel;
  [ModelName.Epi]: EpiModel;
  [ModelName.Company]: CompanyModel;
  [ModelName.Phone]: PhoneModel;
  [ModelName.Number]: NumberModel;
  [ModelName.AlarmRelay]: AlarmRelayModel;
  [ModelName.BoxTimer]: BoxTimerModel;
  [ModelName.BoxTimerSession]: BoxTimerSessionModel;
  [ModelName.Notes]: NotesModel;
  [ModelName.PortConfigTemplate]: PortConfigTemplateModel;
  [ModelName.DiagnosticTest]: DiagnosticTestModel;
  [ModelName.DiagnosticTestExecution]: DiagnosticTestExecutionModel;
  [ModelName.DiagnosticSuiteExecution]: DiagnosticSuiteExecutionModel;
  [ModelName.SystemInfo]: SystemInfoModel;
  [ModelName.E911History]: E911HistoryModel;
  [ModelName.VmailBox]: VmailBoxModel;
};

// const modelTypeMap: {
//   [K in ModelName]: new (fastify: FastifyInstance) => BaseModel<DocumentTypeMap[K]>
// } = {
//   [ModelName.EPIK_BOX]: EpikBoxModel,
//   [ModelName.Obis]: EpiModel,
// };

const modelRegistry: Partial<ModelInstanceMap> = {};

let isInitialized = false;

export function getModel<T extends ModelName>(name: T): ModelInstanceMap[T] {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  const model = modelRegistry[name] as ModelInstanceMap[T];
  if (!model) {
    const error = new Error(`Model ${name} not found in registry`);
    logger.error(error);
    throw error;
  }

  return model;
}

export function getAllModels(): ModelInstanceMap {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  return modelRegistry as ModelInstanceMap;
}

// function initializeModels(fastify: FastifyInstance): void {
//   if (isInitialized) {
//     logger.warn('Models already initialized, skipping initialization');
//     return;
//   }

//   if (!fastify.mongo || !fastify.mongo.db) {
//     const error = new Error(
//       'MongoDB not available. Register MongoDB plugin before initializing models'
//     );
//     logger.error(error);
//     throw error;
//   }

//   try {
//     const modelNames = Object.values(ModelName);

//     modelNames.forEach(name => {
//       // Get the constructor and create a new instance
//       const ModelConstructor = modelTypeMap[name];
//       modelRegistry[name] = new ModelConstructor(fastify);
//       logger.debug(`Initialized model: ${name}`);
//     });

//     isInitialized = true;
//     logger.info('All models initialized successfully');
//   } catch (error) {
//     logger.error({ error }, 'Failed to initialize models');
//     throw new Error(`Model initialization failed: ${(error as Error).message}`);
//   }
// }

function initializeModels(fastify: FastifyInstance): void {
  console.log({ isInitialized });
  if (isInitialized) {
    logger.warn('Models already initialized, skipping initialization');
    return;
  }

  if (!fastify.mongo || !fastify.mongo.db) {
    const error = new Error(
      'MongoDB not available. Register MongoDB plugin before initializing models'
    );
    logger.error(error);
    throw error;
  }

  try {
    // Explicit initialization to avoid union type inference issues
    modelRegistry[ModelName.Number] = new NumberModel(fastify);
    modelRegistry[ModelName.EPIK_BOX] = new EpikBoxModel(fastify);
    modelRegistry[ModelName.Epi] = new EpiModel(fastify);
    modelRegistry[ModelName.Company] = new CompanyModel(fastify);
    modelRegistry[ModelName.Phone] = new PhoneModel(fastify);
    modelRegistry[ModelName.AlarmRelay] = new AlarmRelayModel(fastify);
    modelRegistry[ModelName.BoxTimer] = new BoxTimerModel(fastify);
    modelRegistry[ModelName.BoxTimerSession] = new BoxTimerSessionModel(fastify);
    modelRegistry[ModelName.Notes] = new NotesModel(fastify);
    modelRegistry[ModelName.PortConfigTemplate] = new PortConfigTemplateModel(fastify);
    modelRegistry[ModelName.DiagnosticTest] = new DiagnosticTestModel(fastify);
    modelRegistry[ModelName.DiagnosticTestExecution] = new DiagnosticTestExecutionModel(fastify);
    modelRegistry[ModelName.DiagnosticSuiteExecution] = new DiagnosticSuiteExecutionModel(fastify);
    modelRegistry[ModelName.SystemInfo] = new SystemInfoModel(fastify);
    modelRegistry[ModelName.E911History] = new E911HistoryModel(fastify);
    modelRegistry[ModelName.VmailBox] = new VmailBoxModel(fastify);

    isInitialized = true;
    logger.info('All models initialized successfully');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize models');
    throw new Error(`Model initialization failed: ${(error as Error).message}`);
  }
}

export const mongodbPlugin = fp(async function (fastify: FastifyInstance) {
  //extract db name from uri e.g mongodb://localhost:27017/epikFax?replicaSet=rs0
  console.log({ mong: 'Connecting to MongoDB' });
  const databaseName =
    config.mongoUri.match(/^mongodb(?:\+srv)?:\/\/[^/]+\/([^?]+)/)?.[1] || 'epikFax';
  dbLogger.info({ databaseName, uri: config.mongoUri }, 'Connecting to MongoDB');
  await fastify.register(mongodb, {
    forceClose: true,
    url: config.mongoUri,
    database: databaseName,
    maxPoolSize: 100,
    connectTimeoutMS: 5000,
    // logLevel: 'debug',
  });

  await initializeModels(fastify);

  fastify.log.info('MongoDB connected and models initialized');
});

export * from './alarmRelay';
export { BaseModel } from './base.model';
export * from './boxTimer';
export * from './boxTimerSession';
export * from './company';
export * from './diagnosticTest';
export * from './epi';
export * from './epikbox';
export * from './location';
export * from './notes';
export * from './number';
export * from './phone';
export * from './portConfigTemplate';
export * from './e911History';

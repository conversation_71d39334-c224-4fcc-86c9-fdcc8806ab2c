import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';


@ObjectType()
export class PortConfigTemplateParams {
  @Field(() => String)
  portType?: string;

  @Field(() => String)
  ataType: 'obi' | 'granite' | 'both';
}

@ObjectType()
export class PortConfigTemplateDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true })
  title?: string;

  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => Boolean, { defaultValue: true })
  default?: boolean;

  @Field(() => String, { defaultValue: 'Medium' })
  SilenceDetectSensitivity?: 'Medium' | 'Low';

  @Field(() => String, { defaultValue: '46' })
  OnHookTipRingVoltage?: string;

  @Field(() => String, { defaultValue: '20' })
  OffHookCurrentMax?: string;

  @Field(() => String, { defaultValue: '3' })
  DTMFDetectMinLength?: string;

  @Field(() => String, { defaultValue: '7' })
  DTMFDetectMinGap?: string;

  @Field(() => String, { defaultValue: '1' })
  ChannelTxGain?: string;

  @Field(() => String, { defaultValue: '1' })
  ChannelRxGain?: string;

  @Field(() => String, { defaultValue: 'auto' })
  DTMFMethod?: string;

  @Field(() => String, { defaultValue: '0' })
  DTMFPlaybackLevel?: string;

  @Field(() => String, { defaultValue: '70' })
  RingVoltage?: string;

  @Field(() => String, { defaultValue: '2' })
  DigitMapShortTimer?: string;

  @Field(() => String, { defaultValue: '500' })
  CPCDelayTime?: string;

  @Field(() => String, { defaultValue: '1000' })
  CPCDuration?: string;

  @Field(() => Boolean, { defaultValue: false })
  showModemModeToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false })
  modemMode?: boolean;

  @Field(() => Boolean, { defaultValue: false })
  showT38EnableToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false })
  t38Enabled?: boolean;

  @Field(() => Boolean, { defaultValue: false })
  showFaxEnabledToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false })
  faxEnabled?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  showJitterDelay?: boolean;

  @Field(() => String, { defaultValue: '100', nullable: true  })
  JitterMinDelay?: string;

  @Field(() => String, { defaultValue: '300', nullable: true })
  JitterMaxDelay?: string;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  inbandRoute?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  showInbandRouteToggle?: boolean;

  @Field(() => String, { nullable: true })
  trunkSelection?: string;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  showTrunkSelectionToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  vadEnable?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  showVadToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  showModemOnlyToggle?: boolean;

  @Field(() => Boolean, { defaultValue: false, nullable: true })
  modemOnly?: boolean;

  @Field(() => String, { defaultValue: 'both', nullable: true })
  ataType?: 'obi' | 'granite' | 'both';

  @Field(() => [String], { nullable: false })
  lineType?: string[];

}

export class PortConfigTemplateModel extends BaseModel<PortConfigTemplateDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'portconfigtemplates');
  }
}

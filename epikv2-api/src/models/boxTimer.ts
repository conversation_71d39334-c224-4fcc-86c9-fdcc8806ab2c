import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';

@ObjectType()
export class BoxTimerDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => ObjectId)
  box: ObjectId;

  @Field(() => ObjectId)
  user: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  session?: ObjectId;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;
}

export class BoxTimerModel extends BaseModel<BoxTimerDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'boxtimers');
  }

  async createWithTTL(doc: Partial<BoxTimerDocument>): Promise<BoxTimerDocument> {
    const collection = this.getCollection();
    
    // Create TTL index if it doesn't exist (expires after 10 minutes)
    await collection.createIndex({ createdAt: 1 }, { expireAfterSeconds: 600 });
    
    const result = await collection.insertOne({
      ...doc,
      createdAt: new Date(),
      _id: new ObjectId()
    } as any);
    
    return { ...doc, _id: result.insertedId, createdAt: new Date() } as BoxTimerDocument;
  }
}

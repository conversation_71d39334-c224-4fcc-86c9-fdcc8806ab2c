import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { IsOptional } from 'class-validator';

@ObjectType()
export class E911Info {
  @Field(() => String, { nullable: true })
  @IsOptional()
  address1: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  address2: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  city: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  state: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  zip: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  country: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  locationid: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  callername: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  vendor: string;
   
  @Field(() => Date, { nullable: true })
  createdAt: Date;

  @Field(() => Date, { nullable: true })
  updatedAt: Date;
}

@ObjectType()
export class E911HistoryDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true })
  @IsOptional()
  number: string;

  @Field(() => E911Info, { nullable: true })
  @IsOptional()
  e911Info?: E911Info;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  date: Date;

  @Field(() => String, { nullable: true })
  @IsOptional()
  comment?: string;
}

export class E911HistoryModel extends BaseModel<E911HistoryDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'e911history');
  }
}


import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';


@ObjectType()
export class APRTestResultResponse {
  @Field(() => String, { nullable: true })
  details?: string;

  @Field(() => String, { nullable: true })
  result?: string;

  @Field(() => String, { nullable: true })
  timestamp?: string;

  @Field(() => String, { nullable: true })
  error?: string;
}

@ObjectType()
export class AlarmRelayDocument implements BaseDocument {
    @Field(() => ID)
    _id?: ObjectId;

   @Field(() => Boolean, { nullable: true })
   isEnabled?: boolean;

   @Field(() => String, { nullable: true }) 
   protoclSelection: string

   @Field(() => String, { nullable: true }) 
   operationMode: string

   @Field(() => Boolean, { nullable: true })
   isTestPassed?: boolean;

   
   @Field(() => Boolean, { nullable: true })
   isTestStarted?: boolean;

   @Field(() => ObjectId, { nullable: true })
   timerSession?: ObjectId;

   @Field(() => Boolean, { nullable: true })
    deleted?: boolean;

   @Field(() => String, { nullable: true }) 
   companionPort: string

}

@ObjectType()
export class APRStatusResponse {
  @Field(() => String, { nullable: true })
  msg?: string;

  @Field(() => Boolean, { nullable: true })
  status?: boolean;
}

export class AlarmRelayModel extends BaseModel<AlarmRelayDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'alarmrelays');
    }
}
import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';


@ObjectType()
export class DiskIo {
    @Field(() => String, { nullable: true })
    Name?: string;

    @Field(() => String, { nullable: true })
    ReadsCompleted?: string;

    @Field(() => String, { nullable: true })
    WritesCompleted?: string;
}
@ObjectType()
export class DiskUsage {
    @Field(() => String, { nullable: true })
    all?: string; // TODO: fix this

    @Field(() => String, { nullable: true })
    avail?: string; // TODO: fix this

    @Field(() => String, { nullable: true })
    error?: string;

    @Field(() => String, { nullable: true })
    free?: string;

    @Field(() => String, { nullable: true })
    size?: string;

    @Field(() => String, { nullable: true })
    used?: string;
}

@ObjectType()
export class LoadAvg {
    @Field(() => String, { nullable: true })
    Loadavg1?: string;

    @Field(() => String, { nullable: true })
    Loadavg5?: string;

    @Field(() => String, { nullable: true })
    Loadavg15?: string;

    @Field(() => String, { nullable: true })
    error?: string;
}

@ObjectType()
export class Uptime {
    @Field(() => String, { nullable: true })
    uptime?: string;
}

@ObjectType()
export class WifiInfo {
    @Field(() => String, { nullable: true })
    ap?: string;

    @Field(() => String, { nullable: true })
    error?: string;

    @Field(() => String, { nullable: true })
    mode?: string;

    @Field(() => String, { nullable: true })
    ssid?: string;
}

@ObjectType()
export class BoxFirmware { // pending
    @Field(() => String, { nullable: true })
    version?: string;
}

@ObjectType()
export class LteSignalStrength {
    @Field(() => String, { nullable: true })
    signalStrength?: string;
}


@ObjectType()
export class ArpTableOptions {
    @Field(() => [String], { nullable: true, description: "List of IP addresses in the ARP table" })
    keys?: string[];

    @Field(() => [String], { nullable: true, description: "List of MAC addresses corresponding to the IPs in the ARP table" })
    values?: string[];

}



@ObjectType()
export class Eth0Config {
    @Field(() => String, { nullable: true })
    nameserver1?: string;
    @Field(() => String, { nullable: true })
    nameserver2?: string;
    @Field(() => String, { nullable: true })
    nameserver3?: string;
    @Field(() => String, { nullable: true })
    nameserver4?: string;
}

@ObjectType()
export class Eth0Stats {
    @Field(() => String, { nullable: true })
    eth0IP?: string;
    @Field(() => String, { nullable: true })
    eth0MAC?: string;
    @Field(() => String, { nullable: true })
    rxErrors?: string;
    @Field(() => String, { nullable: true })
    rxPackets?: string;
    @Field(() => String, { nullable: true })
    txErrors?: string;
    @Field(() => String, { nullable: true })
    txPackets?: string;
}


@ObjectType()
export class LinkStatus {
    @Field(() => String, { nullable: true })
    epiLink?: string;

    @Field(() => String, { nullable: true })
    eth02?: string;
}


@ObjectType()
export class Icmp {
    @Field(() => String, { nullable: true })
    eth0?: string;
    @Field(() => String, { nullable: true })
    wwan0?: string;
}


@ObjectType()
export class PRICardStatus {
    @Field(() => String, { nullable: true })
    priCardStatus?: string;
}


@ObjectType()
export class DeviceFirmwareVersion {
    @Field(() => String, { nullable: true })
    version?: string;
}

@ObjectType()
export class EpiInfo {

    @Field(() => String, { nullable: true })
    fwVersion?: string;

    @Field(() => String, { nullable: true })
    ip?: string;

    @Field(() => String, { nullable: true })
    mac?: string;

    @Field(() => String, { nullable: true })
    provUrl?: string;

    @Field(() => String, { nullable: true })
    uptime?: string;
}


@ObjectType()
export class ModemInfo {
    @Field(() => String, { nullable: true })
    carrier?: string;
    @Field(() => String, { nullable: true })
    imei?: string;
    @Field(() => String, { nullable: true })
    ipAddress?: string;
    @Field(() => String, { nullable: true })
    manufacturer?: string;
    @Field(() => String, { nullable: true })
    model?: string;
    @Field(() => String, { nullable: true })
    sim?: string;
}

@ObjectType()
export class TunnelAccess {
    @Field(() => String, { nullable: true })
    eth0?: string;
    @Field(() => String, { nullable: true })
    wwan0?: string;
}

@ObjectType()
export class DataCenterLatency {
    @Field(() => String, { nullable: true })
    chicago?: string;
    @Field(() => String, { nullable: true })
    losAngeles?: string;
    @Field(() => String, { nullable: true })
    newYork?: string;
    @Field(() => String, { nullable: true })
    atlanta?: string;
    @Field(() => String, { nullable: true })
    dallas?: string;
    @Field(() => String, { nullable: true })
    bestAvailableLatency?: string;
    @Field(() => String, { nullable: true })
    bestAvailableDc?: string;
}

@ObjectType()
export class PowerSource {
    @Field(() => String, { nullable: true })
    powerSource?: string;
}

@ObjectType()
export class EpikEdgeModel {
    @Field(() => String, { nullable: true })
    model?: string;
}

@ObjectType()
export class ManagementAgentStatus {
    @Field(() => String, { nullable: true })
    status?: string;
}

@ObjectType()
export class EpikFailoverUpdateStatus {
    @Field(() => String, { nullable: true })
    status?: string;
}

@ObjectType()
export class EpikRestStatus {
    @Field(() => String, { nullable: true })
    status?: string;
}

@ObjectType()
export class EpikRestVersion {
    @Field(() => String, { nullable: true })
    version?: string;
}

@ObjectType()
export class DcAvgPingDocument {
    @Field(() => String, { nullable: true })
    chPingAvg?: string;

    @Field(() => String, { nullable: true })
    laPingAvg?: string;

    @Field(() => String, { nullable: true })
    nyPingAvg?: string;

    @Field(() => String, { nullable: true })
    atPingAvg?: string;

    @Field(() => String, { nullable: true })
    dlPingAvg?: string;

    @Field(() => String, { nullable: true })
    bestDC?: string;

    @Field(() => String, { nullable: true })
    bestLatency?: string;

    @Field(() => String, { nullable: true })
    timeUpdated?: string;

    @Field(() => String, { nullable: true })
    error?: string;
}

@ObjectType()
export class CpuInfo {
    @Field(() => String, { nullable: true })
    CPUCount?: string;
    @Field(() => String, { nullable: true })
    Guest?: string;
    @Field(() => String, { nullable: true })
    GuestNice?: string;
    @Field(() => String, { nullable: true })
    Idle?: string;
    @Field(() => String, { nullable: true })
    Iowait?: string;
    @Field(() => String, { nullable: true })
    irq?: string;
    @Field(() => String, { nullable: true })
    Nice?: string;
    @Field(() => String, { nullable: true })
    Softirq?: string;
    @Field(() => String, { nullable: true })
    StatCount?: string;
    @Field(() => String, { nullable: true })
    Steal?: string;
    @Field(() => String, { nullable: true })
    System?: string;
    @Field(() => String, { nullable: true })
    Total?: string;
    @Field(() => String, { nullable: true })
    User?: string;
}

@ObjectType()
export class MemoryInfo {
    @Field(() => String, { nullable: true })
    Active?: string;
    @Field(() => String, { nullable: true })
    Available?: string;
    @Field(() => String, { nullable: true })
    Buffers?: string;
    @Field(() => String, { nullable: true })
    Cached?: string;
    @Field(() => String, { nullable: true })
    Free?: string;
    @Field(() => String, { nullable: true })
    Inactive?: string;
    @Field(() => String, { nullable: true })
    MemAvailableEnabled?: string;
    @Field(() => String, { nullable: true })
    SwapCached?: string;
    @Field(() => String, { nullable: true })
    SwapFree?: string;
    @Field(() => String, { nullable: true })
    SwapTotal?: string;
    @Field(() => String, { nullable: true })
    SwapUsed?: string;
    @Field(() => String, { nullable: true })
    Total?: string;
    @Field(() => String, { nullable: true })
    Used?: string;
}

@ObjectType()
export class NetworkInterface {
    @Field(() => String, { nullable: true })
    Name?: string;
    @Field(() => String, { nullable: true })
    RxBytes?: string;
    @Field(() => String, { nullable: true })
    TxBytes?: string;
}

@ObjectType()
export class PortStatuses {
    @Field(() => String, { nullable: true })
    eth0?: string;
    @Field(() => String, { nullable: true })
    eth1?: string;
    @Field(() => String, { nullable: true })
    eth2?: string;
    @Field(() => String, { nullable: true })
    wg0?: string;
    @Field(() => String, { nullable: true })
    wg7?: string;
    @Field(() => String, { nullable: true })
    wlan0?: string;
    @Field(() => String, { nullable: true })
    wwan0?: string;

}

@ObjectType()
export class CdmBoxStatus {
    @Field(() => String, { nullable: true })
    Status?: string;
    @Field(() => String, { nullable: true })
    LastCdmCheck?: string;
    @Field(() => String, { nullable: true })
    Duration?: string;
}

@ObjectType()
export class NetworkInfoDetail {
    @Field(() => String, { nullable: true })
    activeInterface?: string;

    @Field(() => ArpTableOptions, { nullable: true })
    arp?: ArpTableOptions;

    @Field(() => Eth0Config, { nullable: true })
    eth0Config?: Eth0Config;

    @Field(() => Eth0Stats, { nullable: true })
    eth0Stats?: Eth0Stats;

    @Field(() => String, { nullable: true })
    fwVersion?: string;

    @Field(() => Icmp, { nullable: true })
    icmp?: Icmp;

    @Field(() => LinkStatus, { nullable: true })
    linkStatus?: LinkStatus;


    @Field(() => String, { nullable: true })
    modemCount?: string;

    @Field(() => String, { nullable: true })
    edgeIP?: string;

    @Field(() => String, { nullable: true })
    ip4G?: string;

    @Field(() => TunnelAccess, { nullable: true })
    tunnelAccess?: TunnelAccess;

    @Field(() => String, { nullable: true })
    registrationStatus?: string;

    @Field(() => String, { nullable: true })
    tunnelIP?: string;

    @Field(() => String, { nullable: true })
    powerSource?: string;

    @Field(() => String, { nullable: true })
    lteSgnalStrength?: string;

    @Field(() => PRICardStatus, { nullable: true })
    priCardStatus?: PRICardStatus;

    @Field(() => DeviceFirmwareVersion, { nullable: true })
    deviceFirmwareVersion?: DeviceFirmwareVersion;

    @Field(() => [EpiInfo], { nullable: true })
    epiInfo?: EpiInfo[];

    @Field(() => DcAvgPingDocument, { nullable: true })
    dcAvgPing?: DcAvgPingDocument;

    @Field(() => CdmBoxStatus, { nullable: true })
    cdmBoxStatus?: CdmBoxStatus;
}

@ObjectType()
export class SystemInfoDocument implements BaseDocument {
    @Field(() => ID)
    _id?: ObjectId;

    @Field(() => ObjectId, { nullable: true })
    boxId?: ObjectId;

    @Field(() => String, { nullable: true })
    cdmStatus?: string;

    @Field(() => String, { nullable: true })
    deviceFw?: string;

    @Field(() => String, { nullable: true })
    error?: string;

    @Field(() => String, { nullable: true })
    failoverStatus?: string;

    @Field(() => String, { nullable: true })
    failoverUpdateStatus?: string;

    @Field(() => String, { nullable: true })
    restbinStatus?: string;

    @Field(() => [DiskIo], { nullable: true })
    diskIo?: DiskIo[];

    @Field(() => DiskUsage, { nullable: true })
    diskUsage?: DiskUsage;

    @Field(() => LoadAvg, { nullable: true })
    loadavg?: LoadAvg;

    @Field(() => String, { nullable: true })
    restbinVersion?: string;

    @Field(() => String, { nullable: true })
    time?: string;

    @Field(() => Uptime, { nullable: true })
    uptime?: Uptime;

    @Field(() => String, { nullable: true })
    watchGuardStatus?: string;

    @Field(() => WifiInfo, { nullable: true })
    wifiInfo?: WifiInfo;

    @Field(() => BoxFirmware, { nullable: true })// missing
    boxFirmware?: BoxFirmware;

    @Field(() => LteSignalStrength, { nullable: true }) // missing
    lteSignalStrength?: LteSignalStrength;

    @Field(() => NetworkInfoDetail, { nullable: true }) // missing
    networkInfoDetail?: NetworkInfoDetail;

    @Field(() => CpuInfo, { nullable: true })
    cpu?: CpuInfo;

    @Field(() => MemoryInfo, { nullable: true })
    memory?: MemoryInfo;

    @Field(() => [NetworkInterface], { nullable: true })
    network?: NetworkInterface[];

    @Field(() => PortStatuses, { nullable: true })
    portStatuses?: PortStatuses;

    @Field(() => String, { nullable: true })
    lastUpdated?: Date;
}
export class SystemInfoModel extends BaseModel<SystemInfoDocument> {
    constructor(fastify: FastifyInstance) {
        super(fastify, 'systemInfo');
    }
}
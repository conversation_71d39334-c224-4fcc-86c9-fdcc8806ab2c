import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID, registerEnumType } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';

export enum TimerSessionState {
  PENDING = 'Pending',
  FINISHED = 'Finished',
  CANCELLED = 'Cancelled'
}

export enum TimerSessionType {
  ALARM = 'alarm',
  MAINTENANCE = 'maintenance',
  TEST = 'test'
}

registerEnumType(TimerSessionState, {
  name: 'TimerSessionState',
});

registerEnumType(TimerSessionType, {
  name: 'TimerSessionType',
});

@ObjectType()
export class BoxTimerSessionDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => ObjectId)
  box: ObjectId;

  @Field(() => ObjectId)
  user: ObjectId;

  @Field(() => TimerSessionState, { defaultValue: TimerSessionState.PENDING })
  state: TimerSessionState;

  @Field(() => TimerSessionType, { defaultValue: TimerSessionType.ALARM })
  type: TimerSessionType;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;
}

export class BoxTimerSessionModel extends BaseModel<BoxTimerSessionDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'boxtimersessions');
  }

  async findLatestPendingSession(boxId: ObjectId): Promise<BoxTimerSessionDocument | null> {
    const collection = this.getCollection();
    const result = await collection.findOne(
      { 
        box: boxId, 
        state: TimerSessionState.PENDING,
        deleted: { $ne: true }
      },
      { sort: { createdAt: -1 } }
    );
    return result as BoxTimerSessionDocument | null;
  }

  async finishSession(sessionId: ObjectId): Promise<boolean> {
    const collection = this.getCollection();
    const result = await collection.updateOne(
      { _id: sessionId },
      { 
        $set: { 
          state: TimerSessionState.FINISHED,
          updatedAt: new Date()
        }
      }
    );
    return result.modifiedCount > 0;
  }
}

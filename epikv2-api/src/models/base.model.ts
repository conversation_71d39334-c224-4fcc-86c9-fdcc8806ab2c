import { PaginatedResult, PaginationInput } from '@/types';
import { BaseDocument } from '@/types/mongodb';
import logger, { dbLogger, performanceLogger } from '@/utils/logger';
import { FastifyInstance } from 'fastify';
import {
  AggregateOptions,
  BulkWriteOptions,
  BulkWriteResult,
  Collection,
  Document,
  Filter,
  FindOneAndUpdateOptions,
  InsertOneOptions,
  ObjectId,
  OptionalUnlessRequiredId,
  UpdateFilter,
  UpdateOptions,
  WithId,
} from 'mongodb';

export abstract class BaseModel<T extends BaseDocument> {
  protected collection: Collection<T>;
  protected collectionName: string;

  constructor(fastify: FastifyInstance, collectionName: string) {
    if (!fastify.mongo || !fastify.mongo.db) {
      throw new Error('MongoDB not connected');
    }
    this.collectionName = collectionName;
    this.collection = fastify.mongo.db.collection<T>(collectionName);
  }

  async findWithPagination(
    filter: Filter<T> = {},
    options: PaginationInput
  ): Promise<PaginatedResult<T>> {
    const startTime = Date.now();
    try {
      const { page = 1, pageSize = 20 } = options;

      const skip = (page - 1) * pageSize;

      const [docs, totalDocs] = await Promise.all([
        this.collection
          .find(filter, {
            skip,
            limit: pageSize,
          })
          .toArray(),
        this.collection.countDocuments(filter),
      ]);

      const totalPages = Math.ceil(totalDocs / pageSize);

      performanceLogger.info({
        operation: `Find with pagination in ${this.collectionName}`,
        duration: Date.now() - startTime,
        count: docs.length,
        totalDocs,
      });

      return {
        docs: docs as T[],
        pagination: {
          currentPage: page,
          totalPages,
          count: totalDocs,
        },
      };
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
        },
        'Error in findWithPagination'
      );
      throw error;
    }
  }

  async findById(id: String, projection: Document = {}): Promise<WithId<T> | null> {
    const startTime = Date.now();
    try {
      let objectId: ObjectId;

      try {
        objectId = new ObjectId(id as string);
      } catch (error) {
        dbLogger.warn({ id }, 'Invalid ObjectId format');
        return null;
      }

      const doc = await this.collection.findOne(
        { _id: objectId, deleted: { $ne: true } } as Filter<T>,
        { projection }
      );

      performanceLogger.info({
        operation: `FindById in ${this.collectionName}`,
        duration: Date.now() - startTime,
        found: !!doc,
      });

      return doc;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          id,
        },
        'Error in findById'
      );
      throw error;
    }
  }

  /**
   * Find a single document by filter
   */
  async findOne(filter: Filter<T>, projection: Document = {}): Promise<WithId<T> | null> {
    const startTime = Date.now();
    try {
      // Add deleted filter if not explicitly provided
      if (!('deleted' in filter)) {
        filter = { ...filter, deleted: { $ne: true } } as Filter<T>;
      }

      const doc = await this.collection.findOne(filter, { projection });

      performanceLogger.info({
        operation: `FindOne in ${this.collectionName}`,
        duration: Date.now() - startTime,
        found: !!doc,
      });

      return doc;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
        },
        'Error in findOne'
      );
      throw error;
    }
  }

  async findMany(
    filter: Filter<T> = {},
    projection: Document = {},
    preserveOrderByIds?: ObjectId[]
  ): Promise<T[]> {
    const startTime = Date.now();
    try {
      const docs = await this.collection.find(filter, { projection }).toArray();

      performanceLogger.info({
        operation: `FindMany in ${this.collectionName}`,
        duration: Date.now() - startTime,
        count: docs.length,
      });

      if (preserveOrderByIds && preserveOrderByIds.length > 0) {
        const docMap = new Map<string, T>();

        (docs as WithId<T>[]).forEach(doc => {
          if (doc._id) {
            docMap.set(doc._id.toString(), doc as unknown as T);
          }
        });

        return preserveOrderByIds
          .map(id => docMap.get(id.toString()))
          .filter((doc): doc is T => doc !== undefined);
      }

      return docs as T[];
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
        },
        'Error in findMany'
      );
      throw error;
    }
  }

  /**
   * Create a new document
   */
  async create(
    data: Partial<T>,
    options: InsertOneOptions = {}
  ): Promise<OptionalUnlessRequiredId<T>> {
    const startTime = Date.now();
    try {
      const now = new Date();
      const document = {
        ...data,
        creationDate: now,
        lastUpdated: now,
        deleted: false,
      } as OptionalUnlessRequiredId<T>;

      const result = await this.collection.insertOne(document, options);

      performanceLogger.info({
        operation: `Create in ${this.collectionName}`,
        duration: Date.now() - startTime,
      });

      return { ...document, _id: result.insertedId };
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          data,
        },
        'Error in create'
      );
      throw error;
    }
  }

  /**
   * Update a document by ID
   */
  async update(
    id: string,
    data: UpdateFilter<T>,
    options: FindOneAndUpdateOptions = {}
  ): Promise<WithId<T> | null> {
    const startTime = Date.now();
    try {
      let objectId: ObjectId;

      try {
        objectId = new ObjectId(id);
      } catch (error) {
        dbLogger.warn({ id }, 'Invalid ObjectId format');
        return null;
      }

      const safeData = { ...data };
      delete (safeData as any)._id;
      delete (safeData as any).creationDate;
      delete (safeData as any).deleted;

      const result = await this.collection.findOneAndUpdate(
        { _id: objectId, deleted: { $ne: true } } as Filter<T>,
        {
          $set: {
            ...safeData,
            lastUpdated: new Date(),
          },
        } as UpdateFilter<T>,
        options
      );

      performanceLogger.info({
        operation: `Update in ${this.collectionName}`,
        duration: Date.now() - startTime,
        success: !!result,
      });

      return result;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          id,
          data,
        },
        'Error in update'
      );
      throw error;
    }
  }

  /**
   * Update many documents by filter
   */
  async updateMany(
    filter: Filter<T>,
    data: UpdateFilter<T>,
    options: UpdateOptions = {}
  ): Promise<number> {
    const startTime = Date.now();
    try {
      // Add deleted filter if not explicitly provided
      if (!('deleted' in filter)) {
        filter = { ...filter, deleted: { $ne: true } } as Filter<T>;
      }

      // Ensure we don't override system fields
      const safeData = { ...data };
      delete (safeData as any)._id;
      delete (safeData as any).creationDate;
      delete (safeData as any).deleted;

      const result = await this.collection.updateMany(
        filter,
        {
          $set: {
            ...safeData,
            lastUpdated: new Date(),
          },
        } as UpdateFilter<T>,
        options
      );

      performanceLogger.info({
        operation: `UpdateMany in ${this.collectionName}`,
        duration: Date.now() - startTime,
        modifiedCount: result.modifiedCount,
      });

      return result.modifiedCount;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
          data,
        },
        'Error in updateMany'
      );
      throw error;
    }
  }

  /**
   * Soft delete a document by ID
   */
  async delete(id: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      let objectId: ObjectId;

      try {
        objectId = new ObjectId(id);
      } catch (error) {
        dbLogger.warn({ id }, 'Invalid ObjectId format');
        return false;
      }

      const result = await this.collection.updateOne(
        { _id: objectId } as Filter<T>,
        {
          $set: {
            deleted: true,
            lastUpdated: new Date(),
          },
        } as UpdateFilter<T>
      );

      performanceLogger.info({
        operation: `Delete in ${this.collectionName}`,
        duration: Date.now() - startTime,
        success: result.modifiedCount > 0,
      });

      return result.modifiedCount > 0;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          id,
        },
        'Error in delete'
      );
      throw error;
    }
  }

  /**
   * Soft delete many documents by filter
   */
  async deleteMany(filter: Filter<T>): Promise<number> {
    const startTime = Date.now();
    try {
      const result = await this.collection.updateMany(filter, {
        $set: {
          deleted: true,
          lastUpdated: new Date(),
        },
      } as UpdateFilter<T>);

      performanceLogger.info({
        operation: `DeleteMany in ${this.collectionName}`,
        duration: Date.now() - startTime,
        modifiedCount: result.modifiedCount,
      });

      return result.modifiedCount;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
        },
        'Error in deleteMany'
      );
      throw error;
    }
  }

  /**
   * Hard delete a document (permanent removal)
   */
  async hardDelete(id: string): Promise<boolean> {
    const startTime = Date.now();
    try {
      let objectId: ObjectId;

      try {
        objectId = new ObjectId(id);
      } catch (error) {
        dbLogger.warn({ id }, 'Invalid ObjectId format');
        return false;
      }

      const result = await this.collection.deleteOne({ _id: objectId } as Filter<T>);

      performanceLogger.info({
        operation: `HardDelete in ${this.collectionName}`,
        duration: Date.now() - startTime,
        success: result.deletedCount > 0,
      });

      return result.deletedCount > 0;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          id,
        },
        'Error in hardDelete'
      );
      throw error;
    }
  }

  /**
   * Perform an aggregation pipeline
   */
  async aggregate<R extends Document>(
    pipeline: Document[],
    options: AggregateOptions = { allowDiskUse: true }
  ): Promise<R[]> {
    const startTime = Date.now();
    try {
      dbLogger.debug(pipeline);
      const result = await this.collection.aggregate<R>(pipeline, options).toArray();

      performanceLogger.info({
        operation: `Aggregate in ${this.collectionName}`,
        duration: Date.now() - startTime,
        resultCount: result.length,
        pipelineStages: pipeline.length,
      });

      return result;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          pipeline,
        },
        'Error in aggregate'
      );
      throw error;
    }
  }

  /**
   * Perform bulk write operations
   */
  async bulkWrite(operations: any[], options: BulkWriteOptions = {}): Promise<BulkWriteResult> {
    const startTime = Date.now();
    try {
      const result = await this.collection.bulkWrite(operations, options);

      performanceLogger.info({
        operation: `BulkWrite in ${this.collectionName}`,
        duration: Date.now() - startTime,
        operationsCount: operations.length,
        insertedCount: result.insertedCount,
        modifiedCount: result.modifiedCount,
        deletedCount: result.deletedCount,
      });

      return result;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          operationsCount: operations.length,
        },
        'Error in bulkWrite'
      );
      throw error;
    }
  }

  /**
   * Count documents matching a filter
   */
  async count(filter: Filter<T> = {}): Promise<number> {
    const startTime = Date.now();
    try {
      const count = await this.collection.countDocuments(filter);

      performanceLogger.info({
        operation: `Count in ${this.collectionName}`,
        duration: Date.now() - startTime,
        count,
      });

      return count;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          filter,
        },
        'Error in count'
      );
      throw error;
    }
  }
  /**
   * Find a document by id and populate specified fields.
   * @param id - The document id.
   * @param populateFields - Array of field names to populate (must be ObjectId references).
   * @param projection - Optional projection for the main document.
   * @returns The document with populated fields, or null if not found.
   */
  async findByIdWithPopulate(
    id: String,
    populateFields: string[] = [],
    projection: Document = {}
  ): Promise<WithId<T> | null> {
    const startTime = Date.now();
    try {
      let objectId: ObjectId;
      try {
        objectId = new ObjectId(id as string);
      } catch (error) {
        dbLogger.warn({ id }, 'Invalid ObjectId format');
        return null;
      }

      // Find the main document
      const doc = await this.collection.findOne(
        { _id: objectId, deleted: { $ne: true } } as Filter<T>,
        { projection }
      );

      if (!doc) {
        return null;
      }


      // Populate specified fields
      for (let entry of populateFields) {
        const field = entry.split(',')[0];
        const collectionName = entry.split(',')[1];
        if (doc[field] && doc[field]._bsontype === 'ObjectId') {
          console.log('collectionName', collectionName);
          // Access the underlying db from the collection's client
          // @ts-ignore
          const db = (this.collection as any).db || (this.collection as any).s?.db;
          if (!db) {
            throw new Error('Unable to access db from collection');
          }
          const relatedCollection = db.collection(collectionName);
          const relatedDoc = await relatedCollection.findOne({ _id: doc[field] });
          doc[field] = relatedDoc;
        }
      }

      performanceLogger.info({
        operation: `FindByIdWithPopulate in ${this.collectionName}`,
        duration: Date.now() - startTime,
        found: !!doc,
        populatedFields: populateFields,
      });

      return doc as WithId<T>;
    } catch (error) {
      dbLogger.error(
        {
          error,
          collection: this.collectionName,
          id,
          populateFields,
        },
        'Error in findByIdWithPopulate'
      );
      throw error;
    }
  }
}


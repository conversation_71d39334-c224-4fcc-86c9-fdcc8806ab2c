import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';

@ObjectType()
export class CompanyDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  name: string;
}

export class CompanyModel extends BaseModel<CompanyDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'companiesv2');
    }
}
import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID, registerEnumType } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { IsOptional } from 'class-validator';
import { RouteInfo } from '@/types/routeInfo';
import { AlarmRelayDocument } from './alarmRelay';
import { E911Info } from './e911History';

enum CarrierEnum {
  INTELIQUENT = 'inteliquent',
  VOICETEL = 'voicetel',
  TDM = 'tdm',
  VERIZON_TDM = 'verizontdm',
  VERIZON_SIP = 'verizonsip',
  TELNYX = 'telnyx',
  IQ_TOLLFREE = 'iqtollfree',
  NONEPIK_LINE = 'nonepikline',
  SKYT = 'skyt',
}

enum E911CarrierEnum {
  BANDWIDTH = 'bandwidth',
  VOICETEL = 'voicetel',
}

registerEnumType(E911CarrierEnum, {
  name: 'E911CarrierEnum',
});

registerEnumType(CarrierEnum, {
  name: 'CarrierEnum',
});

@ObjectType()
export class NumberDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true })
  @IsOptional()
  number: string;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  linkedBox: ObjectId | null;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  assignedTo: ObjectId | null;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  company: ObjectId;

  @Field(() => String)
  type: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  callerIdName: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  isForwarded?: Boolean;
  
  @Field(() => [String], { nullable: true })
  @IsOptional()
  huntPorts: string[];

  @Field(() => String, { nullable: true })
  @IsOptional()
  huntType: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  portLabel: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  forwardUris: string[];

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  linkedToObi?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  faxDeliveryConfirmation?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  savefaxes?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  confirmationEmail?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  dateCreated?: Date;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  deleted?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  enabled?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  cid?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  emails?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  allowedUsers?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  allowedNumbers?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  groupMembers?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  forwardNumbers?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  fallbackEmails?: string[];

  @Field(() => RouteInfo, { nullable: true })
  @IsOptional()
  routeInfo?: RouteInfo;

  @Field(() => RouteInfo, { nullable: true })
  @IsOptional()
  previousRouteInfo?: RouteInfo;

  @Field(() => String, { nullable: true })
  @IsOptional()
  destination?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  cidOverride?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  port?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  temporary?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  greetingFilePath?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  agreementFilePath?: string;

  @Field(() => CarrierEnum, { nullable: true, defaultValue: CarrierEnum.INTELIQUENT })
  @IsOptional()
  carrier?: CarrierEnum;

 @Field(() => E911CarrierEnum, { nullable: true, defaultValue: E911CarrierEnum.BANDWIDTH })
  @IsOptional()
  e911Carrier?: E911CarrierEnum;

  @Field(() => String, { nullable: true })
  @IsOptional()
  tdmRoute?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  trunk?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  previousTrunk?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  linkedToExternalAta?: boolean;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  alarmRelayInfo: ObjectId;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  inBoundCallBlocked?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  inbandRouting?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  isNumberLocked?: boolean;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  usbPort?: ObjectId;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  onlyEfax?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  isTollFree?: boolean;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  tollFreeNumbers?: string[];

  @Field(() => Date, { nullable: true })
  @IsOptional()
  provisionDate?: Date;

  @Field(() => String, { nullable: true })
  @IsOptional()
  spoofType?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  spoofIncomingCid?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  trunkSelection?: string;

  @Field(() => Number, { nullable: true })
  @IsOptional()
  numDigits?: number;

  @Field(() => E911Info, { nullable: true })
  @IsOptional()
  e911Info?: E911Info;

}

@ObjectType()
export class NumberDocumentPopulated extends NumberDocument {
  @Field(() => AlarmRelayDocument, { nullable: true })
  alarmRelayInfoDoc?: AlarmRelayDocument;
}


export class NumberModel extends BaseModel<NumberDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'numbers');
    }
}
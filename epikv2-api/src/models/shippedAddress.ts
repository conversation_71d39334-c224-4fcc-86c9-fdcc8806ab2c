import { Field, ObjectType } from "type-graphql";

@ObjectType()
export class ShippedAddress {
  @Field(() => String, { nullable: true })
  attn?: string;

  @Field(() => String, { nullable: true })
  street1?: string;

  @Field(() => String, { nullable: true })
  street2?: string;

  @Field(() => String, { nullable: true })
  city?: string;

  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  zip?: string;
}
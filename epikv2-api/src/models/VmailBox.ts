import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID, Int } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { NumberDocumentPopulated } from './number';

@ObjectType()
export class VmailBoxDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, {nullable: true})
  pin?: string;

  @Field(() => Int, { defaultValue: 0, nullable: true })
  totalMessages?: number;

  @Field(() => Int, { nullable: true })
  limit?: number;

  @Field(() => String, { defaultValue: '', nullable: true })
  greeting?: string;

  @Field(() => [String], { defaultValue: [], nullable: true })
  notificationEmails!: string[];

  @Field(() => [String], { defaultValue: [],nullable: true })
  notificationNumbers!: string[];

  @Field(() => ID, { nullable: true })
  number?: ObjectId | null;

  @Field(() => NumberDocumentPopulated, { nullable: true })
  numberDoc?: NumberDocumentPopulated;
  
  @Field(() => Boolean, { defaultValue: false, nullable: true  })
  deleted?: boolean;

  @Field(() => Date, { nullable: true  })
  createdAt?: Date;

  @Field(() => Date, { nullable: true  })
  updatedAt?: Date;
}

export class VmailBoxModel extends BaseModel<VmailBoxDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'vmailboxes');
  }
}

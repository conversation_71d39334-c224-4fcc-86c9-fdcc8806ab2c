import { BaseDocument } from '@/types/mongodb';
import { FastifyInstance } from 'fastify';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { EpikBoxVersion } from './epikbox';

export enum DiagnosticTestStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PASS = 'pass',
  FAILED = 'failed',
  FIXED = 'fixed',
  ERROR = 'error',
  SKIPPED = 'skipped',
}

// Test command definition
export interface DiagnosticTestCommand {
  version: EpikBoxVersion;
  command: string;
  requiresSudo?: boolean;
  timeoutSeconds?: number;
  workingDirectory?: string;
  environment?: Record<string, string>;
}

// Test validation criteria
export interface DiagnosticTestValidation {
  type: string; // 'contains', 'not_contains', 'equals', 'regex', 'custom'
  expectedValue?: string;
  pattern?: string; // For regex validation
  customFunction?: string; // Name of custom validation function
  description?: string;
}

// Fix command definition
export interface DiagnosticFixCommand {
  version: EpikBoxVersion;
  commands: string[];
  requiresSudo?: boolean;
  timeoutSeconds?: number;
  description?: string;
}

// Main diagnostic test document
export interface DiagnosticTestDocument extends BaseDocument {
  _id?: ObjectId;
  name: string;
  displayName: string;
  description?: string;
  testCommands: DiagnosticTestCommand[];
  validations: DiagnosticTestValidation[];
  fixCommands?: DiagnosticFixCommand[];
  category: string;
  severity: string;
  enabled: boolean;
  autoFix: boolean;
  executionOrder: number;
  tags?: string[];
  documentation?: string;
  createdBy: string;
  executionCount: number;
  successCount: number;
  failureCount: number;
  fixSuccessCount: number;
  lastExecuted?: Date;
  creationDate: Date;
  lastUpdated: Date;
  deleted: boolean;
}

// Test execution document
export interface DiagnosticTestExecutionDocument extends BaseDocument {
  _id?: ObjectId;
  testId: ObjectId;
  suiteExecutionId?: ObjectId;
  serialNumber: string;
  userId: ObjectId;
  status: DiagnosticTestStatus;
  output?: string;
  errorMessage?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  fixAttempted?: boolean;
  fixOutput?: string;
  fixErrorMessage?: string;
  testResults?: any;
  creationDate: Date;
  deleted: boolean;
}

// Suite execution document
export interface DiagnosticSuiteExecutionDocument extends BaseDocument {
  _id?: ObjectId;
  serialNumber: string;
  totalTests: number;
  userId: ObjectId;
  creationDate: Date;
  deleted: boolean;
}

export class DiagnosticTestModel extends BaseModel<DiagnosticTestDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'diagnostictests');
  }
}

export class DiagnosticTestExecutionModel extends BaseModel<DiagnosticTestExecutionDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'diagnostictestexecutions');
  }
}

export class DiagnosticSuiteExecutionModel extends BaseModel<DiagnosticSuiteExecutionDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'diagnosticsuiteexecutions');
  }
}

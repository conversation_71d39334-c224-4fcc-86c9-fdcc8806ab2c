import { BaseDocument } from '@/types/mongodb';
import { IsOptional, IsString } from 'class-validator';
import { FastifyInstance } from 'fastify';
import { GraphQLJSON, GraphQLJSONObject } from 'graphql-scalars';
import { ObjectId } from 'mongodb';
import { Field, ID, InputType, ObjectType, registerEnumType } from 'type-graphql';
import { BaseModel } from './base.model';
import { CompanyDocument } from './company';
import { EpiDocumentPopulated } from './epi';
import { LocationDocument } from './location';
import { PhoneDocument } from './phone';
import { PRISettingsDocument } from './PRISettings';
import { CASSettingsDocument } from './CASSettings';
import { SystemInfoDocument, DcAvgPingDocument } from './systemInfo';
/*
modems:{
          label: '',
          type: 'legacy',
          imeis: imei[1],
          sims: [],
          phones: phones.length === 2 ? [phones[1]] : [],
        }
*/

// Enums
export enum BoxRegistrarEnum {
  Unified = 'Unified',
  midRegistrar = 'midRegistrar',
}

registerEnumType(BoxRegistrarEnum, {
  name: 'BoxRegistrarEnum',
});

@ObjectType()
export class ActiveLTESim {
  @Field(() => String, { nullable: true })
  sim?: string;

  @Field(() => String, { nullable: true })
  imei?: string;

  @Field(() => String, { nullable: true })
  mtn?: string;

  @Field(() => String, { nullable: true })
  ip?: string;

  @Field(() => String, { nullable: true })
  iccid?: string;

  @Field(() => Date, { nullable: true })
  timeStamp?: Date;
}

@ObjectType()
export class Modem {
  @Field(() => String, { nullable: true })
  imeis?: string;

  @Field(() => String, { nullable: true })
  label?: string;

  @Field(() => [String], { nullable: true })
  phones?: string[];

  @Field(() => [String], { nullable: true })
  sims?: string[];

  @Field(() => String, { nullable: true })
  type?: string;
}

export enum EpikBoxVersion {
  V2 = 2,
  V3 = 3,
  V3_5 = 3.5,
  V4 = 4,
  ALL = 0,
}

@ObjectType()
export class ModemInfoDocument {
  @Field(() => String, { nullable: true })
  manufacturer_placeholder?: string;

  @Field(() => String, { nullable: true })
  manufacturer?: string;

  @Field(() => String, { nullable: true })
  model_placeholder?: string;

  @Field(() => String, { nullable: true })
  model?: string;

  @Field(() => String, { nullable: true })
  sim_placeholder?: string;

  @Field(() => String, { nullable: true })
  sim?: string;

  @Field(() => String, { nullable: true })
  imei_placeholder?: string;

  @Field(() => String, { nullable: true })
  imei?: string;

  @Field(() => String, { nullable: true })
  carrier_placeholder?: string;

  @Field(() => String, { nullable: true })
  carrier?: string;

  @Field(() => String, { nullable: true })
  ipAddress_placeholder?: string;

  @Field(() => String, { nullable: true })
  ipAddress?: string;
}



@ObjectType()
export class EpiEntry {
  @Field(() => GraphQLJSON)
  data: any;
}


@ObjectType()
export class  WifiStatus {
  @Field(() => String, { nullable: true })
  Error_Msg?: string;
  @Field(() => String, { nullable: true })
  Gateway?: string;
  @Field(() => String, { nullable: true })
  IP: string;
  @Field(() => String, { nullable: true })
  Mode?: string;
  @Field(() => String, { nullable: true })
  Password?: string;
  @Field(() => String, { nullable: true })
  SSID?: string;
  @Field(() => String, { nullable: true })
  Sec_Mode?: string;
  @Field(() => String, { nullable: true })
  Status: string;
  @Field(() => String, { nullable: true })
  Subnet: string;
}


@ObjectType()
class NetworkInfaceObj {
  @Field(() => String, { nullable: true })
  interface?: string;
  @Field(() => String, { nullable: true })
  internet?: string;
  @Field(() => String, { nullable: true })
  icmp?: string;
  @Field(() => String, { nullable: true })
  wg?: string;
}
@ObjectType()
export class NetworkInfo {
  @Field(() => String, { nullable: true })
  dns?: string;
  @Field(() => [NetworkInfaceObj], { nullable: true })
  interfaces?: NetworkInfaceObj[];
  @Field(() => String, { nullable: true })
  timestamp?: string;
  @Field(() => String, { nullable: true })
  error?: string;
}

@ObjectType()
export class Features {
  @Field(() => Boolean, { nullable: true })
  showPriTab?: boolean;

  @Field(() => Boolean, { nullable: true })
  pri?: boolean;

  @Field(() => Boolean, { nullable: true })
  sip?: boolean;

  @Field(() => Boolean, { nullable: true })
  twoModems?: boolean;

  @Field(() => Boolean, { nullable: true })
  daisyChain?: boolean;

  @Field(() => Boolean, { nullable: true })
  dcAutoUpdate?: boolean;
}
@ObjectType()
export class PortForwardObj {
  @Field(() => String, { nullable: true })
  srcIP?: string;

  @Field(() => Number, { nullable: true })
  srcStartPort?: number;

  @Field(() => Number, { nullable: true })
  srcEndPort?: number;

  @Field(() => String, { nullable: true })
  dstIP?: string;

  @Field(() => Number, { nullable: true })
  dstStartPort?: number;

  @Field(() => Number, { nullable: true })
  dstEndPort?: number;

  @Field(() => String, { nullable: true })
  proto?: string;
}

@ObjectType()
export class SensorData {
  @Field(() => String, { nullable: true })
  power?: string;

  @Field(() => String, { nullable: true })
  temp?: string;
}

@ObjectType()
export class DcConnectionStatsResponse {
  @Field(() => GraphQLJSONObject, { nullable: true })
  AT?: any;

  @Field(() => GraphQLJSONObject, { nullable: true })
  CH?: any;

  @Field(() => GraphQLJSONObject, { nullable: true })
  DL?: any;

  @Field(() => GraphQLJSONObject, { nullable: true })
  LA?: any;

  @Field(() => GraphQLJSONObject, { nullable: true })
  NY?: any;

  @Field(() => String, { nullable: true })
  Timestamp?: string;
}
@ObjectType()
export class PortInfo {
  @Field(() => String, { nullable: true })
  port?: string;
  @Field(() => String, { nullable: true })
  calledId?: string;
  @Field(() => String, { nullable: true })
  recording?: string;
  @Field(() => String, { nullable: true })
  trunkType?: string;
}

@ObjectType()
export class VSwitchTab {
  @Field(() => Boolean, { nullable: true })
  registered?: boolean;
  @Field(() => Boolean, { nullable: true })
  registerationConfigCreated?: boolean;
  @Field(() => [String], { nullable: true })
  portsConfigCreated?: string[];
  @Field(() => [PortInfo], { nullable: true })
  portsInfo?: PortInfo[];
}

@ObjectType()
export class SpeedTest {
  @Field(() => String, { nullable: true })
  latency?: string;
  @Field(() => String, { nullable: true })
  jitter?: string;
  @Field(() => String, { nullable: true })
  uploadSpeed?: string;
  @Field(() => String, { nullable: true })
  downloadSpeed?: string;
}


@ObjectType()
export class SimPingInfo {
  @Field(() => String, { nullable: true })
  Error?: string;
  @Field(() => Number, { nullable: true })
  Jitter?: number;
  @Field(() => Number, { nullable: true })
  PacketLoss?: number;
  @Field(() => Number, { nullable: true })
  PingAvg?: number;
  @Field(() => Number, { nullable: true })
  SIM?: number;
}
@ObjectType()
export class FetchLtePerf {
  @Field(() => [SimPingInfo], { nullable: true })
  SimsInfo?: SimPingInfo[];
  @Field(() => String, { nullable: true })
  TimeStamp?: string;
  @Field(() => Date, { nullable: true })
  initiatedTimeStamp?: Date;
}
@ObjectType()
export class EpikBoxDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true })
  displayName?: string;

  @Field(() => String, { nullable: true }) // nullable should be remove later
  serialNumber: string;

  @Field(() => String, { nullable: true }) // nullable should be remove later
  vpnAddress: string;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;

  @Field(() => Date, { nullable: true })
  creationDate?: Date;

  @Field(() => String, { nullable: true })
  customerProvidedIp?: string;

  @Field(() => Number, { nullable: true })
  numPorts?: number;

  @Field(() => Boolean, { nullable: true })
  registered?: boolean;

  @Field(() => String, { nullable: true })
  datacenter?: string;

  @Field(() => Boolean, { nullable: true })
  monitor?: boolean;

  //referances
  @Field(() => ObjectId, { nullable: true })
  assignedTo?: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  locRef?: ObjectId;

  @Field(() => [ObjectId], { nullable: true })
  Obis?: ObjectId[];

  @Field(() => [ObjectId], { nullable: true })
  removedObis?: ObjectId[];

  @Field(() => [ObjectId], { nullable: true })
  phones?: ObjectId[];

  @Field(() => Boolean, { nullable: true })
  deviceOnline?: boolean;

  @Field(() => String, { nullable: true })
  lteIp?: string;

  @Field(() => String, { nullable: true })
  lteIp2?: string;

  @Field(() => String, { nullable: true })
  powerState?: string;

  @Field(() => String, { nullable: true })
  activeCarrier?: string;

  @Field(() => SensorData, { nullable: true })
  sensorData?: SensorData;

  @Field(() => String, { nullable: true })
  signalStrength?: string;

  @Field(() => String, { nullable: true })
  activeInterface?: string;

  @Field(() => Boolean, { nullable: true })
  myEpik?: boolean;

  @Field(() => ModemInfoDocument, { nullable: true })
  modemInfo?: ModemInfoDocument;

  @Field(() => String, { nullable: true })
  lanIp?: string;

  @Field(() => String, { nullable: true })
  publicIp?: string;

  @Field(() => String, { nullable: true })
  simStatus?: string;

  @Field(() => String, { nullable: true })
  model?: string;

  @Field(() => String, { nullable: true })
  fwVersion?: string;

  @Field(() => String, { nullable: true })
  apuType?: string;

  @Field(() => Boolean, { nullable: true })
  powerSaveOption?: boolean;

  @Field(() => Boolean, { nullable: true })
  starCodes?: boolean;

  @Field(() => Boolean, { nullable: true })
  boxOfflineCallForward?: boolean;

  @Field(() => Boolean, { nullable: true })
  recording?: boolean;

  @Field(() => Boolean, { nullable: true })
  OBEnable?: boolean;

  @Field(() => Boolean, { nullable: true })
  eth3Disable?: boolean;

  @Field(() => Boolean, { nullable: true })
  ext_9override?: boolean;

  @Field(() => String, { nullable: true })
  e911Number?: string

  @Field(() => BoxRegistrarEnum, { nullable: true })
  boxRegistrar?: BoxRegistrarEnum = BoxRegistrarEnum.Unified;

  @Field(() => DcAvgPingDocument, { nullable: true })
  dcAvgPing?: DcAvgPingDocument;

  @Field(() => PRISettingsDocument, { nullable: true })
  priSettings?: PRISettingsDocument;

  @Field(() => CASSettingsDocument, { nullable: true })
  sipSettings?: CASSettingsDocument;

  @Field(() => WifiStatus, { nullable: true })
  wifiStatus?: WifiStatus;

  @Field(() => NetworkInfo, { nullable: true })
  networkInfo?: NetworkInfo;

  @Field(() => Features, { nullable: true })
  features?: Features;

  @Field(() => Date, { nullable: true })
  nightlyUpdateTime?: Date;

  @Field(() => [PortForwardObj], { nullable: true })
  portForwardList?: PortForwardObj[];

  @Field(() => [Modem], { nullable: true })
  modems?: [Modem];
  @Field(() => String, { nullable: true })
  priorityInterface?: string;

  @Field(() => String, { nullable: true })
  primarySim?: string;

  @Field(() => Boolean, { nullable: true })
  portAlerts?: boolean;

  @Field(() => String, { nullable: true })
  currentApn?: string;

  @Field(() => String, { nullable: true, defaultValue:'pst' })
  epiTimezone?: string;

  @Field(() => [ActiveLTESim], { nullable: true })
  activeLTE?: ActiveLTESim[];

  @Field(() => Boolean, { nullable: true })
  epikUpdateStatus?: boolean;

  @Field(() => Boolean, { nullable: true })
  isDemoBox?: boolean;

  @Field(() => Boolean, { nullable: true })
  advancedRouting?: boolean;

  @Field(() => DcConnectionStatsResponse, { nullable: true })
  dcConnectionStats?: DcConnectionStatsResponse;

  @Field(() => Boolean, { nullable: true })
  dnsCheck?: boolean;

  @Field(() => VSwitchTab, { nullable: true })
  vSwitchTab?: VSwitchTab;


  @Field(() => GraphQLJSONObject, { nullable: true })
  lteAnalyzer?: any;

  @Field(() => SpeedTest, { nullable: true })
  speedTestData?: SpeedTest;

  @Field(() => SpeedTest, { nullable: true })
  speedTestVoice?: SpeedTest;

  @Field(() => FetchLtePerf, { nullable: true })
  preferredProviderTest?: FetchLtePerf;

}


@ObjectType()
export class EpikBoxDocumentPopulated extends EpikBoxDocument {
  @Field(() => [EpiDocumentPopulated], { nullable: true })
  obiDocs?: EpiDocumentPopulated[];

  @Field(() => [PhoneDocument], { nullable: true })
  phoneDocs?: PhoneDocument[];

  @Field(() => LocationDocument, { nullable: true })
  locationDoc?: LocationDocument;

  @Field(() => CompanyDocument, { nullable: true })
  companyDoc?: CompanyDocument;

  @Field(() => SystemInfoDocument, { nullable: true })
  sysInfo?: SystemInfoDocument;
}

@ObjectType()
export class EdgeDeviceDetails extends EpikBoxDocumentPopulated {
  //why field resolver is not running for this
  @Field(() => String, { nullable: true })
  livePowerState?: String;

  @Field(() => String, { nullable: true })
  liveActiveInterface?: String;

  @Field(() => String, { nullable: true })
  liveLanIp?: String;

  @Field(() => String, { nullable: true })
  livePublicIp?: String;

  @Field(() => String, { nullable: true })
  liveSignalStrength?: String;

  @Field(() => String, { nullable: true })
  liveSimStatus?: String;

  @Field(() => ModemInfoDocument, { nullable: true })
  liveModemInfo?: ModemInfoDocument;

  @Field(() => SensorData, { nullable: true })
  liveSensorData?: SensorData;

  @Field(() => Boolean, { nullable: true })
  liveDeviceOnline?: boolean;

  @Field(() => DcAvgPingDocument, { nullable: true })
  liveDcAvgPing?: DcAvgPingDocument;

  @Field(() => Boolean, { nullable: true })
  liveRegistered?: boolean;

  @Field(() => EpiEntry, { nullable: true })
  liveEpis?: EpiEntry;

  @Field(() => WifiStatus, { nullable: true })
  liveWifiStatus?: WifiStatus;

  @Field(() => NetworkInfo, { nullable: true })
  liveNetworkInfo?: NetworkInfo;

  @Field(() => Date, { nullable: true })
  liveNightlyUpdateTime?: Date;

  @Field(() => [PortForwardObj], { nullable: true })
  livePortForwardList?: PortForwardObj[];

  @Field(() => String, { nullable: true })
  livePriorityInterface?: string;

  @Field(() => String, { nullable: true })
  livePrimarySim?: string;

  @Field(() => String, { nullable: true })
  liveCurrentApn?: string;


  @Field(() => Boolean, { nullable: true })
  liveEpikUpdateStatus?: boolean;

  @Field(() => SystemInfoDocument, { nullable: true })
  liveSysInfo?: SystemInfoDocument;

  @Field(() => DcConnectionStatsResponse, { nullable: true })
  liveDcConnectionStats?: DcConnectionStatsResponse;

  @Field(() => Boolean, { nullable: true })
  liveDnsCheck?: boolean;

  @Field(() => VSwitchTab, { nullable: true })
  liveVSwitchTab?: VSwitchTab;


  @Field(() => GraphQLJSONObject, { nullable: true })
  liveLteAnalyzer?: any;

  @Field(() => SpeedTest, { nullable: true })
  liveSpeedTestData?: SpeedTest;

  @Field(() => SpeedTest, { nullable: true })
  liveSpeedTestVoice?: SpeedTest;

  @Field(() => FetchLtePerf, { nullable: true })
  livePreferredProviderTest?: FetchLtePerf;

}

@ObjectType()
export class LiveFieldUpdate {
  @Field(() => String)
  fieldName: string;

  @Field(() => GraphQLJSON, { nullable: true }) // or GraphQLJSONObject for mixed types
  value?: any;

  @Field(() => String, { nullable: true }) // or GraphQLJSONObject for mixed types
  error?: string;

  @Field(() => ID)
  id: String;
}

// Input Types
@InputType()
export class EpikBoxFilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  query?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  assignedNumber?: string[];

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  displayName?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  company?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  number?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  macAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  epiNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  shipingNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  imei?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sim?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdon?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  companyName?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  isAll?: boolean;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  ids?: string[];
}

@InputType()
export class UpdateEpikBoxInput
  implements Partial<Pick<EpikBoxDocument, 'serialNumber' | 'vpnAddress'>>
{
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  lastSeen?: Date;
}

export class EpikBoxModel extends BaseModel<EpikBoxDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'epikboxes');
  }
  getDeviceVersion(device: EpikBoxDocument): EpikBoxVersion {
    if (device.model === 'G4-4100') {
      return EpikBoxVersion.V4;
    }
    if (device.vpnAddress) {
      const thirdOctet = parseInt(device.vpnAddress.split('.')[2], 10);
      if (thirdOctet < 20) {
        return EpikBoxVersion.V2;
      }
      return EpikBoxVersion.V3;
    }
    return EpikBoxVersion.V3;
  }
}

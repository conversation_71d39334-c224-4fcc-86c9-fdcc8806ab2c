import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID, registerEnumType } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { IsOptional } from 'class-validator';
import { NumberDocumentPopulated } from './number';
import { PortConfigTemplateDocument } from './portConfigTemplate';
import { VmailBoxDocument } from './VmailBox';


export enum SilenceDetectSensitivityEnum {
  Medium = 'Medium',
  Low = 'Low',
}
registerEnumType(SilenceDetectSensitivityEnum, {
  name: 'SilenceDetectSensitivityEnum',
});

@ObjectType()
export class WanInfo {
  @Field(() => String, { nullable: true })
  placeHolder?: string;

  @Field(() => String, { nullable: true })
  ip?: string;

  @Field(() => String, { nullable: true })
  subnet?: string;

  @Field(() => String, { nullable: true })
  gateway?: string;

  @Field(() => String, { nullable: true })
  dns?: string;
}

@ObjectType()
export class Sp1erviceStatus {

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => String, { nullable: true })
  callState?: string;
}

@ObjectType()
export class Sp2erviceStatus {

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => String, { nullable: true })
  callState?: string;
}

@ObjectType()
export class ObiTalkServiceStatus {
  @Field(() => String, { nullable: true })
  placeHolder?: string;

  @Field(() => String, { nullable: true })
  status?: string;
}

@ObjectType()
export class EpiRegistrationMeta {

  @Field(() => WanInfo, { nullable: true })
  wanInfo?: WanInfo;

  @Field(() => Sp1erviceStatus, { nullable: true })
  sp1ServiceStatus?: Sp1erviceStatus;

  @Field(() => Sp2erviceStatus, { nullable: true })
  sp2ServiceStatus?: Sp2erviceStatus;

  @Field(() => ObiTalkServiceStatus, { nullable: true })
  obiTalkServiceStatus?: ObiTalkServiceStatus;
}

@ObjectType()
export class PortPhysicalMeta {
  @Field(() => String, { nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  state?: string;

  @Field(() => String, { nullable: true })
  loopCurrent?: string;

  @Field(() => String, { nullable: true })
  Vbat?: string;

  @Field(() => String, { nullable: true })
  tipRingVoltage?: string;

  @Field(() => String, { nullable: true })
  lastCallerInfo?: string;
}

@ObjectType()
export class EpiPort {
  @Field(() => String, { nullable: true })
  @IsOptional()
  boxPortNumber: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  registered: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  disabled: boolean;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  provisionDate?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  enableDisableLastUpdatedTime?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  updatedOn?: Date;

  @Field(() => String, { nullable: true })
  @IsOptional()
  serviceName?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  monitored?: Boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  callerIdMasking?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  callerIdMaskingNumber?: string;

   @Field(() => SilenceDetectSensitivityEnum, { nullable: true })
  @IsOptional()
  SilenceDetectSensitivity: SilenceDetectSensitivityEnum = SilenceDetectSensitivityEnum.Medium;

  @Field(() => String, { nullable: true })
  @IsOptional()
  OnHookTipRingVoltage?: string = '46';

  @Field(() => String, { nullable: true })
  @IsOptional()
  OffHookCurrentMax?: string = '20';

  @Field(() => String, { nullable: true })
  @IsOptional()
  DTMFDetectMinLength?: string = '3';

  @Field(() => String, { nullable: true })
  @IsOptional()
  DTMFDetectMinGap?: string = '7';

  @Field(() => String, { nullable: true })
  @IsOptional()
  ChannelTxGain?: string = '1';

  @Field(() => String, { nullable: true })
  @IsOptional()
  ChannelRxGain?: string = '1';

  @Field(() => String, { nullable: true })
  @IsOptional()
  DTMFMethod?: string = 'auto';

  @Field(() => String, { nullable: true })
  @IsOptional()
  DTMFPlaybackLevel?: string = '0';

  @Field(() => String, { nullable: true })
  @IsOptional()
  RingVoltage?: string = '70';

  @Field(() => String, { nullable: true })
  @IsOptional()
  DigitMapShortTimer?: string = '2';

  @Field(() => String, { nullable: true })
  @IsOptional()
  CPCDelayTime?: string = '500';

  @Field(() => String, { nullable: true })
  @IsOptional()
  CPCDuration?: string ='1000';

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showVadToggle?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showModemModeToggle?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  modemMode?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showT38EnableToggle?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  t38Enabled?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showFaxEnabledToggle?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  faxEnabled?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showJitterDelay?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  JitterMinDelay?: string = "100";

  @Field(() => String, { nullable: true })
  @IsOptional()
  JitterMaxDelay?: string = "300";

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  showModemOnlyToggle?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  modemOnly?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  forceForward?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  forceForwardNumber?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  configOverride?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  e911Number?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  e911Enabled?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  voip?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  status?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  connected?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  remoteHost?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  ipAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  remotePort?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  authName?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  assignedNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  vmNumber?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  advancedModemConfiguration?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  portActivated?: boolean;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  portActivatedDate?: Date;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  startedBilling?: boolean;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  startedBillingDate?: Date;

  @Field(() => String, { nullable: true })
  @IsOptional()
  callerIdOverride?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  provisionUrl?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  blockCallerId?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  callWaiting?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  configPassword?: string;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  assignedNumberRef?: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  appliedConfigTemplate?: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  vmBox?: ObjectId;

}

@ObjectType()
export class EpiPortPopulated extends EpiPort {
  @Field(() => NumberDocumentPopulated, { nullable: true })
  assignedNumberDoc?: NumberDocumentPopulated;
  
  @Field(() => PortConfigTemplateDocument, { nullable: true })
  appliedConfigTemplateDoc?: PortConfigTemplateDocument;

  @Field(() => VmailBoxDocument, { nullable: true })
  vmailBoxDoc?: VmailBoxDocument;
}

@ObjectType()
export class EpiDocumentBase implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  deviceId: string;

  @Field(() => String, { nullable: true })
  obiNumber: string;

  @Field(() => String, { nullable: true })
  macAddress: string;

  @Field(() => ObjectId, { nullable: true })
  assignedTo: ObjectId;

  @Field(() => EpiRegistrationMeta, { nullable: true })
  registrationMeta?: EpiRegistrationMeta; 

  @Field(() => [PortPhysicalMeta], { nullable: true })
  portPhysicalMeta?: PortPhysicalMeta[];
}

@ObjectType()
export class EpiDocument extends EpiDocumentBase {
  @Field(() => EpiPort)
  port1: EpiPort;

  @Field(() => EpiPort)
  port2: EpiPort;
}

@ObjectType()
export class EpiDocumentPopulated extends EpiDocumentBase {
  @Field(() => EpiPortPopulated, { nullable: true })
  @IsOptional()
  port1: EpiPortPopulated;

  @Field(() => EpiPortPopulated, { nullable: true })
  @IsOptional()
  port2: EpiPortPopulated;

  @Field(() => EpiRegistrationMeta, { nullable: true })
  liveRegistrationMeta?: EpiRegistrationMeta;

  @Field(() => [PortPhysicalMeta], { nullable: true })
  livePortPhysicalMeta?: PortPhysicalMeta[];
}

export class EpiModel extends BaseModel<EpiDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'obis');
  }
}

export class EpiParams {
  @Field(() => String, { nullable: true })
  portNumber: string;
}
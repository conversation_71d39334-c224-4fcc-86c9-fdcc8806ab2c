import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID, registerEnumType } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';

export enum AttachmentStatus {
  YES = 'Yes',
  NO = 'No'
}

registerEnumType(AttachmentStatus, {
  name: 'AttachmentStatus',
});

@ObjectType()
export class NotesDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => ObjectId)
  user: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  epikBox?: ObjectId;

  @Field(() => ObjectId, { nullable: true })
  number?: ObjectId;

  @Field(() => String)
  note: string;

  @Field(() => AttachmentStatus, { defaultValue: AttachmentStatus.NO })
  hasAttachment: AttachmentStatus;

  @Field(() => String, { nullable: true })
  attachmentPath?: string;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;
}

export class NotesModel extends BaseModel<NotesDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'notes');
  }

  async createNote(note: Partial<NotesDocument>): Promise<NotesDocument> {
    const collection = this.getCollection();
    const result = await collection.insertOne({
      ...note,
      _id: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      hasAttachment: note.hasAttachment || AttachmentStatus.NO,
      deleted: false
    } as any);
    
    return { ...note, _id: result.insertedId } as NotesDocument;
  }
}

import { config } from '@/config/environment';
import Fastify, { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

import compress from '@fastify/compress';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
// @ts-expect-error: No type definitions for '@fastify/rate-limit'
import rateLimit from '@fastify/rate-limit';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';

import { authMiddleware } from '@/middleware/auth';
import { errorHandler } from '@/middleware/errorHandler';
import {
  correlationMiddleware,
  requestLogger,
  securityHeadersMiddleware,
} from '@/middleware/requestLogger';

import { graphqlPlugin } from '@/graphql/graphql';
import '@/middleware/permissionClient';
import { permissionPlugin } from '@/middleware/permissionClient';
import { healthRoutes } from '@/routes/health';
import { mongodbPlugin } from './models';
import { epikboxRoutes } from './routes/epikbox';
import { voiceMailRoutes } from './routes/voicemail';
import { servicesPlugin } from './services';
import { numberRoutes } from './routes/numberRouter';
import { alarmRelayRoutes } from './routes/alarmRelayRouter';
import { portConfigTemplateRoutes } from './routes/portConfigTemplate';
import { epiRoutes } from './routes/epi';

export async function createApp(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: false,
    trustProxy: false,
  });

  await app.register(mongodbPlugin);

  const cspDirectives = config.devMode
    ? {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'http://dev.epik.io'],
      }
    : {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'"],
      };

  await app.register(helmet, {
    contentSecurityPolicy: {
      directives: cspDirectives,
    },
  });

  await app.register(cors, {
    origin: (origin, cb) => {
      if (config.devMode) return cb(null, true);
      if (!origin) return cb(null, true);
      const allowedOrigins = config.corsOrigins;
      if (allowedOrigins.includes(origin)) {
        return cb(null, true);
      }
      const wildcardMatches = allowedOrigins
        .filter(allowed => allowed.includes('*'))
        .some(pattern => {
          const regexPattern = pattern
            .replace('*.', '(.+\\.)?')
            .replace(/\./g, '\\.')
            .replace(/\//g, '\\/');
          return new RegExp(`^${regexPattern}$`).test(origin);
        });
      if (wildcardMatches) {
        return cb(null, true);
      }
      cb(new Error(`Origin not allowed: ${origin}`), false);
    },
    credentials: !config.devMode,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  });

  await app.register(compress, {
    global: true,
    threshold: 1024,
    encodings: ['gzip', 'deflate'],
  });

  await app.register(rateLimit, {
    max: config.rateLimitMax,
    timeWindow: config.rateLimitWindow,
    errorResponseBuilder: () => ({
      error: 'Too Many Requests',
      message: 'Rate limit exceeded',
      statusCode: 429,
    }),
  });

  await app.register(swagger, {
    openapi: {
      info: {
        title: 'EpikV2 API',
        version: '1.0.0',
      },
      servers: [
        {
          url: `http://localhost:${config.port}`,
        },
      ],
    },
  });

  await app.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'full',
      deepLinking: false,
    },
    staticCSP: true,
    transformStaticCSP: (header: string) => header,
  });
  await app.register(servicesPlugin);
  await app.register(requestLogger);
  await app.register(correlationMiddleware);
  await app.register(securityHeadersMiddleware);
  await app.register(errorHandler);
  await app.register(authMiddleware);
  await app.register(permissionPlugin);
  //all routes
  await app.register(
    async function (app: FastifyInstance) {
      await app.register(graphqlPlugin);
      await app.register(healthRoutes, { prefix: '/health' });
      await app.register(epikboxRoutes, { prefix: '/epikbox' });
      await app.register(numberRoutes, { prefix: '/number' });
      await app.register(alarmRelayRoutes, { prefix: '/alarmRelay' });
      await app.register(portConfigTemplateRoutes, { prefix: '/portConfig' });
      await app.register(epiRoutes, { prefix: '/epi' });
    },
    { prefix: '/apps/epikv2-api' }
  );

  app.setNotFoundHandler((_request: FastifyRequest, reply: FastifyReply) => {
    reply.status(404).send({
      status: 'error',
      data: { notfound: true },
    });
  });
  console.log('App created');
  return app;
}

{"name": "epikv2-api", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "vitest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "migration": "tsx -r dotenv/config scripts/run-migration.ts", "generate-proto": "tsx scripts/proto-generator.ts", "generate-proto:force": "tsx scripts/proto-generator.ts --force"}, "dependencies": {"@fastify/compress": "^8.0.1", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.0.1", "@fastify/mongodb": "^9.0.2", "@fastify/multipart": "^9.0.3", "@fastify/rate-limit": "^10.1.1", "@fastify/swagger": "^9.1.0", "@fastify/swagger-ui": "^5.0.1", "@grpc/grpc-js": "^1.10.2", "@grpc/proto-loader": "^0.7.0", "@types/google-protobuf": "^3.15.12", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "class-validator": "^0.14.1", "dotenv": "^16.4.7", "email-validator": "^2.0.4", "fastify": "^5.1.0", "fastify-plugin": "^5.0.1", "ffmpeg-static": "^5.2.0", "google-protobuf": "^3.21.4", "graphql": "^16.8.1", "graphql-subscriptions": "^3.0.0", "grpc-reflection-js": "^0.1.2", "mercurius": "^16.1.0", "mongodb": "^6.10.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "type-graphql": "2.0.0-rc.2", "xml2js": "^0.6.2", "zod": "^3.24.1"}, "devDependencies": {"@parcel/watcher": "^2.5.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.10.5", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "mongodb-memory-server": "^10.1.4", "pm2": "^5.4.3", "prettier": "^3.4.2", "protoc-gen-ts": "^0.8.7", "rimraf": "^6.0.1", "supertest": "^7.0.0", "tsc-alias": "^1.8.10", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
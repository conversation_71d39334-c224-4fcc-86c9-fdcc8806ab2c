# REST Streaming Diagnostic API

This document provides examples of how to use the REST streaming diagnostic API.

## 🚀 **Available Endpoints**

### **POST** `/apps/epikv2-api/epikboxes/:serialNumber/diagnostics/start`
Start diagnostic tests with real-time streaming updates

### **POST** `/apps/epikv2-api/epikboxes/:serialNumber/diagnostics/fix`
Attempt to fix a specific failed test with streaming updates

### **GET** `/apps/epikv2-api/epikboxes/:serialNumber/diagnostics/tests`
Get available diagnostic tests for a device

### **GET** `/apps/epikv2-api/epikboxes/:serialNumber/diagnostics/history`
Get diagnostic execution history

## 📡 **Streaming Examples**

### 1. Start Diagnostic Tests

```bash
curl -X POST "http://localhost:3000/apps/epikv2-api/epikboxes/EP12345/diagnostics/start" \
  -H "Content-Type: application/json" \
  -d '{"userId": "admin"}'
```

**Streaming Response:**
```
data: {"event":"connected","message":"Diagnostic stream started","timestamp":"2024-01-15T10:30:00.000Z"}

data: {"event":"diagnostics-EP12345","data":{"testName":"vswitch_container_test","status":"running"},"timestamp":"2024-01-15T10:30:01.000Z"}

data: {"event":"diagnostics-EP12345","data":{"testName":"vswitch_container_test","status":"pass","duration":2500},"timestamp":"2024-01-15T10:30:03.500Z"}

data: {"event":"diagnostics-EP12345","data":{"testName":"disk_space_test","status":"running"},"timestamp":"2024-01-15T10:30:04.000Z"}

data: {"event":"completed","message":"All diagnostics completed","timestamp":"2024-01-15T10:30:45.000Z"}
```

### 2. Fix Failed Test

```bash
curl -X POST "http://localhost:3000/apps/epikv2-api/epikboxes/EP12345/diagnostics/fix" \
  -H "Content-Type: application/json" \
  -d '{"testName": "vswitch_container_test", "userId": "admin"}'
```

**Streaming Response:**
```
data: {"event":"connected","message":"Fix attempt started for test: vswitch_container_test","timestamp":"2024-01-15T10:30:00.000Z"}

data: {"event":"diagnostics-EP12345","data":{"testName":"vswitch_container_test_fix","status":"trying to fix"},"timestamp":"2024-01-15T10:30:01.000Z"}

data: {"event":"diagnostics-EP12345","data":{"testName":"vswitch_container_test","status":"fixed","duration":5000},"timestamp":"2024-01-15T10:30:06.000Z"}

data: {"event":"fix_completed","message":"Fix attempt completed for test: vswitch_container_test","timestamp":"2024-01-15T10:30:06.500Z"}
```

This REST streaming approach gives you real-time updates without the complexity of GraphQL subscriptions!
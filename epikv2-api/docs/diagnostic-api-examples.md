# GraphQL Diagnostic API Guide

This document provides comprehensive examples of how to use the GraphQL diagnostic API with queries, mutations, and real-time subscriptions.

## 🚀 **GraphQL-Only Solution**

Our diagnostic API is built entirely on GraphQL, providing:
- **Type-safe operations** with full TypeScript support
- **Real-time subscriptions** for live diagnostic updates
- **Flexible queries** - request exactly what you need
- **Async operations** for long-running diagnostics
- **Single endpoint** - `/graphql` for everything

## 📊 **Available Operations**

### **Queries**
- `diagnosticTests` - Get available tests for a device
- `diagnosticTest` - Get specific test details
- `diagnosticHistory` - Get test execution history
- `diagnosticSuiteHistory` - Get suite execution history
- `diagnosticExecution` - Get specific execution details

### **Mutations**
- `executeDiagnosticTest` - Execute single test (sync)
- `executeDiagnosticTestAsync` - Execute single test (async)
- `executeDiagnosticSuite` - Execute test suite (sync with real-time updates)
- `executeDiagnosticSuiteAsync` - Execute test suite (async)
- `fixDiagnosticTest` - Fix failed test

### **Subscriptions**
- `diagnosticUpdates` - All diagnostic events for a device
- `diagnosticTestUpdates` - Specific test updates
- `diagnosticSuiteUpdates` - Suite execution updates
- `diagnosticAsyncUpdates` - Async operation updates
- `diagnosticFilteredUpdates` - Filtered events

## 🔍 **Query Examples**

### 1. Get Available Diagnostic Tests

```graphql
query GetDiagnosticTests($serialNumber: String, $category: DiagnosticTestCategory) {
  diagnosticTests(serialNumber: $serialNumber, category: $category) {
    _id
    name
    displayName
    description
    category
    severity
    enabled
    autoFix
    executionOrder
    tags
    executionCount
    successCount
    failureCount
    lastExecuted
  }
}
```

Variables:
```json
{
  "serialNumber": "EP12345",
  "category": "VOICE"
}
```

### 2. Get Specific Test Details

```graphql
query GetDiagnosticTest($testId: String!) {
  diagnosticTest(testId: $testId) {
    _id
    name
    displayName
    description
    category
    severity
    testCommands {
      version
      command
      requiresSudo
      timeoutSeconds
    }
    validations {
      type
      expectedValue
      description
    }
    fixCommands {
      version
      commands
      description
    }
    enabled
    autoFix
    documentation
  }
}
```

### 3. Get Execution History

```graphql
query GetDiagnosticHistory($filter: DiagnosticHistoryFilter!) {
  diagnosticHistory(filter: $filter) {
    _id
    testId
    testName
    serialNumber
    userId
    status
    output
    errorMessage
    startTime
    endTime
    duration
    fixAttempted
    fixOutput
    testResults
  }
}
```

Variables:
```json
{
  "filter": {
    "serialNumber": "EP12345",
    "limit": 10,
    "skip": 0
  }
}
```

## 🔧 **Mutation Examples**

### 1. Execute Single Diagnostic Test (Synchronous)

```graphql
mutation ExecuteTest($input: ExecuteTestInput!, $userId: String) {
  executeDiagnosticTest(input: $input, userId: $userId) {
    testId
    testName
    status
    output
    errorMessage
    duration
    fixAttempted
    fixOutput
    fixErrorMessage
    executedAt
    executionId
  }
}
```

Variables:
```json
{
  "input": {
    "testId": "64f1234567890abcdef12345",
    "serialNumber": "EP12345",
    "autoFix": true
  },
  "userId": "admin"
}
```

### 2. Execute Single Test Asynchronously

```graphql
mutation ExecuteTestAsync($input: ExecuteTestInput!, $userId: String) {
  executeDiagnosticTestAsync(input: $input, userId: $userId) {
    operationId
    status
    message
    startedAt
  }
}
```

### 3. Execute Diagnostic Suite with Real-time Updates

```graphql
mutation ExecuteSuite($input: ExecuteSuiteInput!, $userId: String) {
  executeDiagnosticSuite(input: $input, userId: $userId) {
    suiteExecutionId
    serialNumber
    overallStatus
    totalTests
    passedTests
    failedTests
    fixedTests
    testResults {
      testId
      testName
      status
      output
      duration
      fixAttempted
    }
    duration
    startTime
    endTime
    userId
  }
}
```

Variables:
```json
{
  "input": {
    "serialNumber": "EP12345",
    "autoFix": true,
    "stopOnFailure": false,
    "categories": ["VOICE", "NETWORK"],
    "severities": ["HIGH", "CRITICAL"]
  },
  "userId": "admin"
}
```

### 4. Execute Suite Asynchronously

```graphql
mutation ExecuteSuiteAsync($input: ExecuteSuiteInput!, $userId: String) {
  executeDiagnosticSuiteAsync(input: $input, userId: $userId) {
    operationId
    status
    message
    startedAt
  }
}
```

## 📡 **Subscription Examples**

### 1. Subscribe to All Diagnostic Updates

```graphql
subscription DiagnosticUpdates($serialNumber: String!) {
  diagnosticUpdates(serialNumber: $serialNumber) {
    event
    serialNumber
    testName
    status
    output
    errorMessage
    duration
    timestamp
    executionId
    fixAttempted
    fixOutput
    progress
    suiteExecutionId
  }
}
```

Variables:
```json
{
  "serialNumber": "EP12345"
}
```

### 2. Subscribe to Specific Test Updates

```graphql
subscription TestUpdates($serialNumber: String!, $testId: String) {
  diagnosticTestUpdates(serialNumber: $serialNumber, testId: $testId) {
    event
    testName
    status
    output
    errorMessage
    duration
    timestamp
    fixAttempted
  }
}
```

### 3. Subscribe to Suite Execution Updates

```graphql
subscription SuiteUpdates($serialNumber: String!) {
  diagnosticSuiteUpdates(serialNumber: $serialNumber) {
    event
    serialNumber
    testName
    status
    output
    duration
    timestamp
    suiteExecutionId
    progress
  }
}
```

### 4. Subscribe to Async Operation Updates

```graphql
subscription AsyncUpdates($serialNumber: String!) {
  diagnosticAsyncUpdates(serialNumber: $serialNumber) {
    event
    serialNumber
    testName
    status
    output
    errorMessage
    duration
    timestamp
    executionId
  }
}
```

## 🔧 **Client Integration Examples**

### React Hook with Apollo Client

```typescript
import { useQuery, useMutation, useSubscription } from '@apollo/client';
import { useState, useEffect } from 'react';

const DIAGNOSTIC_UPDATES = gql`
  subscription DiagnosticUpdates($serialNumber: String!) {
    diagnosticUpdates(serialNumber: $serialNumber) {
      event
      testName
      status
      output
      errorMessage
      duration
      timestamp
    }
  }
`;

const EXECUTE_SUITE = gql`
  mutation ExecuteSuite($input: ExecuteSuiteInput!) {
    executeDiagnosticSuite(input: $input) {
      suiteExecutionId
      overallStatus
      totalTests
      passedTests
      failedTests
      fixedTests
    }
  }
`;

export function useDiagnostics(serialNumber: string) {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const { data: updateData } = useSubscription(DIAGNOSTIC_UPDATES, {
    variables: { serialNumber }
  });

  const [executeSuite] = useMutation(EXECUTE_SUITE);

  useEffect(() => {
    if (updateData?.diagnosticUpdates) {
      const update = updateData.diagnosticUpdates;

      if (update.event === 'suite_started') {
        setIsRunning(true);
        setTestResults([]);
      } else if (update.event === 'test_completed') {
        setTestResults(prev => [...prev, update]);
      } else if (update.event === 'suite_completed') {
        setIsRunning(false);
      }
    }
  }, [updateData]);

  const runDiagnostics = async () => {
    try {
      await executeSuite({
        variables: {
          input: {
            serialNumber,
            autoFix: true,
            stopOnFailure: false
          }
        }
      });
    } catch (error) {
      console.error('Failed to run diagnostics:', error);
      setIsRunning(false);
    }
  };

  return {
    testResults,
    isRunning,
    runDiagnostics
  };
}
```

### Vue.js Composition API

```typescript
import { ref, watch } from 'vue';
import { useSubscription, useMutation } from '@vue/apollo-composable';

export function useDiagnostics(serialNumber: string) {
  const testResults = ref([]);
  const isRunning = ref(false);

  const { result: updateResult } = useSubscription(DIAGNOSTIC_UPDATES, {
    serialNumber
  });

  const { mutate: executeSuite } = useMutation(EXECUTE_SUITE);

  watch(updateResult, (data) => {
    if (data?.diagnosticUpdates) {
      const update = data.diagnosticUpdates;

      if (update.event === 'suite_started') {
        isRunning.value = true;
        testResults.value = [];
      } else if (update.event === 'test_completed') {
        testResults.value.push(update);
      } else if (update.event === 'suite_completed') {
        isRunning.value = false;
      }
    }
  });

  const runDiagnostics = async () => {
    try {
      await executeSuite({
        input: {
          serialNumber,
          autoFix: true,
          stopOnFailure: false
        }
      });
    } catch (error) {
      console.error('Failed to run diagnostics:', error);
      isRunning.value = false;
    }
  };

  return {
    testResults,
    isRunning,
    runDiagnostics
  };
}
```

## 🎯 **Usage Patterns**

### **For Simple Operations**
```graphql
# Get available tests
query { diagnosticTests(serialNumber: "EP12345") { name displayName } }

# Execute single test
mutation {
  executeDiagnosticTest(input: { testId: "test123", serialNumber: "EP12345" }) {
    status output
  }
}
```

### **For Real-time Operations**
```graphql
# Start suite execution
mutation { executeDiagnosticSuite(input: { serialNumber: "EP12345" }) { suiteExecutionId } }

# Subscribe to updates
subscription { diagnosticUpdates(serialNumber: "EP12345") { event testName status } }
```

### **For Async Operations**
```graphql
# Start async execution
mutation {
  executeDiagnosticSuiteAsync(input: { serialNumber: "EP12345" }) {
    operationId status
  }
}

# Monitor async progress
subscription {
  diagnosticAsyncUpdates(serialNumber: "EP12345") {
    event status executionId
  }
}
```

## 🔗 **Integration with Fastify**

```typescript
import { createGraphQLSchema } from '@/graphql/schema';
import mercurius from 'mercurius';

// Register GraphQL
await fastify.register(mercurius, {
  schema: await createGraphQLSchema(),
  graphiql: true, // Enable GraphQL playground
  subscription: true, // Enable subscriptions
  context: (request, reply) => ({
    userId: request.headers['x-user-id'],
    userRole: request.headers['x-user-role']
  })
});
```

This GraphQL-only solution provides everything you need for robust diagnostic operations with real-time updates, async support, and type safety!
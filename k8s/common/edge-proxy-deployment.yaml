apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-proxy-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: edge-proxy
  template:
    metadata:
      labels:
        app: edge-proxy
    spec:
      containers:
        - name: edge-proxy
          image: epikedge/edge-proxy:latest
          ports:
            - containerPort: 50051
          env:
            - name: EDGE_PORT
              value: "50051"
            - name: MONGO_DB_URI
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: MONGO_DB_URI
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: REDIS_PORT
            - name: REDIS_SERVER
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: REDIS_SERVER

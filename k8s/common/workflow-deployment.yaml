apiVersion: apps/v1
kind: Deployment
metadata:
  name: workflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: workflow
  template:
    metadata:
      labels:
        app: workflow
    spec:
      containers:
      - name: workflow
        image: epikedge/workflow
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        ports:
        - containerPort: 3030
        envFrom:
        - secretRef:
            name: workflow-secrets
apiVersion: apps/v1
kind: Deployment
metadata:
  name: internal-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: internal-proxy
  template:
    metadata:
      labels:
        app: internal-proxy
    spec:
      containers:
        - name: internal-proxy
          image: epikedge/internal-proxy
          resources:
            limits:
              cpu: "1"
              memory: "1Gi"
            requests:
              cpu: "0.5"
              memory: "0.5Gi"
          ports:
            - containerPort: 8080

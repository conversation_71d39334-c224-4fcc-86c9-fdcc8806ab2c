apiVersion: apps/v1
kind: Deployment
metadata:
  name: epikv2-api-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: epikv2-api
  template:
    metadata:
      labels:
        app: epikv2-api
    spec:
      containers:
        - name: epikv2-api
          image: epikedge/epikv2-api
          env:
            - name: SECRET
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: SECRETV2
            - name: MONGO_DB_URI
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: MONGO_DB_URI
            - name: REDIS_PORT
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: REDIS_PORT
            - name: REDIS_SERVER
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: REDIS_SERVER
            - name: DEV_MODE
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: DEV_MODE
            - name: AUTH_GRPC_PORT
              valueFrom:
                configMapKeyRef:
                  name: app-config
                  key: AUTH_GRPC_PORT
            - name: PORT
              value: "3000"
            - name: VMAIL_GREETING_PATH
              value: /data/voicemail/greetings/
          volumeMounts:
            - name: voicemail-greetings
              mountPath: /data/voicemail/greetings
      volumes:
        - name: voicemail-greetings
          persistentVolumeClaim:
            claimName: pvc-voicemail-greetings

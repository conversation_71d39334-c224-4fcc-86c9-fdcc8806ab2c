
apiVersion: v1
kind: ConfigMap
metadata:
  name: nats-config
  namespace: default
  labels:
    app: nats
data:
  nats.conf: |
    # NATS Server Configuration for Development
    server_name: default
    
    # Network settings
    host: "0.0.0.0"
    port: 4222
    
    # HTTP monitoring port
    monitor_port: 8222
    
    # Logging
    debug: true
    trace: false
    logtime: true
    log_file: "/dev/stdout"
    
    # Limits for development
    max_connections: 100
    max_subscriptions: 1000
    max_payload: 1MB
    max_pending: 64MB
    max_control_line: 4KB
    
    # JetStream configuration
    jetstream {
      # Memory storage only
      max_memory_store: 512MB
      max_file_store: 0
    }

---
apiVersion: v1
kind: Service
metadata:
  name: nats
  namespace: default
  labels:
    app: nats
spec:
  type: ClusterIP
  ports:
  - name: client
    port: 4222
    targetPort: 4222
  - name: monitor
    port: 8222
    targetPort: 8222
  selector:
    app: nats

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats
  namespace: default
  labels:
    app: nats
    environment: development
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nats
  template:
    metadata:
      labels:
        app: nats
        environment: development
    spec:
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
        runAsNonRoot: true
      containers:
      - name: nats
        image: nats:2.10.7-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 4222
          name: client
        - containerPort: 8222
          name: monitor
        args:
        - "--config"
        - "/etc/nats-config/nats.conf"
        volumeMounts:
        - name: config-volume
          mountPath: /etc/nats-config
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /
            port: 8222
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 8222
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 10
          failureThreshold: 3
      volumes:
      - name: config-volume
        configMap:
          name: nats-config
      - name: tmp
        emptyDir: {}

---
# NATS Box for testing and debugging
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats-box
  namespace: default
  labels:
    app: nats-box
    environment: development
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nats-box
  template:
    metadata:
      labels:
        app: nats-box
        environment: development
    spec:
      containers:
      - name: nats-box
        image: natsio/nats-box:0.14.1
        command:
        - "tail"
        - "-f"
        - "/dev/null"
        env:
        - name: NATS_URL
          value: "nats://nats:4222"
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
          limits:
            cpu: 100m
            memory: 64Mi
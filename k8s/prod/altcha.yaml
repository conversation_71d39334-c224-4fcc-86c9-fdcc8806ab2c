apiVersion: apps/v1
kind: Deployment
metadata:
  name: altcha-sentinel
spec:
  replicas: 1
  selector:
    matchLabels:
      app: altcha-sentinel
  template:
    metadata:
      labels:
        app: altcha-sentinel
    spec:
      containers:
      - name: altcha
        image: ghcr.io/altcha-org/sentinel:latest
        ports:
        - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: altcha-service
spec:
  selector:
    app: altcha-sentinel
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP

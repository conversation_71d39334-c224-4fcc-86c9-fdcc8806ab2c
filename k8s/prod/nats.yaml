
apiVersion: v1
kind: ServiceAccount
metadata:
  name: nats
  namespace: default

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: nats-cluster-role
rules:
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: nats-cluster-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: nats-cluster-role
subjects:
- kind: ServiceAccount
  name: nats
  namespace: default

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nats-config
  namespace: default
  labels:
    app: nats
data:
  nats.conf: |
    # NATS Server Configuration for Production
    server_name: $HOSTNAME
    
    # Network settings
    host: "0.0.0.0"
    port: 4222
    
    # HTTP monitoring port
    monitor_port: 8222
    
    # Logging (less verbose for production)
    debug: false
    trace: false
    logtime: true
    log_file: "/dev/stdout"
    
    # Production limits
    max_connections: 10000
    max_subscriptions: 100000
    max_payload: 8MB
    max_pending: 256MB
    max_control_line: 4KB
    write_deadline: "10s"
    max_closed_clients: 10000
    
    # Clustering configuration
    cluster {
      host: "0.0.0.0"
      port: 6222
      
      routes = [
        nats://nats-0.nats-headless.default.svc.cluster.local:6222,
        nats://nats-1.nats-headless.default.svc.cluster.local:6222,
        nats://nats-2.nats-headless.default.svc.cluster.local:6222
      ]
      
      cluster_advertise: $CLUSTER_ADVERTISE
      connect_retries: 120
    }
    
    # JetStream configuration for production
    jetstream {
      # Memory storage only (no persistence)
      max_memory_store: 8GB
      max_file_store: 0
    }

---
apiVersion: v1
kind: Service
metadata:
  name: nats
  namespace: default
  labels:
    app: nats
spec:
  type: ClusterIP
  ports:
  - name: client
    port: 4222
    targetPort: 4222
  - name: monitor
    port: 8222
    targetPort: 8222
  selector:
    app: nats

---
apiVersion: v1
kind: Service
metadata:
  name: nats-headless
  namespace: default
  labels:
    app: nats
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: client
    port: 4222
    targetPort: 4222
  - name: cluster
    port: 6222
    targetPort: 6222
  - name: monitor
    port: 8222
    targetPort: 8222
  selector:
    app: nats

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: nats
  namespace: default
  labels:
    app: nats
    environment: production
spec:
  serviceName: nats-headless
  replicas: 3
  selector:
    matchLabels:
      app: nats
  template:
    metadata:
      labels:
        app: nats
        environment: production
    spec:
      serviceAccountName: nats
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
        runAsNonRoot: true
      # Anti-affinity to spread pods across nodes
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - nats
              topologyKey: kubernetes.io/hostname
      containers:
      - name: nats
        image: nats:2.10.7-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 4222
          name: client
        - containerPort: 8222
          name: monitor
        - containerPort: 6222
          name: cluster
        args:
        - "--config"
        - "/etc/nats-config/nats.conf"
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: CLUSTER_ADVERTISE
          value: $(POD_NAME).nats-headless.$(POD_NAMESPACE).svc.cluster.local
        - name: HOSTNAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: config-volume
          mountPath: /etc/nats-config
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /
            port: 8222
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 8222
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 10
          failureThreshold: 3
        # Graceful shutdown
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/sh
              - -c
              - "nats-server --signal quit"
      volumes:
      - name: config-volume
        configMap:
          name: nats-config
      - name: tmp
        emptyDir: {}
      terminationGracePeriodSeconds: 60

---
# Pod Disruption Budget for production
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: nats-pdb
  namespace: default
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: nats

---
# NATS Box for production testing
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats-box
  namespace: default
  labels:
    app: nats-box
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nats-box
  template:
    metadata:
      labels:
        app: nats-box
        environment: production
    spec:
      containers:
      - name: nats-box
        image: natsio/nats-box:0.14.1
        command:
        - "tail"
        - "-f"
        - "/dev/null"
        env:
        - name: NATS_URL
          value: "nats://nats:4222"
        resources:
          requests:
            cpu: 10m
            memory: 32Mi
          limits:
            cpu: 100m
            memory: 64Mi
package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/avast/retry-go/v4"
)

var inFlight sync.Map

type responseResult struct {
	body       []byte
	headers    http.Header
	statusCode int
	err        error
	done       chan struct{}
}

var cache sync.Map
var breakers sync.Map

type cachedResult struct {
	body       []byte
	headers    http.Header
	statusCode int
	expiry     time.Time
}

type circuitBreaker struct {
	failCount  int
	blockUntil time.Time
}

func main() {
	http.HandleFunc("/", proxyHandler)
	log.Println("Proxy listening on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func proxyHandler(w http.ResponseWriter, r *http.Request) {
	ip := r.URL.Query().Get("ip")
	dc := r.URL.Query().Get("dc")
	path := r.URL.Query().Get("path")
	port := r.URL.Query().Get("port")

	if ip == "" || dc == "" || path == "" {
		http.Error(w, "Missing required query params: ip, dc, path", http.StatusBadRequest)
		return
	}

	if port == "" {
		port = "9988"
	}

	ctx := r.Context()
	transformedIP, err := transformIP(ip, dc)
	if err != nil {
		http.Error(w, "Invalid IP format: "+err.Error(), http.StatusBadRequest)
		return
	}

	targetURL := fmt.Sprintf("http://%s:%s%s", transformedIP, port, path)
	if len(r.URL.RawQuery) > 0 {
		q := r.URL.Query()
		q.Del("ip")
		q.Del("dc")
		q.Del("path")
		q.Del("port")
		if len(q) > 0 {
			targetURL += "?" + q.Encode()
		}
	}

	key := fmt.Sprintf("%s|%s|%s|%s|%s", r.Method, ip, dc, port, path)
	res, err := getOrDo(ctx, key, targetURL, r)
	if err != nil {
		select {
		case <-ctx.Done():
			http.Error(w, "Client cancelled request", 499)
		default:
			http.Error(w, "Request failed: "+err.Error(), http.StatusBadGateway)
		}
		return
	}

	for k, v := range res.headers {
		w.Header()[k] = v
	}
	w.WriteHeader(res.statusCode)
	w.Write(res.body)
}

func failOrBlock(key string) {
	now := time.Now()
	cbIface, _ := breakers.LoadOrStore(key, &circuitBreaker{})
	cb := cbIface.(*circuitBreaker)

	cb.failCount++
	if cb.failCount >= 3 {
		cb.blockUntil = now.Add(30 * time.Second)
		log.Printf("Circuit breaker tripped for %s", key)
	}
}

func closedChan() chan struct{} {
	ch := make(chan struct{})
	close(ch)
	return ch
}

func getOrDo(ctx context.Context, key, url string, originalReq *http.Request) (*responseResult, error) {
	if b, ok := breakers.Load(key); ok {
		cb := b.(*circuitBreaker)
		if time.Now().Before(cb.blockUntil) {
			return nil, fmt.Errorf("circuit open for %s", key)
		}
	}

	if originalReq.Method == "GET" {
		if v, ok := cache.Load(key); ok {
			c := v.(cachedResult)
			if time.Now().Before(c.expiry) {
				return &responseResult{
					body:       c.body,
					headers:    c.headers,
					statusCode: c.statusCode,
					err:        nil,
					done:       closedChan(),
				}, nil
			}
		}
	}

	val, loaded := inFlight.LoadOrStore(key, &responseResult{done: make(chan struct{})})
	res := val.(*responseResult)

	if loaded {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-res.done:
			return res, res.err
		}
	}

	go func() {
		defer close(res.done)
		log.Printf("Forwarding %s request to: %s", originalReq.Method, url)

		err := retry.Do(
			func() error {
				// Create new request with same method and body
				req, _ := http.NewRequestWithContext(ctx, originalReq.Method, url, originalReq.Body)

				// Copy original headers
				for k, v := range originalReq.Header {
					req.Header[k] = v
				}

				client := http.Client{Timeout: 30 * time.Second}
				resp, err := client.Do(req)
				if err != nil {
					return err
				}
				defer resp.Body.Close()

				res.body, err = io.ReadAll(resp.Body)
				res.statusCode = resp.StatusCode
				res.headers = resp.Header
				res.err = err

				// Cache successful GET requests
				if err == nil && resp.StatusCode >= 200 && resp.StatusCode < 300 && originalReq.Method == "GET" {
					cache.Store(key, cachedResult{
						body:       res.body,
						headers:    res.headers,
						statusCode: res.statusCode,
						expiry:     time.Now().Add(5 * time.Second),
					})
				}
				return err
			},
			retry.Context(ctx),
			retry.Attempts(3),
			retry.DelayType(retry.BackOffDelay),
		)

		if err != nil {
			res.err = err
			failOrBlock(key)
		}

		inFlight.Delete(key)
	}()

	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-res.done:
		return res, res.err
	}
}

func transformIP(ip, dc string) (string, error) {
	dc = strings.ToUpper(dc)

	octets := strings.Split(ip, ".")
	if len(octets) != 4 {
		return "", fmt.Errorf("expected IPv4 address")
	}

	o1, err := strconv.Atoi(octets[0])
	if err != nil {
		return "", fmt.Errorf("invalid IP format")
	}
	o2, err := strconv.Atoi(octets[1])
	if err != nil {
		return "", fmt.Errorf("invalid IP format")
	}

	if o1 != 10 {
		return ip, nil
	}

	if o2 != 15 && o2 != 64 {
		return ip, nil
	}

	translations := getTranslations(o2)

	if dc == "DA" || dc == "DL" || dc == "DAL" {
		dc = "DA"
	}

	newO2, exists := translations[dc]
	if !exists {
		return ip, nil
	}

	return fmt.Sprintf("10.%d.%s.%s", newO2, octets[2], octets[3]), nil
}

func getTranslations(secondOctet int) map[string]int {
	if secondOctet == 15 {
		return map[string]int{
			"CH": 18,
			"NY": 19,
			"AT": 20,
			"DA": 21,
		}
	}
	if secondOctet == 64 {
		return map[string]int{
			"CH": 66,
			"NY": 67,
			"AT": 68,
			"DA": 69,
		}
	}
	return map[string]int{}
}

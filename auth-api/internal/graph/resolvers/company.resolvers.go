package graph

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.72

import (
	"auth-api/internal/api/middleware"
	"auth-api/internal/common/transport"
	"auth-api/internal/db/models"
	"auth-api/internal/graph/generated"
	"context"
	"strings"

	"go.mongodb.org/mongo-driver/v2/bson"
)

// EnterpriseNames is the resolver for the enterpriseNames field.
func (r *companyResolver) EnterpriseNames(ctx context.Context, obj *models.Company) (*string, error) {
	if len(obj.Enterprises) == 0 {
		return nil, nil
	}
	filter := &bson.M{"_id": bson.M{"$in": obj.Enterprises}}
	enterprises := r.Repo.Enterprise.BsonFind(filter, []string{"name"})
	if enterprises != nil {
		var names []string
		for _, e := range *enterprises {
			names = append(names, e.Name)
		}
		namesStr := strings.Join(names, ", ")
		return &namesStr, nil
	}
	return nil, nil
}

// MonitoredDevices is the resolver for the monitoredDevices field.
func (r *companyResolver) MonitoredDevices(ctx context.Context, obj *models.Company) (int, error) {
	return int(r.Repo.EpikBox.CountDocuments(bson.M{"assignedTo": obj.ID, "monitor": true})), nil
}

// ManagedDevices is the resolver for the managedDevices field.
func (r *companyResolver) ManagedDevices(ctx context.Context, obj *models.Company) (int, error) {
	return int(r.Repo.EpikBox.CountDocuments(bson.M{"assignedTo": obj.ID})), nil
}

// ListCompanies is the resolver for the ListCompanies field.
func (r *queryResolver) ListCompanies(ctx context.Context, input transport.ListCompanyInput) (*transport.PaginatedResponse[models.Company], error) {
	usr := middleware.GetUserClaimFromContext(ctx)
	r.Service.Permissions.ValidateUserPermission(usr.UserID, transport.CompanyFeatureList, transport.ValidateRead)
	res := r.Service.Company.ListCompanies(usr.UserID, &input)
	return res, nil
}

// Company returns generated.CompanyResolver implementation.
func (r *Resolver) Company() generated.CompanyResolver { return &companyResolver{r} }

type companyResolver struct{ *Resolver }

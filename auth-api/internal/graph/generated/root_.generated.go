// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package generated

import (
	"auth-api/internal/common/transport"
	"bytes"
	"context"
	"errors"
	"sync/atomic"

	"github.com/99designs/gqlgen/graphql"
	"github.com/99designs/gqlgen/graphql/introspection"
	gqlparser "github.com/vektah/gqlparser/v2"
	"github.com/vektah/gqlparser/v2/ast"
)

// NewExecutableSchema creates an ExecutableSchema from the ResolverRoot interface.
func NewExecutableSchema(cfg Config) graphql.ExecutableSchema {
	return &executableSchema{
		schema:     cfg.Schema,
		resolvers:  cfg.Resolvers,
		directives: cfg.Directives,
		complexity: cfg.Complexity,
	}
}

type Config struct {
	Schema     *ast.Schema
	Resolvers  ResolverRoot
	Directives DirectiveRoot
	Complexity ComplexityRoot
}

type ResolverRoot interface {
	Company() CompanyResolver
	PermissionGroup() PermissionGroupResolver
	Query() QueryResolver
	UISchema() UISchemaResolver
	User() UserResolver
}

type DirectiveRoot struct {
}

type ComplexityRoot struct {
	Company struct {
		CompanyState     func(childComplexity int) int
		CompanyType      func(childComplexity int) int
		ContactEmails    func(childComplexity int) int
		ContactPerson    func(childComplexity int) int
		CreatedAt        func(childComplexity int) int
		EnterpriseNames  func(childComplexity int) int
		Enterprises      func(childComplexity int) int
		EpikCustomerId   func(childComplexity int) int
		ID               func(childComplexity int) int
		Location         func(childComplexity int) int
		Logo             func(childComplexity int) int
		ManagedDevices   func(childComplexity int) int
		MonitoredDevices func(childComplexity int) int
		Name             func(childComplexity int) int
		UpdatedAt        func(childComplexity int) int
	}

	CompanyContactPerson struct {
		Email func(childComplexity int) int
		Name  func(childComplexity int) int
		Phone func(childComplexity int) int
	}

	CompanyLocation struct {
		Address func(childComplexity int) int
		City    func(childComplexity int) int
		State   func(childComplexity int) int
		Zip     func(childComplexity int) int
	}

	FeatureMeta struct {
		Read        func(childComplexity int) int
		ResourceKey func(childComplexity int) int
		Write       func(childComplexity int) int
	}

	ListCompanyResponse struct {
		Docs       func(childComplexity int) int
		Pagination func(childComplexity int) int
	}

	ListPermissionGroupsResponse struct {
		Docs    func(childComplexity int) int
		Options func(childComplexity int) int
	}

	ListUserResponse struct {
		Docs       func(childComplexity int) int
		Pagination func(childComplexity int) int
	}

	MenuItem struct {
		Childs      func(childComplexity int) int
		ResourceKey func(childComplexity int) int
		Title       func(childComplexity int) int
	}

	PaginationResponse struct {
		Count       func(childComplexity int) int
		CurrentPage func(childComplexity int) int
		TotalPages  func(childComplexity int) int
	}

	PermissionEntry struct {
		Permission func(childComplexity int) int
		Schema     func(childComplexity int) int
	}

	PermissionGroup struct {
		Color       func(childComplexity int) int
		CreatedAt   func(childComplexity int) int
		ID          func(childComplexity int) int
		Permissions func(childComplexity int) int
		Title       func(childComplexity int) int
		UpdatedAt   func(childComplexity int) int
	}

	PermissionOptions struct {
		Label func(childComplexity int) int
		Value func(childComplexity int) int
	}

	PermissionsSchema struct {
		Read  func(childComplexity int) int
		Write func(childComplexity int) int
	}

	Query struct {
		Empty                func(childComplexity int) int
		ListCompanies        func(childComplexity int, input transport.ListCompanyInput) int
		ListPermissionGroups func(childComplexity int) int
		ListUserPermission   func(childComplexity int) int
		ListUsers            func(childComplexity int, input transport.ListUserInput) int
	}

	ResourceEntry struct {
		Feature func(childComplexity int) int
		Meta    func(childComplexity int) int
	}

	UISchema struct {
		Resources func(childComplexity int) int
	}

	UISchemaFeature struct {
		Read        func(childComplexity int) int
		ResourceKey func(childComplexity int) int
		Write       func(childComplexity int) int
	}

	UISchemaResource struct {
		Features    func(childComplexity int) int
		ResourceKey func(childComplexity int) int
	}

	User struct {
		Company             func(childComplexity int) int
		CompanyDoc          func(childComplexity int) int
		Default2FA          func(childComplexity int) int
		Email               func(childComplexity int) int
		Enabled             func(childComplexity int) int
		ID                  func(childComplexity int) int
		LockReminder        func(childComplexity int) int
		MaintenanceNoteSeen func(childComplexity int) int
		Name                func(childComplexity int) int
		PhoneNumber         func(childComplexity int) int
		ProfilePic          func(childComplexity int) int
		RegisterDate        func(childComplexity int) int
		ResetPassword       func(childComplexity int) int
		TimeZone            func(childComplexity int) int
		TwoFactor           func(childComplexity int) int
		UpdatedAt           func(childComplexity int) int
	}

	UserPermissionData struct {
		MenuItems func(childComplexity int) int
		UISchema  func(childComplexity int) int
	}
}

type executableSchema struct {
	schema     *ast.Schema
	resolvers  ResolverRoot
	directives DirectiveRoot
	complexity ComplexityRoot
}

func (e *executableSchema) Schema() *ast.Schema {
	if e.schema != nil {
		return e.schema
	}
	return parsedSchema
}

func (e *executableSchema) Complexity(ctx context.Context, typeName, field string, childComplexity int, rawArgs map[string]any) (int, bool) {
	ec := executionContext{nil, e, 0, 0, nil}
	_ = ec
	switch typeName + "." + field {

	case "Company.companyState":
		if e.complexity.Company.CompanyState == nil {
			break
		}

		return e.complexity.Company.CompanyState(childComplexity), true

	case "Company.companyType":
		if e.complexity.Company.CompanyType == nil {
			break
		}

		return e.complexity.Company.CompanyType(childComplexity), true

	case "Company.contactEmails":
		if e.complexity.Company.ContactEmails == nil {
			break
		}

		return e.complexity.Company.ContactEmails(childComplexity), true

	case "Company.contactPerson":
		if e.complexity.Company.ContactPerson == nil {
			break
		}

		return e.complexity.Company.ContactPerson(childComplexity), true

	case "Company.createdAt":
		if e.complexity.Company.CreatedAt == nil {
			break
		}

		return e.complexity.Company.CreatedAt(childComplexity), true

	case "Company.enterpriseNames":
		if e.complexity.Company.EnterpriseNames == nil {
			break
		}

		return e.complexity.Company.EnterpriseNames(childComplexity), true

	case "Company.enterprises":
		if e.complexity.Company.Enterprises == nil {
			break
		}

		return e.complexity.Company.Enterprises(childComplexity), true

	case "Company.epikCustomerId":
		if e.complexity.Company.EpikCustomerId == nil {
			break
		}

		return e.complexity.Company.EpikCustomerId(childComplexity), true

	case "Company._id":
		if e.complexity.Company.ID == nil {
			break
		}

		return e.complexity.Company.ID(childComplexity), true

	case "Company.location":
		if e.complexity.Company.Location == nil {
			break
		}

		return e.complexity.Company.Location(childComplexity), true

	case "Company.logo":
		if e.complexity.Company.Logo == nil {
			break
		}

		return e.complexity.Company.Logo(childComplexity), true

	case "Company.managedDevices":
		if e.complexity.Company.ManagedDevices == nil {
			break
		}

		return e.complexity.Company.ManagedDevices(childComplexity), true

	case "Company.monitoredDevices":
		if e.complexity.Company.MonitoredDevices == nil {
			break
		}

		return e.complexity.Company.MonitoredDevices(childComplexity), true

	case "Company.name":
		if e.complexity.Company.Name == nil {
			break
		}

		return e.complexity.Company.Name(childComplexity), true

	case "Company.updatedAt":
		if e.complexity.Company.UpdatedAt == nil {
			break
		}

		return e.complexity.Company.UpdatedAt(childComplexity), true

	case "CompanyContactPerson.email":
		if e.complexity.CompanyContactPerson.Email == nil {
			break
		}

		return e.complexity.CompanyContactPerson.Email(childComplexity), true

	case "CompanyContactPerson.name":
		if e.complexity.CompanyContactPerson.Name == nil {
			break
		}

		return e.complexity.CompanyContactPerson.Name(childComplexity), true

	case "CompanyContactPerson.phone":
		if e.complexity.CompanyContactPerson.Phone == nil {
			break
		}

		return e.complexity.CompanyContactPerson.Phone(childComplexity), true

	case "CompanyLocation.address":
		if e.complexity.CompanyLocation.Address == nil {
			break
		}

		return e.complexity.CompanyLocation.Address(childComplexity), true

	case "CompanyLocation.city":
		if e.complexity.CompanyLocation.City == nil {
			break
		}

		return e.complexity.CompanyLocation.City(childComplexity), true

	case "CompanyLocation.state":
		if e.complexity.CompanyLocation.State == nil {
			break
		}

		return e.complexity.CompanyLocation.State(childComplexity), true

	case "CompanyLocation.zip":
		if e.complexity.CompanyLocation.Zip == nil {
			break
		}

		return e.complexity.CompanyLocation.Zip(childComplexity), true

	case "FeatureMeta.read":
		if e.complexity.FeatureMeta.Read == nil {
			break
		}

		return e.complexity.FeatureMeta.Read(childComplexity), true

	case "FeatureMeta.resourceKey":
		if e.complexity.FeatureMeta.ResourceKey == nil {
			break
		}

		return e.complexity.FeatureMeta.ResourceKey(childComplexity), true

	case "FeatureMeta.write":
		if e.complexity.FeatureMeta.Write == nil {
			break
		}

		return e.complexity.FeatureMeta.Write(childComplexity), true

	case "ListCompanyResponse.docs":
		if e.complexity.ListCompanyResponse.Docs == nil {
			break
		}

		return e.complexity.ListCompanyResponse.Docs(childComplexity), true

	case "ListCompanyResponse.pagination":
		if e.complexity.ListCompanyResponse.Pagination == nil {
			break
		}

		return e.complexity.ListCompanyResponse.Pagination(childComplexity), true

	case "ListPermissionGroupsResponse.docs":
		if e.complexity.ListPermissionGroupsResponse.Docs == nil {
			break
		}

		return e.complexity.ListPermissionGroupsResponse.Docs(childComplexity), true

	case "ListPermissionGroupsResponse.options":
		if e.complexity.ListPermissionGroupsResponse.Options == nil {
			break
		}

		return e.complexity.ListPermissionGroupsResponse.Options(childComplexity), true

	case "ListUserResponse.docs":
		if e.complexity.ListUserResponse.Docs == nil {
			break
		}

		return e.complexity.ListUserResponse.Docs(childComplexity), true

	case "ListUserResponse.pagination":
		if e.complexity.ListUserResponse.Pagination == nil {
			break
		}

		return e.complexity.ListUserResponse.Pagination(childComplexity), true

	case "MenuItem.childs":
		if e.complexity.MenuItem.Childs == nil {
			break
		}

		return e.complexity.MenuItem.Childs(childComplexity), true

	case "MenuItem.resourceKey":
		if e.complexity.MenuItem.ResourceKey == nil {
			break
		}

		return e.complexity.MenuItem.ResourceKey(childComplexity), true

	case "MenuItem.title":
		if e.complexity.MenuItem.Title == nil {
			break
		}

		return e.complexity.MenuItem.Title(childComplexity), true

	case "PaginationResponse.count":
		if e.complexity.PaginationResponse.Count == nil {
			break
		}

		return e.complexity.PaginationResponse.Count(childComplexity), true

	case "PaginationResponse.currentPage":
		if e.complexity.PaginationResponse.CurrentPage == nil {
			break
		}

		return e.complexity.PaginationResponse.CurrentPage(childComplexity), true

	case "PaginationResponse.totalPages":
		if e.complexity.PaginationResponse.TotalPages == nil {
			break
		}

		return e.complexity.PaginationResponse.TotalPages(childComplexity), true

	case "PermissionEntry.permission":
		if e.complexity.PermissionEntry.Permission == nil {
			break
		}

		return e.complexity.PermissionEntry.Permission(childComplexity), true

	case "PermissionEntry.schema":
		if e.complexity.PermissionEntry.Schema == nil {
			break
		}

		return e.complexity.PermissionEntry.Schema(childComplexity), true

	case "PermissionGroup.color":
		if e.complexity.PermissionGroup.Color == nil {
			break
		}

		return e.complexity.PermissionGroup.Color(childComplexity), true

	case "PermissionGroup.createdAt":
		if e.complexity.PermissionGroup.CreatedAt == nil {
			break
		}

		return e.complexity.PermissionGroup.CreatedAt(childComplexity), true

	case "PermissionGroup._id":
		if e.complexity.PermissionGroup.ID == nil {
			break
		}

		return e.complexity.PermissionGroup.ID(childComplexity), true

	case "PermissionGroup.permissions":
		if e.complexity.PermissionGroup.Permissions == nil {
			break
		}

		return e.complexity.PermissionGroup.Permissions(childComplexity), true

	case "PermissionGroup.title":
		if e.complexity.PermissionGroup.Title == nil {
			break
		}

		return e.complexity.PermissionGroup.Title(childComplexity), true

	case "PermissionGroup.updatedAt":
		if e.complexity.PermissionGroup.UpdatedAt == nil {
			break
		}

		return e.complexity.PermissionGroup.UpdatedAt(childComplexity), true

	case "PermissionOptions.label":
		if e.complexity.PermissionOptions.Label == nil {
			break
		}

		return e.complexity.PermissionOptions.Label(childComplexity), true

	case "PermissionOptions.value":
		if e.complexity.PermissionOptions.Value == nil {
			break
		}

		return e.complexity.PermissionOptions.Value(childComplexity), true

	case "PermissionsSchema.read":
		if e.complexity.PermissionsSchema.Read == nil {
			break
		}

		return e.complexity.PermissionsSchema.Read(childComplexity), true

	case "PermissionsSchema.write":
		if e.complexity.PermissionsSchema.Write == nil {
			break
		}

		return e.complexity.PermissionsSchema.Write(childComplexity), true

	case "Query._empty":
		if e.complexity.Query.Empty == nil {
			break
		}

		return e.complexity.Query.Empty(childComplexity), true

	case "Query.ListCompanies":
		if e.complexity.Query.ListCompanies == nil {
			break
		}

		args, err := ec.field_Query_ListCompanies_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ListCompanies(childComplexity, args["input"].(transport.ListCompanyInput)), true

	case "Query.ListPermissionGroups":
		if e.complexity.Query.ListPermissionGroups == nil {
			break
		}

		return e.complexity.Query.ListPermissionGroups(childComplexity), true

	case "Query.ListUserPermission":
		if e.complexity.Query.ListUserPermission == nil {
			break
		}

		return e.complexity.Query.ListUserPermission(childComplexity), true

	case "Query.ListUsers":
		if e.complexity.Query.ListUsers == nil {
			break
		}

		args, err := ec.field_Query_ListUsers_args(ctx, rawArgs)
		if err != nil {
			return 0, false
		}

		return e.complexity.Query.ListUsers(childComplexity, args["input"].(transport.ListUserInput)), true

	case "ResourceEntry.feature":
		if e.complexity.ResourceEntry.Feature == nil {
			break
		}

		return e.complexity.ResourceEntry.Feature(childComplexity), true

	case "ResourceEntry.meta":
		if e.complexity.ResourceEntry.Meta == nil {
			break
		}

		return e.complexity.ResourceEntry.Meta(childComplexity), true

	case "UISchema.resources":
		if e.complexity.UISchema.Resources == nil {
			break
		}

		return e.complexity.UISchema.Resources(childComplexity), true

	case "UISchemaFeature.read":
		if e.complexity.UISchemaFeature.Read == nil {
			break
		}

		return e.complexity.UISchemaFeature.Read(childComplexity), true

	case "UISchemaFeature.resourceKey":
		if e.complexity.UISchemaFeature.ResourceKey == nil {
			break
		}

		return e.complexity.UISchemaFeature.ResourceKey(childComplexity), true

	case "UISchemaFeature.write":
		if e.complexity.UISchemaFeature.Write == nil {
			break
		}

		return e.complexity.UISchemaFeature.Write(childComplexity), true

	case "UISchemaResource.features":
		if e.complexity.UISchemaResource.Features == nil {
			break
		}

		return e.complexity.UISchemaResource.Features(childComplexity), true

	case "UISchemaResource.resourceKey":
		if e.complexity.UISchemaResource.ResourceKey == nil {
			break
		}

		return e.complexity.UISchemaResource.ResourceKey(childComplexity), true

	case "User.company":
		if e.complexity.User.Company == nil {
			break
		}

		return e.complexity.User.Company(childComplexity), true

	case "User.companyDoc":
		if e.complexity.User.CompanyDoc == nil {
			break
		}

		return e.complexity.User.CompanyDoc(childComplexity), true

	case "User.default2fa":
		if e.complexity.User.Default2FA == nil {
			break
		}

		return e.complexity.User.Default2FA(childComplexity), true

	case "User.email":
		if e.complexity.User.Email == nil {
			break
		}

		return e.complexity.User.Email(childComplexity), true

	case "User.enabled":
		if e.complexity.User.Enabled == nil {
			break
		}

		return e.complexity.User.Enabled(childComplexity), true

	case "User._id":
		if e.complexity.User.ID == nil {
			break
		}

		return e.complexity.User.ID(childComplexity), true

	case "User.lockReminder":
		if e.complexity.User.LockReminder == nil {
			break
		}

		return e.complexity.User.LockReminder(childComplexity), true

	case "User.maintenanceNoteSeen":
		if e.complexity.User.MaintenanceNoteSeen == nil {
			break
		}

		return e.complexity.User.MaintenanceNoteSeen(childComplexity), true

	case "User.name":
		if e.complexity.User.Name == nil {
			break
		}

		return e.complexity.User.Name(childComplexity), true

	case "User.phoneNumber":
		if e.complexity.User.PhoneNumber == nil {
			break
		}

		return e.complexity.User.PhoneNumber(childComplexity), true

	case "User.profilePic":
		if e.complexity.User.ProfilePic == nil {
			break
		}

		return e.complexity.User.ProfilePic(childComplexity), true

	case "User.registerDate":
		if e.complexity.User.RegisterDate == nil {
			break
		}

		return e.complexity.User.RegisterDate(childComplexity), true

	case "User.resetPassword":
		if e.complexity.User.ResetPassword == nil {
			break
		}

		return e.complexity.User.ResetPassword(childComplexity), true

	case "User.timeZone":
		if e.complexity.User.TimeZone == nil {
			break
		}

		return e.complexity.User.TimeZone(childComplexity), true

	case "User.twoFactor":
		if e.complexity.User.TwoFactor == nil {
			break
		}

		return e.complexity.User.TwoFactor(childComplexity), true

	case "User.updatedAt":
		if e.complexity.User.UpdatedAt == nil {
			break
		}

		return e.complexity.User.UpdatedAt(childComplexity), true

	case "UserPermissionData.menuItems":
		if e.complexity.UserPermissionData.MenuItems == nil {
			break
		}

		return e.complexity.UserPermissionData.MenuItems(childComplexity), true

	case "UserPermissionData.uiSchema":
		if e.complexity.UserPermissionData.UISchema == nil {
			break
		}

		return e.complexity.UserPermissionData.UISchema(childComplexity), true

	}
	return 0, false
}

func (e *executableSchema) Exec(ctx context.Context) graphql.ResponseHandler {
	opCtx := graphql.GetOperationContext(ctx)
	ec := executionContext{opCtx, e, 0, 0, make(chan graphql.DeferredResult)}
	inputUnmarshalMap := graphql.BuildUnmarshalerMap(
		ec.unmarshalInputListCompanyInput,
		ec.unmarshalInputListUserInput,
		ec.unmarshalInputPaginationInput,
	)
	first := true

	switch opCtx.Operation.Operation {
	case ast.Query:
		return func(ctx context.Context) *graphql.Response {
			var response graphql.Response
			var data graphql.Marshaler
			if first {
				first = false
				ctx = graphql.WithUnmarshalerMap(ctx, inputUnmarshalMap)
				data = ec._Query(ctx, opCtx.Operation.SelectionSet)
			} else {
				if atomic.LoadInt32(&ec.pendingDeferred) > 0 {
					result := <-ec.deferredResults
					atomic.AddInt32(&ec.pendingDeferred, -1)
					data = result.Result
					response.Path = result.Path
					response.Label = result.Label
					response.Errors = result.Errors
				} else {
					return nil
				}
			}
			var buf bytes.Buffer
			data.MarshalGQL(&buf)
			response.Data = buf.Bytes()
			if atomic.LoadInt32(&ec.deferred) > 0 {
				hasNext := atomic.LoadInt32(&ec.pendingDeferred) > 0
				response.HasNext = &hasNext
			}

			return &response
		}

	default:
		return graphql.OneShot(graphql.ErrorResponse(ctx, "unsupported GraphQL operation"))
	}
}

type executionContext struct {
	*graphql.OperationContext
	*executableSchema
	deferred        int32
	pendingDeferred int32
	deferredResults chan graphql.DeferredResult
}

func (ec *executionContext) processDeferredGroup(dg graphql.DeferredGroup) {
	atomic.AddInt32(&ec.pendingDeferred, 1)
	go func() {
		ctx := graphql.WithFreshResponseContext(dg.Context)
		dg.FieldSet.Dispatch(ctx)
		ds := graphql.DeferredResult{
			Path:   dg.Path,
			Label:  dg.Label,
			Result: dg.FieldSet,
			Errors: graphql.GetErrors(ctx),
		}
		// null fields should bubble up
		if dg.FieldSet.Invalids > 0 {
			ds.Result = graphql.Null
		}
		ec.deferredResults <- ds
	}()
}

func (ec *executionContext) introspectSchema() (*introspection.Schema, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapSchema(ec.Schema()), nil
}

func (ec *executionContext) introspectType(name string) (*introspection.Type, error) {
	if ec.DisableIntrospection {
		return nil, errors.New("introspection disabled")
	}
	return introspection.WrapTypeFromDef(ec.Schema(), ec.Schema().Types[name]), nil
}

var sources = []*ast.Source{
	{Name: "../schema/commons.graphqls", Input: `scalar OID
scalar DateTime
scalar Int64
type Query {
  _empty: String
}

input PaginationInput {
    page: Int64!
    pageSize: Int64!
}

type PaginationResponse {
  currentPage: Int64!
  totalPages: Int64!
  count: Int64!
}
`, BuiltIn: false},
	{Name: "../schema/company.graphqls", Input: `enum CompanyState {
  deleted
  disabled
  enabled
}

enum CompanyType {
  agency
  retail
  special
  reseller
  partner
  manufacturer
}

type CompanyContactPerson {
  name: String!
  email: String!
  phone: String!
}

type CompanyLocation {
  city: String!
  state: String!
  address: String!
  zip: String!
}

type Company {
  _id: OID!
  epikCustomerId: String!
  companyState: CompanyState!

  name: String
  enterpriseNames: String
  logo: String
  companyType: CompanyType

  contactEmails: [String!]!
  contactPerson: CompanyContactPerson!
  location: CompanyLocation!

  enterprises: [OID!]!

  monitoredDevices: Int!
  managedDevices: Int!

  createdAt: DateTime!
  updatedAt: DateTime!
}

type ListCompanyResponse {
  pagination: PaginationResponse
  docs: [Company!]!
}

extend type Query {
  ListCompanies(input: ListCompanyInput!): ListCompanyResponse!
}

input ListCompanyInput {
  pagination: PaginationInput!
  query: String
  epikCustomerId: String
  name: String
}
`, BuiltIn: false},
	{Name: "../schema/permissions.graphqls", Input: `
# Enum for Resources type
enum Resources {
  USER
  PERMISSION_GROUP
  USER_APPROVAL
}

# Enum for Features type
enum Features {
  USER_LIST_USER
  USER_ADD
  USER_UPDATE
  USER_SOFT_DELETE
  USER_PERMISSION_GROUP
  USER_USER_APPROVAL
  PERMISSION_GROUP_LIST
  USER_APPROVAL_LIST
}

# Type for FeatureMeta
type FeatureMeta {
  read: Boolean!
  write: Boolean!
  resourceKey: Resources!
}

# Type for MenuItems with recursive structure
type MenuItem {
  title: String!
  resourceKey: Resources!
  childs: [MenuItem]
}

# Type for UISchema
type UISchemaFeature {
  read: Boolean!
  write: Boolean!
  resourceKey: Resources!
}

# We need to restructure UISchema for GraphQL
# Since it's originally a map[Resources]map[Features]*FeatureMeta
type UISchema {
  resources: [UISchemaResource!]!
}

type UISchemaResource {
  resourceKey: Resources!
  features: [ResourceEntry!]!
}

type ResourceEntry {
  feature: Features!
  meta: FeatureMeta!
}


type PermissionsSchema {
  read: Boolean!
  write: Boolean!
}


type PermissionEntry {
  permission: String!
  schema: PermissionsSchema!
}

type PermissionGroup {
  _id: OID
  title: String!
  color: String!
  permissions: [PermissionEntry!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type PermissionOptions {
  label: String!
  value: String!
}

type ListPermissionGroupsResponse {
  docs: [PermissionGroup!]!
  options: [PermissionOptions!]!
}

# Simple Query type with only the needed function
extend type Query {
  ListUserPermission: UserPermissionData
  ListPermissionGroups: ListPermissionGroupsResponse
}

# Type to hold the combined return values of ListUserPermission
type UserPermissionData {
  uiSchema: UISchema!
  menuItems: [MenuItem!]!
}
`, BuiltIn: false},
	{Name: "../schema/user.graphqls", Input: `type User {
  _id: OID
  company: OID
  companyDoc: Company
  name: String
  email: String
  phoneNumber: String
  registerDate: DateTime
  enabled: Boolean
  profilePic: String
  resetPassword: Boolean
  lockReminder: Boolean
  maintenanceNoteSeen: Boolean
  timeZone: String
  default2fa: String
  twoFactor: Boolean
  updatedAt: DateTime
}

type ListUserResponse {
  pagination: PaginationResponse
  docs: [User!]!
}

extend type Query {
  ListUsers(input: ListUserInput!): ListUserResponse!
}

input ListUserInput {
  pagination: PaginationInput!
  query: String
  company: String
  email: String
  name: String
  role: String
}
`, BuiltIn: false},
}
var parsedSchema = gqlparser.MustLoadSchema(sources...)

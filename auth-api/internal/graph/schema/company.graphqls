enum CompanyState {
  deleted
  disabled
  enabled
}

enum CompanyType {
  agency
  retail
  special
  reseller
  partner
  manufacturer
}

type CompanyContactPerson {
  name: String!
  email: String!
  phone: String!
}

type CompanyLocation {
  city: String!
  state: String!
  address: String!
  zip: String!
}

type Company {
  _id: OID!
  epikCustomerId: String!
  companyState: CompanyState!

  name: String
  enterpriseNames: String
  logo: String
  companyType: CompanyType

  contactEmails: [String!]!
  contactPerson: CompanyContactPerson!
  location: CompanyLocation!

  enterprises: [OID!]!

  monitoredDevices: Int!
  managedDevices: Int!

  createdAt: DateTime!
  updatedAt: DateTime!
}

type ListCompanyResponse {
  pagination: PaginationResponse
  docs: [Company!]!
}

extend type Query {
  ListCompanies(input: ListCompanyInput!): ListCompanyResponse!
}

input ListCompanyInput {
  pagination: PaginationInput!
  query: String
  epikCustomerId: String
  name: String
}

package grpc

import (
	"context"

	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/common/transport"
	"auth-api/internal/services"
	pb "auth-api/proto"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// PermissionServer implements the PermissionService gRPC service
type PermissionServer struct {
	pb.UnimplementedPermissionServiceServer
	permissionService *services.PermissionService
	log               *logger.Logger
}

// NewPermissionServer creates a new PermissionServer
func NewPermissionServer(permissionService *services.PermissionService) *PermissionServer {
	return &PermissionServer{
		permissionService: permissionService,
		log:               logger.NewLogger(),
	}
}

func (s *PermissionServer) UnaryEcho(context.Context, *pb.EchoRequest) (*pb.EchoResponse, error) {
	return &pb.EchoResponse{Message: "Hello"}, nil
}

func (s *PermissionServer) ListUserAccessableCompaniesWithEnterprises(ctx context.Context, req *pb.UserIdInput) (*pb.ListCompanyOrEnterpriseResponse, error) {
	s.log.Info("ListUserAccessableCompaniesWithEnterprises: ", req.UserId)
	res := pb.ListCompanyOrEnterpriseResponse{
		Ids:   []string{},
		IsAll: false,
	}
	defer func() {
		if r := recover(); r != nil {
			s.log.Error(r)
		}
	}()
	isAll, _, ids := s.permissionService.ListUserAccessableCompaniesWithEnterprises(req.UserId)
	if ids != nil {
		res.Ids = *ids
	}
	res.IsAll = isAll
	s.log.Debug("ListUserAccessableCompaniesWithEnterprises: ", res)
	return &res, nil
}
func (s *PermissionServer) ListUserAccessableEnterprises(ctx context.Context, req *pb.UserIdInput) (*pb.ListCompanyOrEnterpriseResponse, error) {
	s.log.Info("ListUserAccessableEnterprises: ", req.UserId)
	res := pb.ListCompanyOrEnterpriseResponse{
		Ids:   []string{},
		IsAll: false,
	}
	defer func() {
		if r := recover(); r != nil {
			s.log.Error(r)
		}
	}()
	isAll, _, ids := s.permissionService.ListUserAccessableEnterprises(req.UserId)
	if ids != nil {
		res.Ids = *ids
	}
	res.IsAll = isAll
	s.log.Debug("ListUserAccessableEnterprises: ", res)
	return &res, nil
}

// ValidateUserPermission checks if a user has a specific permission
func (s *PermissionServer) ValidateUserPermission(ctx context.Context, req *pb.ValidateUserPermissionRequest) (*pb.ValidatePermissionResponse, error) {
	s.log.Info("ValidateUserPermission: ", req.UserId, req.FeatureKey, req.Operation)
	if req.UserId == "" || req.FeatureKey == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id and feature_key are required")
	}

	// Convert operation to the appropriate type
	operation := transport.ValidateOperations(req.Operation)
	if operation < 1 || operation > 3 {
		return nil, status.Error(codes.InvalidArgument, "operation must be 1 (Read), 2 (Write), or 3 (Both)")
	}

	res, err := s.validatePermission(func() {
		s.permissionService.ValidateUserPermission(
			req.UserId,
			transport.Features(req.FeatureKey),
			operation,
		)
	})
	s.log.Debug("ValidateUserPermission: ", res)
	return res, err
}

// ValidateCompanyAccess checks if a user has access to a specific company
func (s *PermissionServer) ValidateCompanyAccess(ctx context.Context, req *pb.ValidateCompanyAccessRequest) (*pb.ValidateAccessResponse, error) {
	s.log.Info("ValidateCompanyAccess", req.UserId, req.CompanyId)
	if req.UserId == "" || req.CompanyId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id and company_id are required")
	}

	res, err := s.validateAccess(func() {
		s.permissionService.ValidateCompanyAccess(req.UserId, req.CompanyId)
	})
	s.log.Debug("ValidateCompanyAccess: ", res)
	return res, err
}

// ValidateEnterpriseAccess checks if a user has access to a specific enterprise
func (s *PermissionServer) ValidateEnterpriseAccess(ctx context.Context, req *pb.ValidateEnterpriseAccessRequest) (*pb.ValidateAccessResponse, error) {
	s.log.Info("ValidateEnterpriseAccess: ", req.UserId, req.EnterpriseId)
	if req.UserId == "" || req.EnterpriseId == "" {
		return nil, status.Error(codes.InvalidArgument, "user_id and enterprise_id are required")
	}

	res, err := s.validateAccess(func() {
		s.permissionService.ValidateEnterpriseAccess(req.UserId, req.EnterpriseId)
	})
	s.log.Debug("ValidateEnterpriseAccess: ", res)
	return res, err
}

// validatePermission is a helper function to handle common permission validation logic
func (s *PermissionServer) validatePermission(validationFunc func()) (*pb.ValidatePermissionResponse, error) {
	response := &pb.ValidatePermissionResponse{
		HasPermission: true,
		ErrorMessage:  "",
	}

	defer func() {
		if r := recover(); r != nil {
			response.HasPermission = false
			if e, ok := r.(_err.AppError); ok {
				response.ErrorMessage = e.Error()
			} else {
				response.ErrorMessage = "Internal server error"
			}
		}
	}()

	validationFunc()
	return response, nil
}

// validateAccess is a helper function to handle common access validation logic
func (s *PermissionServer) validateAccess(validationFunc func()) (*pb.ValidateAccessResponse, error) {
	response := &pb.ValidateAccessResponse{
		HasAccess:    true,
		ErrorMessage: "",
	}

	defer func() {
		if r := recover(); r != nil {
			response.HasAccess = false
			if e, ok := r.(_err.AppError); ok {
				response.ErrorMessage = e.Error()
			} else {
				response.ErrorMessage = "Internal server error"
			}
		}
	}()

	validationFunc()
	return response, nil
}

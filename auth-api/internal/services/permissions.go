package services

import (
	"auth-api/internal/common/_err"
	"auth-api/internal/common/logger"
	"auth-api/internal/common/transport"
	"auth-api/internal/config"
	"auth-api/internal/db"
	"auth-api/internal/db/models"
	"auth-api/internal/db/repository"
	"context"
	"fmt"
	"slices"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/v2/bson"
)

type PermissionService struct {
	Repo   *repository.Repositories
	Ctx    context.Context
	Log    *logger.Logger
	Config *config.Config
}

func NewPermissionService(
	ctx context.Context,
	repo *repository.Repositories,
	log *logger.Logger,
	config *config.Config,
) *PermissionService {
	return &PermissionService{
		Repo:   repo,
		Ctx:    ctx,
		Log:    log,
		Config: config,
	}
}

func (s *PermissionService) ListPermissionOptions() *[]transport.PermissionOptions {
	return &[]transport.PermissionOptions{
		{Label: "Carrier Hub: Porting Access", Value: string(models.PermissionPortingAccess)},
		{Label: "Data Center Monitor", Value: string(models.PermissionDCMonitor)},
		{Label: "Warehouse QC Wizard", Value: string(models.PermissionWarehouseQc)},
		{Label: "Eth 0/3 Disable", Value: string(models.PermissionEth3)},
		{Label: "PCAP Lookup", Value: string(models.PermissionPcap)},
		{Label: "Port Configuration Management", Value: string(models.PermissionPortConfig)},
		{Label: "Engineering Lock", Value: string(models.PermissionEngineeringLock)},
		{Label: "Registration Offset", Value: string(models.PermissionRegistrationOffSet)},
		{Label: "Data Center Registration", Value: string(models.PermissionDataCenter)},
		{Label: "Power Save", Value: string(models.PermissionPowerSave)},
		{Label: "Cancel Integration", Value: string(models.PermissionCancelIntegrationAccess)},
		{Label: "PCAP Analyzer", Value: string(models.PermissionRealTimeActivityEnabled)},
		{Label: "Number Carrier Change Access", Value: string(models.PermissionNumberCarrierChangeAccess)},
		{Label: "NOC Wizard", Value: string(models.PermissionNocWizardAccess)},
		{Label: "IP Phone Management", Value: string(models.PermissionPhoneAccess)},
		{Label: "International Dialing", Value: string(models.PermissionInternationalDialingAccess)},
		{Label: "REC Menu", Value: string(models.PermissionRecDiagnostics)},
		{Label: "Omit 9", Value: string(models.PermissionOmitLeading)},
		{Label: "OOBM", Value: string(models.PermissionOutBandManagement)},
		{Label: "Call Waiting", Value: string(models.PermissionCallWaiting)},
		{Label: "Delete EPI", Value: string(models.PermissionDeleteEpi)},
		{Label: "Modify EPI", Value: string(models.PermissionModifyEPIS)},
		{Label: "Enhanced Routing", Value: string(models.PermissionEnhancedRouting)},
		{Label: "Number Action", Value: string(models.PermissionNumberBulkActionsAccess)},
		{Label: "EPI Utilities", Value: string(models.PermissionEpiUtilitiesPermission)},
		{Label: "Caller ID Masking", Value: string(models.PermissionCallerIdMaskingAccess)},
		{Label: "Device Monitoring", Value: string(models.PermissionEnableMonitoring)},
		{Label: "Events", Value: string(models.PermissionEventsAccess)},
		{Label: "EPIK Updates", Value: string(models.PermissionEpikUpdates)},
		{Label: "Admin Dashboard Access", Value: string(models.PermissionAdminDashboardAccess)},
		{Label: "Enhanced Analyzer", Value: string(models.PermissionEnhancedAnalyzer)},
		{Label: "TTY Access Management", Value: string(models.PermissionTtyOptionsAccess)},
		{Label: "Enable Alarm Relay Protocol", Value: string(models.PermissionEnableAlarmProtocol)},
		{Label: "Documentation Tool", Value: string(models.PermissionDocToolAccess)},
		{Label: "EPI firmware update", Value: string(models.PermissionEpiFirmwareUpdate)},
		{Label: "PM Tools", Value: string(models.PermissionPmToolsAccess)},
		{Label: "TC Tools", Value: string(models.PermissionTcToolsAccess)},
	}
}

func (s *PermissionService) ListPermissionGroups() *[]models.PermissionGroup {
	data := s.Repo.PermissionGroup.Find(&models.PermissionGroup{})
	return data
}

func assignFeatures(schema *transport.UISchema, resource transport.Resources, features map[transport.Features]*transport.FeatureMeta) {
	if (*schema)[resource] == nil {
		(*schema)[resource] = []*transport.ResourceEntry{}
	}

	for feature, meta := range features {
		(*schema)[resource] = append((*schema)[resource], &transport.ResourceEntry{Feature: feature, Meta: *meta})
	}
}

func createFeatureMeta(read, write bool, resourceKey ...transport.Resources) *transport.FeatureMeta {
	meta := &transport.FeatureMeta{Read: read, Write: write}
	if len(resourceKey) > 0 {
		meta.ResourceKey = resourceKey[0]
	}
	return meta
}

func (s *PermissionService) generateResourceUISchema(
	access *transport.UserAccessAndPermissions,
	uiPermissionSchema *transport.UISchema,
	resource transport.Resources,
	features map[transport.Features]*transport.FeatureMeta,
) {
	// don't add features if there are no features unless user is epik
	if len(features) == 0 && !access.EpikEngineering {
		return
	}
	assignFeatures(uiPermissionSchema, resource, features)
}

type menuResource struct {
	resource transport.Resources
	title    string
}

func buildMenuItems(uiPermissionSchema transport.UISchema) []*transport.MenuItems {
	var menuItems []*transport.MenuItems

	simpleMenus := []menuResource{
		{transport.EdgeDevicesResource, "Edge Devices"},
		{transport.CompanyResource, "Companies"},
		{transport.UserResource, "Users"},
	}

	for _, menu := range simpleMenus {
		if uiPermissionSchema[menu.resource] != nil {
			menuItems = append(menuItems, &transport.MenuItems{
				Title:       menu.title,
				ResourceKey: menu.resource,
			})
		}
	}

	// Usage section with nested items
	usageSection := buildNestedMenuSection("Usage", []menuResource{
		{transport.CallsResource, "Calls"},
		{transport.FAXResource, "FAX"},
		{transport.NumbersResource, "Numbers"},
	}, uiPermissionSchema)

	if usageSection != nil {
		menuItems = append(menuItems, usageSection)
	}

	// Carrier Hub section with nested items
	carrierSection := buildNestedMenuSection("Carrier Hub", []menuResource{
		{transport.NumberOrderResource, "Number Ordering"},
		{transport.PortingResource, "Porting"},
		{transport.E911Resource, "E911"},
		{transport.CarrierLookupResource, "Carrier Lookup"},
	}, uiPermissionSchema)

	if carrierSection != nil {
		menuItems = append(menuItems, carrierSection)
	}

	return menuItems
}

func buildNestedMenuSection(
	sectionTitle string,
	resources []menuResource,
	uiPermissionSchema transport.UISchema,
) *transport.MenuItems {
	var children []transport.MenuItems

	for _, res := range resources {
		if uiPermissionSchema[res.resource] != nil {
			children = append(children, transport.MenuItems{
				Title:       res.title,
				ResourceKey: res.resource,
			})
		}
	}

	if len(children) == 0 {
		return nil
	}

	return &transport.MenuItems{
		Title:       sectionTitle,
		ResourceKey: "",
		Childs:      children,
	}
}

func buildUISchemaMap(uiPermissionSchema transport.UISchema) transport.UISchemaMap {
	schemaMap := make(transport.UISchemaMap)

	for resourceKey, value := range uiPermissionSchema {
		schemaMap[resourceKey] = make(map[transport.Features]*transport.FeatureMeta)
		for _, feature := range value {
			schemaMap[resourceKey][feature.Feature] = &feature.Meta
		}
	}

	return schemaMap
}

func (s *PermissionService) cacheUserData(userId string, access transport.UserAccessAndPermissions, schemaMap transport.UISchemaMap) {
	cacheDuration := time.Minute * 15
	db.Redis.Set(fmt.Sprintf("%s:access", userId), access, cacheDuration)
	db.Redis.Set(fmt.Sprintf("%s:permissions", userId), schemaMap, cacheDuration)
}

func (s *PermissionService) generateUserUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}
	if access.EpikEngineering {
		features[transport.UserFeatureListUser] = createFeatureMeta(true, true)
		features[transport.UserFeatureAdd] = createFeatureMeta(true, true)
		features[transport.UserFeatureUpdate] = createFeatureMeta(true, true)
		features[transport.UserFeatureSoftDelete] = createFeatureMeta(true, true)
		features[transport.UserFeaturePermissionGroup] = createFeatureMeta(true, true, transport.PermissionGroupResource)
		features[transport.UserFeatureUserApproval] = createFeatureMeta(true, true, transport.UserApprovalResource)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.UserResource, features)
}

func (s *PermissionService) generatePermissionGroupUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}
	if access.EpikEngineering {
		features[transport.PermissionGroupFeatureList] = createFeatureMeta(true, true)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.PermissionGroupResource, features)
}

func (s *PermissionService) generateUserApprovalUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}
	if access.EpikEngineering {
		features[transport.UserApprovalFeatureList] = createFeatureMeta(true, true)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.UserApprovalResource, features)
}

func (s *PermissionService) generateCompanyUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}

	if access.EpikEngineering {
		features[transport.CompanyFeatureList] = createFeatureMeta(true, true)
		features[transport.CompanyFeatureAdd] = createFeatureMeta(true, true)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.CompanyResource, features)
}

func (s *PermissionService) generateEdgeDevicesUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}
	if access.EpikEngineering {
		features[transport.EdgeDevicesFeatureList] = createFeatureMeta(true, true)
		features[transport.EdgeDevicesFeatureAdd] = createFeatureMeta(true, true)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.EdgeDevicesResource, features)
}

func (s *PermissionService) generateBoxDetailsUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	features := map[transport.Features]*transport.FeatureMeta{}
	integrationAccess := access.Permissions[models.PermissionCancelIntegrationAccess]
	if access.EpikEngineering || integrationAccess.Read || integrationAccess.Write {
		features[transport.BoxDetailsIntegrateAccess] = createFeatureMeta(integrationAccess.Read, integrationAccess.Write)
	}
	if access.EpikEngineering {
		features[transport.BoxDetailsFeatureAdminFlag] = createFeatureMeta(true, true)
		features[transport.BoxDetailsActivateButton] = createFeatureMeta(true, true)
		features[transport.BoxDetailsVpnViewAccess] = createFeatureMeta(true, true)
		features[transport.BoxDetailsLteAccess] = createFeatureMeta(true, true)
		features[transport.BoxDetailsFeatureViewButton] = createFeatureMeta(true, true)
	}

	s.generateResourceUISchema(access, uiPermissionSchema, transport.BoxDetailsResource, features)
}

func (s *PermissionService) generateEmptyResourceUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema, resource transport.Resources) {
	features := map[transport.Features]*transport.FeatureMeta{}
	s.generateResourceUISchema(access, uiPermissionSchema, resource, features)
}

// Simplified empty schema generators
func (s *PermissionService) generateCallsUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.CallsResource)
}

func (s *PermissionService) generateFaxUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.FAXResource)
}

func (s *PermissionService) generateNumbersUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.NumbersResource)
}

func (s *PermissionService) generateNumberOrderingUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.NumberOrderResource)
}

func (s *PermissionService) generateNumberPortingUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.PortingResource)
}

func (s *PermissionService) generateNumberE911UISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.E911Resource)
}

func (s *PermissionService) generateNumberCarrierLookupUISchema(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEmptyResourceUISchema(access, uiPermissionSchema, transport.CarrierLookupResource)
}

func (s *PermissionService) ListUserPermission(userId *string) (*transport.UISchema, []*transport.MenuItems) {
	user := s.Repo.User.FindByID(*userId)
	uiPermissionSchema := make(transport.UISchema)

	userAccess := s.Repo.UserAccess.FindOne(&models.UserAccess{UserId: user.ID})
	userAccessPopulated := s.Repo.UserAccess.Populate(userAccess, []repository.UserAccessPopulatePath{repository.PopulateUserAccessPermissionGroup})

	access := s.buildUserAccessObject(user, userAccessPopulated)

	s.generateAllUISchemas(&access, &uiPermissionSchema)

	schemaMap := buildUISchemaMap(uiPermissionSchema)
	s.cacheUserData(*userId, access, schemaMap)

	menuItems := buildMenuItems(uiPermissionSchema)

	return &uiPermissionSchema, menuItems
}

func (s *PermissionService) buildUserAccessObject(user *models.User, userAccessPopulated *models.UserAccessPopulated) transport.UserAccessAndPermissions {
	var Companies, Enterprises []string

	for _, c := range userAccessPopulated.CompanyAccess {
		Companies = append(Companies, c.CompanyId.Hex())
	}
	for _, e := range userAccessPopulated.EnterpriseAccess {
		Enterprises = append(Enterprises, e.EnterpriseId.Hex())
	}

	dbCompanies := s.Repo.Company.BsonFind(&bson.M{"enterprises": bson.M{"$in": Enterprises}}, []string{"_id"})
	allCompanies := slices.Clone(Companies)
	for _, c := range *dbCompanies {
		allCompanies = append(allCompanies, c.ID.Hex())
	}

	access := transport.UserAccessAndPermissions{
		EpikEngineering: userAccessPopulated.EpikEngineering,
		Manager:         userAccessPopulated.Manager,
		Company:         user.Company.Hex(),
		Companies:       Companies,
		Enterprises:     Enterprises,
		Permissions:     map[models.Permission]models.PermissionsSchema{}, //userAccessPopulated.AddonPermissions,
		AllCompanies:    allCompanies,
	}

	if userAccessPopulated.AddonPermissions != nil {
		access.Permissions = userAccessPopulated.AddonPermissions
	}

	// Merge permissions from all permission groups
	for _, doc := range userAccessPopulated.PermissionGroupsDoc {
		for permKey, permVal := range doc.Permissions {
			existing := access.Permissions[permKey]
			access.Permissions[permKey] = models.PermissionsSchema{
				Read:  existing.Read || permVal.Read,
				Write: existing.Write || permVal.Write,
			}
		}
	}

	return access
}

func (s *PermissionService) generateAllUISchemas(access *transport.UserAccessAndPermissions, uiPermissionSchema *transport.UISchema) {
	s.generateEdgeDevicesUISchema(access, uiPermissionSchema)
	s.generateBoxDetailsUISchema(access, uiPermissionSchema)
	s.generateCompanyUISchema(access, uiPermissionSchema)
	s.generateUserUISchema(access, uiPermissionSchema)
	s.generatePermissionGroupUISchema(access, uiPermissionSchema)
	s.generateUserApprovalUISchema(access, uiPermissionSchema)
	s.generateCallsUISchema(access, uiPermissionSchema)
	s.generateFaxUISchema(access, uiPermissionSchema)
	s.generateNumbersUISchema(access, uiPermissionSchema)
	s.generateNumberOrderingUISchema(access, uiPermissionSchema)
	s.generateNumberPortingUISchema(access, uiPermissionSchema)
	s.generateNumberE911UISchema(access, uiPermissionSchema)
	s.generateNumberCarrierLookupUISchema(access, uiPermissionSchema)
}

func (s *PermissionService) ValidateUserPermission(userId string, featureKey transport.Features, operation transport.ValidateOperations) {
	var schemaMap = make(transport.UISchemaMap)
	err := db.Redis.GetObject(fmt.Sprintf("%s:permissions", userId), &schemaMap)
	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}
	resourceKey := transport.Resources(strings.Split(string(featureKey), "_")[0])

	val := schemaMap[resourceKey][featureKey]

	if val == nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}
	read, write := val.Read, val.Write
	if operation == transport.ValidateWrite && !write {
		panic(_err.New(_err.ErrForbidden, ""))
	}
	if operation == transport.ValidateRead && !read {
		panic(_err.New(_err.ErrForbidden, ""))
	}
	if operation == transport.ValidateBoth && (!read || !write) {
		panic(_err.New(_err.ErrForbidden, ""))
	}
}

func (s *PermissionService) ValidateCompanyAccess(userId string, companyId string) {
	var access transport.UserAccessAndPermissions
	err := db.Redis.GetObject(fmt.Sprintf("%s:access", userId), &access)

	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}

	if access.EpikEngineering {
		return
	}
	if slices.Contains(access.AllCompanies, companyId) {
		return
	}
	panic(_err.New(_err.ErrForbidden, ""))
}

func (s *PermissionService) ValidateEnterpriseAccess(userId string, enterpriseId string) {
	var access transport.UserAccessAndPermissions
	err := db.Redis.GetObject(fmt.Sprintf("%s:access", userId), &access)

	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}

	if access.EpikEngineering {
		return
	}
	if slices.Contains(access.Enterprises, enterpriseId) {
		return
	}
	panic(_err.New(_err.ErrForbidden, ""))
}

func (s *PermissionService) ListUserAccessableCompanies(userId string) (bool, *[]bson.ObjectID, *[]string) {
	var access transport.UserAccessAndPermissions
	err := db.Redis.GetObject(fmt.Sprintf("%s:access", userId), &access)
	s.Log.Info("access", access)
	s.Log.Error("err", err)
	s.Log.Info("access.Companies", access.Companies)
	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}

	if access.EpikEngineering {
		s.Log.Info("returning all companies")
		return true, nil, nil
	}
	s.Log.Info("not returning all companies", access.Companies)
	bsonIds := make([]bson.ObjectID, len(access.Companies))
	for i, value := range access.Companies {
		bsonIds[i] = _err.Must(bson.ObjectIDFromHex(value))

	}
	s.Log.Info("bsonIds", bsonIds)
	s.Log.Info("access.Companies", access.Companies)
	s.Log.Info("access.AllCompanies", access.AllCompanies)
	return false, &bsonIds, &access.Companies
}

func (s *PermissionService) ListUserAccessableCompaniesWithEnterprises(userId string) (bool, *[]bson.ObjectID, *[]string) {
	s.Log.Info("ListUserAccessableCompaniesWithEnterprises123", userId)
	var access transport.UserAccessAndPermissions
	err := db.Redis.GetObject(fmt.Sprintf("%s:access", userId), &access)
	// s.Log.Info("access", access)
	s.Log.Error("err", err)
	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}
	if access.EpikEngineering {
		s.Log.Info("returning all companies")
		return true, nil, nil
	}
	s.Log.Info("access.AllCompanies", access.AllCompanies)
	bsonIds := make([]bson.ObjectID, len(access.AllCompanies))
	for i, value := range access.AllCompanies {
		bsonIds[i] = _err.Must(bson.ObjectIDFromHex(value))

	}

	return false, &bsonIds, &access.Companies
}

func (s *PermissionService) ListUserAccessableEnterprises(userId string) (bool, *[]bson.ObjectID, *[]string) {
	var access transport.UserAccessAndPermissions
	err := db.Redis.GetObject(fmt.Sprintf("%s:access", userId), &access)

	if err != nil {
		panic(_err.New(_err.ErrForbidden, ""))
	}

	if access.EpikEngineering {
		return true, nil, nil
	}

	bsonIds := make([]bson.ObjectID, len(access.Enterprises))
	for i, value := range access.Enterprises {
		bsonIds[i] = _err.Must(bson.ObjectIDFromHex(value))

	}

	return false, &bsonIds, &access.Enterprises
}

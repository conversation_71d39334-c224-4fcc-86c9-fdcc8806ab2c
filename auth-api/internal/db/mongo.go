package db

import (
	"auth-api/internal/common/logger"
	"context"
	"regexp"
	"time"

	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"go.mongodb.org/mongo-driver/v2/mongo/readpref"
)

type MongoClient struct {
	Client *mongo.Client
	DB     *mongo.Database
	Ctx    context.Context
}

var Client *mongo.Client
var DB *mongo.Database

func Connect(uri string) *MongoClient {
	log := logger.NewLogger()
	docs := "www.mongodb.com/docs/drivers/go/current/"
	if uri == "" {
		log.Fatal("Set your 'MONGODB_URI' environment variable. " +
			"See: " + docs + "usage-examples/#environment-variable")
	}
	bsonOpts := &options.BSONOptions{
		UseJSONStructTags: true,
		NilSliceAsEmpty:   true,
		OmitEmpty:         true,
	}

	client, err := mongo.
		Connect(
			options.Client().
				ApplyURI(uri).
				SetReadPreference(readpref.SecondaryPreferred()).
				SetBSONOptions(bsonOpts),
		)
	if err != nil {
		log.Fatal("MongoDB connection failed: ", err)
	}

	// Ping to verify connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.Ping(ctx, nil); err != nil {
		log.Fatal("MongoDB ping failed: ", err)
	}

	// Extract DB name
	dbNameMatch := regexp.MustCompile(`^mongodb(?:\+srv)?:\/\/[^/]+\/([^/?]+)`).FindStringSubmatch(uri)
	if len(dbNameMatch) < 2 {
		log.Fatal("Could not extract database name from URI")
	}
	dbName := dbNameMatch[1]
	Client = client
	DB = client.Database(dbName)
	log.Info("DB connected.")
	return &MongoClient{
		Client: client,
		DB:     client.Database(dbName),
		Ctx:    ctx,
	}
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: proto/permission_service.proto

package permission

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PermissionService_UnaryEcho_FullMethodName                                  = "/auth.PermissionService/UnaryEcho"
	PermissionService_ValidateUserPermission_FullMethodName                     = "/auth.PermissionService/ValidateUserPermission"
	PermissionService_ValidateCompanyAccess_FullMethodName                      = "/auth.PermissionService/ValidateCompanyAccess"
	PermissionService_ValidateEnterpriseAccess_FullMethodName                   = "/auth.PermissionService/ValidateEnterpriseAccess"
	PermissionService_ListUserAccessableCompaniesWithEnterprises_FullMethodName = "/auth.PermissionService/ListUserAccessableCompaniesWithEnterprises"
	PermissionService_ListUserAccessableEnterprises_FullMethodName              = "/auth.PermissionService/ListUserAccessableEnterprises"
)

// PermissionServiceClient is the client API for PermissionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermissionServiceClient interface {
	UnaryEcho(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error)
	// Validate if a user has a specific permission
	ValidateUserPermission(ctx context.Context, in *ValidateUserPermissionRequest, opts ...grpc.CallOption) (*ValidatePermissionResponse, error)
	// Validate if a user has access to a specific company
	ValidateCompanyAccess(ctx context.Context, in *ValidateCompanyAccessRequest, opts ...grpc.CallOption) (*ValidateAccessResponse, error)
	// Validate if a user has access to a specific enterprise
	ValidateEnterpriseAccess(ctx context.Context, in *ValidateEnterpriseAccessRequest, opts ...grpc.CallOption) (*ValidateAccessResponse, error)
	ListUserAccessableCompaniesWithEnterprises(ctx context.Context, in *UserIdInput, opts ...grpc.CallOption) (*ListCompanyOrEnterpriseResponse, error)
	ListUserAccessableEnterprises(ctx context.Context, in *UserIdInput, opts ...grpc.CallOption) (*ListCompanyOrEnterpriseResponse, error)
}

type permissionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPermissionServiceClient(cc grpc.ClientConnInterface) PermissionServiceClient {
	return &permissionServiceClient{cc}
}

func (c *permissionServiceClient) UnaryEcho(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EchoResponse)
	err := c.cc.Invoke(ctx, PermissionService_UnaryEcho_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ValidateUserPermission(ctx context.Context, in *ValidateUserPermissionRequest, opts ...grpc.CallOption) (*ValidatePermissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidatePermissionResponse)
	err := c.cc.Invoke(ctx, PermissionService_ValidateUserPermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ValidateCompanyAccess(ctx context.Context, in *ValidateCompanyAccessRequest, opts ...grpc.CallOption) (*ValidateAccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateAccessResponse)
	err := c.cc.Invoke(ctx, PermissionService_ValidateCompanyAccess_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ValidateEnterpriseAccess(ctx context.Context, in *ValidateEnterpriseAccessRequest, opts ...grpc.CallOption) (*ValidateAccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateAccessResponse)
	err := c.cc.Invoke(ctx, PermissionService_ValidateEnterpriseAccess_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ListUserAccessableCompaniesWithEnterprises(ctx context.Context, in *UserIdInput, opts ...grpc.CallOption) (*ListCompanyOrEnterpriseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCompanyOrEnterpriseResponse)
	err := c.cc.Invoke(ctx, PermissionService_ListUserAccessableCompaniesWithEnterprises_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) ListUserAccessableEnterprises(ctx context.Context, in *UserIdInput, opts ...grpc.CallOption) (*ListCompanyOrEnterpriseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCompanyOrEnterpriseResponse)
	err := c.cc.Invoke(ctx, PermissionService_ListUserAccessableEnterprises_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermissionServiceServer is the server API for PermissionService service.
// All implementations must embed UnimplementedPermissionServiceServer
// for forward compatibility.
type PermissionServiceServer interface {
	UnaryEcho(context.Context, *EchoRequest) (*EchoResponse, error)
	// Validate if a user has a specific permission
	ValidateUserPermission(context.Context, *ValidateUserPermissionRequest) (*ValidatePermissionResponse, error)
	// Validate if a user has access to a specific company
	ValidateCompanyAccess(context.Context, *ValidateCompanyAccessRequest) (*ValidateAccessResponse, error)
	// Validate if a user has access to a specific enterprise
	ValidateEnterpriseAccess(context.Context, *ValidateEnterpriseAccessRequest) (*ValidateAccessResponse, error)
	ListUserAccessableCompaniesWithEnterprises(context.Context, *UserIdInput) (*ListCompanyOrEnterpriseResponse, error)
	ListUserAccessableEnterprises(context.Context, *UserIdInput) (*ListCompanyOrEnterpriseResponse, error)
	mustEmbedUnimplementedPermissionServiceServer()
}

// UnimplementedPermissionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPermissionServiceServer struct{}

func (UnimplementedPermissionServiceServer) UnaryEcho(context.Context, *EchoRequest) (*EchoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnaryEcho not implemented")
}
func (UnimplementedPermissionServiceServer) ValidateUserPermission(context.Context, *ValidateUserPermissionRequest) (*ValidatePermissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateUserPermission not implemented")
}
func (UnimplementedPermissionServiceServer) ValidateCompanyAccess(context.Context, *ValidateCompanyAccessRequest) (*ValidateAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCompanyAccess not implemented")
}
func (UnimplementedPermissionServiceServer) ValidateEnterpriseAccess(context.Context, *ValidateEnterpriseAccessRequest) (*ValidateAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateEnterpriseAccess not implemented")
}
func (UnimplementedPermissionServiceServer) ListUserAccessableCompaniesWithEnterprises(context.Context, *UserIdInput) (*ListCompanyOrEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccessableCompaniesWithEnterprises not implemented")
}
func (UnimplementedPermissionServiceServer) ListUserAccessableEnterprises(context.Context, *UserIdInput) (*ListCompanyOrEnterpriseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserAccessableEnterprises not implemented")
}
func (UnimplementedPermissionServiceServer) mustEmbedUnimplementedPermissionServiceServer() {}
func (UnimplementedPermissionServiceServer) testEmbeddedByValue()                           {}

// UnsafePermissionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermissionServiceServer will
// result in compilation errors.
type UnsafePermissionServiceServer interface {
	mustEmbedUnimplementedPermissionServiceServer()
}

func RegisterPermissionServiceServer(s grpc.ServiceRegistrar, srv PermissionServiceServer) {
	// If the following call pancis, it indicates UnimplementedPermissionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PermissionService_ServiceDesc, srv)
}

func _PermissionService_UnaryEcho_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EchoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).UnaryEcho(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_UnaryEcho_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).UnaryEcho(ctx, req.(*EchoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ValidateUserPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateUserPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ValidateUserPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_ValidateUserPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ValidateUserPermission(ctx, req.(*ValidateUserPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ValidateCompanyAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCompanyAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ValidateCompanyAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_ValidateCompanyAccess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ValidateCompanyAccess(ctx, req.(*ValidateCompanyAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ValidateEnterpriseAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateEnterpriseAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ValidateEnterpriseAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_ValidateEnterpriseAccess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ValidateEnterpriseAccess(ctx, req.(*ValidateEnterpriseAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ListUserAccessableCompaniesWithEnterprises_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ListUserAccessableCompaniesWithEnterprises(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_ListUserAccessableCompaniesWithEnterprises_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ListUserAccessableCompaniesWithEnterprises(ctx, req.(*UserIdInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_ListUserAccessableEnterprises_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ListUserAccessableEnterprises(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PermissionService_ListUserAccessableEnterprises_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ListUserAccessableEnterprises(ctx, req.(*UserIdInput))
	}
	return interceptor(ctx, in, info, handler)
}

// PermissionService_ServiceDesc is the grpc.ServiceDesc for PermissionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PermissionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth.PermissionService",
	HandlerType: (*PermissionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UnaryEcho",
			Handler:    _PermissionService_UnaryEcho_Handler,
		},
		{
			MethodName: "ValidateUserPermission",
			Handler:    _PermissionService_ValidateUserPermission_Handler,
		},
		{
			MethodName: "ValidateCompanyAccess",
			Handler:    _PermissionService_ValidateCompanyAccess_Handler,
		},
		{
			MethodName: "ValidateEnterpriseAccess",
			Handler:    _PermissionService_ValidateEnterpriseAccess_Handler,
		},
		{
			MethodName: "ListUserAccessableCompaniesWithEnterprises",
			Handler:    _PermissionService_ListUserAccessableCompaniesWithEnterprises_Handler,
		},
		{
			MethodName: "ListUserAccessableEnterprises",
			Handler:    _PermissionService_ListUserAccessableEnterprises_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/permission_service.proto",
}

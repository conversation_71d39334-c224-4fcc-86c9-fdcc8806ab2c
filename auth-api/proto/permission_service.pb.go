// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: proto/permission_service.proto

package permission

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EchoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoRequest) Reset() {
	*x = EchoRequest{}
	mi := &file_proto_permission_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRequest) ProtoMessage() {}

func (x *EchoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRequest.ProtoReflect.Descriptor instead.
func (*EchoRequest) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{0}
}

func (x *EchoRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type EchoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoResponse) Reset() {
	*x = EchoResponse{}
	mi := &file_proto_permission_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoResponse) ProtoMessage() {}

func (x *EchoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoResponse.ProtoReflect.Descriptor instead.
func (*EchoResponse) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{1}
}

func (x *EchoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Permission validation request
type ValidateUserPermissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FeatureKey    string                 `protobuf:"bytes,2,opt,name=feature_key,json=featureKey,proto3" json:"feature_key,omitempty"`
	Operation     int32                  `protobuf:"varint,3,opt,name=operation,proto3" json:"operation,omitempty"` // 1=Read, 2=Write, 3=Both
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateUserPermissionRequest) Reset() {
	*x = ValidateUserPermissionRequest{}
	mi := &file_proto_permission_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateUserPermissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUserPermissionRequest) ProtoMessage() {}

func (x *ValidateUserPermissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUserPermissionRequest.ProtoReflect.Descriptor instead.
func (*ValidateUserPermissionRequest) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateUserPermissionRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateUserPermissionRequest) GetFeatureKey() string {
	if x != nil {
		return x.FeatureKey
	}
	return ""
}

func (x *ValidateUserPermissionRequest) GetOperation() int32 {
	if x != nil {
		return x.Operation
	}
	return 0
}

// Common response for permission validation
type ValidatePermissionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasPermission bool                   `protobuf:"varint,1,opt,name=has_permission,json=hasPermission,proto3" json:"has_permission,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidatePermissionResponse) Reset() {
	*x = ValidatePermissionResponse{}
	mi := &file_proto_permission_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidatePermissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidatePermissionResponse) ProtoMessage() {}

func (x *ValidatePermissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidatePermissionResponse.ProtoReflect.Descriptor instead.
func (*ValidatePermissionResponse) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{3}
}

func (x *ValidatePermissionResponse) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

func (x *ValidatePermissionResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// Company access validation request
type ValidateCompanyAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CompanyId     string                 `protobuf:"bytes,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateCompanyAccessRequest) Reset() {
	*x = ValidateCompanyAccessRequest{}
	mi := &file_proto_permission_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateCompanyAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCompanyAccessRequest) ProtoMessage() {}

func (x *ValidateCompanyAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCompanyAccessRequest.ProtoReflect.Descriptor instead.
func (*ValidateCompanyAccessRequest) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{4}
}

func (x *ValidateCompanyAccessRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateCompanyAccessRequest) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

// Enterprise access validation request
type ValidateEnterpriseAccessRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	EnterpriseId  string                 `protobuf:"bytes,2,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateEnterpriseAccessRequest) Reset() {
	*x = ValidateEnterpriseAccessRequest{}
	mi := &file_proto_permission_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateEnterpriseAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateEnterpriseAccessRequest) ProtoMessage() {}

func (x *ValidateEnterpriseAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateEnterpriseAccessRequest.ProtoReflect.Descriptor instead.
func (*ValidateEnterpriseAccessRequest) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{5}
}

func (x *ValidateEnterpriseAccessRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateEnterpriseAccessRequest) GetEnterpriseId() string {
	if x != nil {
		return x.EnterpriseId
	}
	return ""
}

// Common response for access validation
type ValidateAccessResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasAccess     bool                   `protobuf:"varint,1,opt,name=has_access,json=hasAccess,proto3" json:"has_access,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateAccessResponse) Reset() {
	*x = ValidateAccessResponse{}
	mi := &file_proto_permission_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateAccessResponse) ProtoMessage() {}

func (x *ValidateAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateAccessResponse.ProtoReflect.Descriptor instead.
func (*ValidateAccessResponse) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{6}
}

func (x *ValidateAccessResponse) GetHasAccess() bool {
	if x != nil {
		return x.HasAccess
	}
	return false
}

func (x *ValidateAccessResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// to list all accessable companies or enterprises
type UserIdInput struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdInput) Reset() {
	*x = UserIdInput{}
	mi := &file_proto_permission_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdInput) ProtoMessage() {}

func (x *UserIdInput) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdInput.ProtoReflect.Descriptor instead.
func (*UserIdInput) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{7}
}

func (x *UserIdInput) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Common response for access validation
type ListCompanyOrEnterpriseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	IsAll         bool                   `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCompanyOrEnterpriseResponse) Reset() {
	*x = ListCompanyOrEnterpriseResponse{}
	mi := &file_proto_permission_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCompanyOrEnterpriseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCompanyOrEnterpriseResponse) ProtoMessage() {}

func (x *ListCompanyOrEnterpriseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_permission_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCompanyOrEnterpriseResponse.ProtoReflect.Descriptor instead.
func (*ListCompanyOrEnterpriseResponse) Descriptor() ([]byte, []int) {
	return file_proto_permission_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListCompanyOrEnterpriseResponse) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCompanyOrEnterpriseResponse) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

var File_proto_permission_service_proto protoreflect.FileDescriptor

const file_proto_permission_service_proto_rawDesc = "" +
	"\n" +
	"\x1eproto/permission_service.proto\x12\x04auth\"'\n" +
	"\vEchoRequest\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"(\n" +
	"\fEchoResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"w\n" +
	"\x1dValidateUserPermissionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1f\n" +
	"\vfeature_key\x18\x02 \x01(\tR\n" +
	"featureKey\x12\x1c\n" +
	"\toperation\x18\x03 \x01(\x05R\toperation\"h\n" +
	"\x1aValidatePermissionResponse\x12%\n" +
	"\x0ehas_permission\x18\x01 \x01(\bR\rhasPermission\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage\"V\n" +
	"\x1cValidateCompanyAccessRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\tR\tcompanyId\"_\n" +
	"\x1fValidateEnterpriseAccessRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12#\n" +
	"\renterprise_id\x18\x02 \x01(\tR\fenterpriseId\"\\\n" +
	"\x16ValidateAccessResponse\x12\x1d\n" +
	"\n" +
	"has_access\x18\x01 \x01(\bR\thasAccess\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage\"&\n" +
	"\vUserIdInput\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\"J\n" +
	"\x1fListCompanyOrEnterpriseResponse\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\tR\x03ids\x12\x15\n" +
	"\x06is_all\x18\x02 \x01(\bR\x05isAll2\xa9\x04\n" +
	"\x11PermissionService\x124\n" +
	"\tUnaryEcho\x12\x11.auth.EchoRequest\x1a\x12.auth.EchoResponse\"\x00\x12_\n" +
	"\x16ValidateUserPermission\x12#.auth.ValidateUserPermissionRequest\x1a .auth.ValidatePermissionResponse\x12Y\n" +
	"\x15ValidateCompanyAccess\x12\".auth.ValidateCompanyAccessRequest\x1a\x1c.auth.ValidateAccessResponse\x12_\n" +
	"\x18ValidateEnterpriseAccess\x12%.auth.ValidateEnterpriseAccessRequest\x1a\x1c.auth.ValidateAccessResponse\x12f\n" +
	"*ListUserAccessableCompaniesWithEnterprises\x12\x11.auth.UserIdInput\x1a%.auth.ListCompanyOrEnterpriseResponse\x12Y\n" +
	"\x1dListUserAccessableEnterprises\x12\x11.auth.UserIdInput\x1a%.auth.ListCompanyOrEnterpriseResponseB\x0eZ\f./permissionb\x06proto3"

var (
	file_proto_permission_service_proto_rawDescOnce sync.Once
	file_proto_permission_service_proto_rawDescData []byte
)

func file_proto_permission_service_proto_rawDescGZIP() []byte {
	file_proto_permission_service_proto_rawDescOnce.Do(func() {
		file_proto_permission_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_permission_service_proto_rawDesc), len(file_proto_permission_service_proto_rawDesc)))
	})
	return file_proto_permission_service_proto_rawDescData
}

var file_proto_permission_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proto_permission_service_proto_goTypes = []any{
	(*EchoRequest)(nil),                     // 0: auth.EchoRequest
	(*EchoResponse)(nil),                    // 1: auth.EchoResponse
	(*ValidateUserPermissionRequest)(nil),   // 2: auth.ValidateUserPermissionRequest
	(*ValidatePermissionResponse)(nil),      // 3: auth.ValidatePermissionResponse
	(*ValidateCompanyAccessRequest)(nil),    // 4: auth.ValidateCompanyAccessRequest
	(*ValidateEnterpriseAccessRequest)(nil), // 5: auth.ValidateEnterpriseAccessRequest
	(*ValidateAccessResponse)(nil),          // 6: auth.ValidateAccessResponse
	(*UserIdInput)(nil),                     // 7: auth.UserIdInput
	(*ListCompanyOrEnterpriseResponse)(nil), // 8: auth.ListCompanyOrEnterpriseResponse
}
var file_proto_permission_service_proto_depIdxs = []int32{
	0, // 0: auth.PermissionService.UnaryEcho:input_type -> auth.EchoRequest
	2, // 1: auth.PermissionService.ValidateUserPermission:input_type -> auth.ValidateUserPermissionRequest
	4, // 2: auth.PermissionService.ValidateCompanyAccess:input_type -> auth.ValidateCompanyAccessRequest
	5, // 3: auth.PermissionService.ValidateEnterpriseAccess:input_type -> auth.ValidateEnterpriseAccessRequest
	7, // 4: auth.PermissionService.ListUserAccessableCompaniesWithEnterprises:input_type -> auth.UserIdInput
	7, // 5: auth.PermissionService.ListUserAccessableEnterprises:input_type -> auth.UserIdInput
	1, // 6: auth.PermissionService.UnaryEcho:output_type -> auth.EchoResponse
	3, // 7: auth.PermissionService.ValidateUserPermission:output_type -> auth.ValidatePermissionResponse
	6, // 8: auth.PermissionService.ValidateCompanyAccess:output_type -> auth.ValidateAccessResponse
	6, // 9: auth.PermissionService.ValidateEnterpriseAccess:output_type -> auth.ValidateAccessResponse
	8, // 10: auth.PermissionService.ListUserAccessableCompaniesWithEnterprises:output_type -> auth.ListCompanyOrEnterpriseResponse
	8, // 11: auth.PermissionService.ListUserAccessableEnterprises:output_type -> auth.ListCompanyOrEnterpriseResponse
	6, // [6:12] is the sub-list for method output_type
	0, // [0:6] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_permission_service_proto_init() }
func file_proto_permission_service_proto_init() {
	if File_proto_permission_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_permission_service_proto_rawDesc), len(file_proto_permission_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_permission_service_proto_goTypes,
		DependencyIndexes: file_proto_permission_service_proto_depIdxs,
		MessageInfos:      file_proto_permission_service_proto_msgTypes,
	}.Build()
	File_proto_permission_service_proto = out.File
	file_proto_permission_service_proto_goTypes = nil
	file_proto_permission_service_proto_depIdxs = nil
}
